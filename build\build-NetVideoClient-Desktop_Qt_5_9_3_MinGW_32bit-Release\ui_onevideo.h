/********************************************************************************
** Form generated from reading UI file 'onevideo.ui'
**
** Created by: Qt User Interface Compiler version 5.9.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ONEVIDEO_H
#define UI_ONEVIDEO_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_OneVideo
{
public:
    QVBoxLayout *verticalLayout;

    void setupUi(QFrame *OneVideo)
    {
        if (OneVideo->objectName().isEmpty())
            OneVideo->setObjectName(QStringLiteral("OneVideo"));
        OneVideo->resize(427, 240);
        OneVideo->setStyleSheet(QStringLiteral("QFrame { background: transparent; margin: 3px; }"));
        OneVideo->setMouseTracking(true);
        verticalLayout = new QVBoxLayout(OneVideo);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);

        retranslateUi(OneVideo);

        QMetaObject::connectSlotsByName(OneVideo);
    } // setupUi

    void retranslateUi(QFrame *OneVideo)
    {
        Q_UNUSED(OneVideo);
    } // retranslateUi

};

namespace Ui {
    class OneVideo: public Ui_OneVideo {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ONEVIDEO_H
