#ifndef ONEVIDEO_H
#define ONEVIDEO_H

#include <QFrame>
#include <QImage>
#include <QTimer>
#include <QProcess>
#include <QtMultimedia/QMediaPlayer>
#include <functional>

class IconButton;
class MyThread;
class QLabel;

// 前向声明
namespace Ui
{
	class OneVideo;
}

class OneVideo : public QFrame
{
	Q_OBJECT
public:
	explicit OneVideo(QWidget *parent = 0);
	~OneVideo();
	void createToolButtons();
	void createIpPortLabel();
	void createTimeLabel();

	static const int WIDTH;
	static const int HEIGHT;

	bool isPlaying() const { return isPlay; }
	bool isVideoExist() const { return networkThread != NULL; }
	QImage getCurrentImage() const;

	void setIpPort(QString ip, int port);
	void setRtsp(QString rtsp);
	void stopNetwork();
	void startNetwork();
	void setDisconnectCallBack(std::function<void(OneVideo *)> func) { disconnectCallBack = func; }

	// 设置RTSP URL并自动连接
	void setRtspUrl(const QString &url);

	// 设置摄像头名称
	void setCameraName(const QString &name);

	// 新增：公开方法，供外部触发
	void takeScreenshot();
	void toggleRecording();
	void toggleListen();

private slots:
	void cameraBtnSlot();
	void recordBtnSlot();
	void fullScreenBtnSlot();
	void rotateImageBtnSlot();
	void muteButtonSlot();
	void updateImage(QImage image);
	void disconnectSlot();
	void updateTimeDisplay();
	void updateRecordingTime();	 // 更新录制时间的槽函数
	void updateLabelPositions(); // 更新所有标签位置

signals:
	void closeSignal(OneVideo *who);
	void disconnected();

protected:
	void closeEvent(QCloseEvent *event) Q_DECL_OVERRIDE;
	void paintEvent(QPaintEvent *event) Q_DECL_OVERRIDE;
	void mouseMoveEvent(QMouseEvent *event) Q_DECL_OVERRIDE;
	void enterEvent(QEvent *event) Q_DECL_OVERRIDE;
	void leaveEvent(QEvent *event) Q_DECL_OVERRIDE;
	void resizeEvent(QResizeEvent *event) Q_DECL_OVERRIDE;
	void contextMenuEvent(QContextMenuEvent *event) Q_DECL_OVERRIDE;

private:
	// 视频录制相关方法
	void startRecording();
	void stopRecording();
	void updateRecordingIndicator();

	// 窃听相关方法
	void startListening();
	void stopListening();

	// UI指针
	Ui::OneVideo *ui;

	// 已注释掉关闭按钮功能，但保留变量以避免编译错误
	IconButton *closeBtn;
	IconButton *playBtn;
	IconButton *cameraBtn;
	IconButton *recordBtn;
	IconButton *fullScreenBtn;
	IconButton *rotateImageBtn;
	IconButton *muteBtn;
	QLabel *ipPortLabel;
	QLabel *timeLabel;
	QLabel *nameLabel; // 新增摄像头名称标签

	// 显示的图像
	QImage centralImage;
	// 按钮工具栏显示状态
	bool isButtonsShow;
	// 是否正在运行
	bool isPlay;
	// 图像旋转角度 (0, 90, 180, 270)
	int rotationAngle;
	// 是否静音
	bool isMuted;

	// 网络线程
	MyThread *networkThread;

	// 存储RTSP URL
	QString rtspUrl;

	// 视频录制相关
	bool isRecording;			// 是否正在录制
	QString recordingFilePath;	// 录制文件路径
	QTimer *recordingTimer;		// 录制计时器
	int recordingDuration;		// 录制持续时间(秒)
	QLabel *recordingIndicator; // 录制指示器

	// 窃听相关
	bool isListening;		   // 是否正在窃听（音频播放）
	QMediaPlayer *audioPlayer; // HTTP音频播放器

	// 新增变量
	QString ip;
	int port;
	QString rtsp;
	bool isRtspMode;
	QString cameraName; // 摄像头名称
	std::function<void(OneVideo *)> disconnectCallBack;
	QProcess *ffmpegProcess; // 用于启动ffmpeg进程进行录制
};

#endif // ONEVIDEO_H
