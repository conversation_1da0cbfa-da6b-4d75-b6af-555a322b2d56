﻿#include "configdialog.h"
#include "ui_configdialog.h"
#include "configmanager.h"

ConfigDialog::ConfigDialog(QWidget *parent) : QDialog(parent),
											  ui(new Ui::ConfigDialog)
{
	ui->setupUi(this);

	// 加载配置
	loadSettings();
}

ConfigDialog::~ConfigDialog()
{
	delete ui;
}

void ConfigDialog::loadSettings()
{
	// 从配置管理器获取设置
	ConfigManager &config = ConfigManager::instance();

	// 扫描设置
	ui->ipPrefixEdit->setText(config.getIpPrefix());
	ui->startIpBox->setValue(config.getStartIp());
	ui->endIpBox->setValue(config.getEndIp());

	// 认证设置 - 显示宏定义的值，但设置为只读
	// ui->usernameEdit->setText(config.getUsername());
	// ui->passwordEdit->setText(config.getPassword());
	ui->streamPathEdit->setText(config.getStreamPath());
	ui->usernameEdit->setReadOnly(true);
	ui->passwordEdit->setReadOnly(true);
	ui->streamPathEdit->setReadOnly(true);
	ui->usernameEdit->setStyleSheet("QLineEdit { background-color: #F0F0F0; }");
	ui->passwordEdit->setStyleSheet("QLineEdit { background-color: #F0F0F0; }");
	ui->streamPathEdit->setStyleSheet("QLineEdit { background-color: #F0F0F0; }");

	// 超时设置
	ui->httpTimeoutBox->setValue(config.getHttpTimeout());

	// 显示设置
	ui->autoDisplayCheck->setChecked(config.getAutoDisplay());

	// 默认分屏设置
	int defaultMode = config.getDefaultDisplayMode();
	int comboIndex = 1; // 默认选择4分屏（索引1）

	if (defaultMode == 1)
	{
		comboIndex = 0;
	}
	else if (defaultMode == 4)
	{
		comboIndex = 1;
	}
	else if (defaultMode == 9)
	{
		comboIndex = 2;
	}

	ui->defaultModeComboBox->setCurrentIndex(comboIndex);
}

void ConfigDialog::saveSettings()
{
	// 获取配置管理器
	ConfigManager &config = ConfigManager::instance();

	// 扫描设置
	config.setIpPrefix(ui->ipPrefixEdit->text());
	config.setStartIp(ui->startIpBox->value());
	config.setEndIp(ui->endIpBox->value());

	// 认证设置 - 不再从界面获取值
	// 这些值现在由宏定义控制
	// config.setUsername(ui->usernameEdit->text());
	// config.setPassword(ui->passwordEdit->text());
	// config.setStreamPath(ui->streamPathEdit->text());

	// 超时设置
	config.setHttpTimeout(ui->httpTimeoutBox->value());

	// 显示设置
	config.setAutoDisplay(ui->autoDisplayCheck->isChecked());

	// 默认分屏设置
	int displayMode = 4; // 默认为4分屏
	int comboIndex = ui->defaultModeComboBox->currentIndex();

	if (comboIndex == 0)
	{
		displayMode = 1;
	}
	else if (comboIndex == 1)
	{
		displayMode = 4;
	}
	else if (comboIndex == 2)
	{
		displayMode = 9;
	}

	config.setDefaultDisplayMode(displayMode);

	// 保存到文件
	config.saveSettings();
}

QString ConfigDialog::getIP() const
{
	// 兼容旧接口，返回空值
	return QString();
}

int ConfigDialog::getPort() const
{
	// 兼容旧接口，返回0
	return 0;
}

QString ConfigDialog::getRtspUrl() const
{
	// 兼容旧接口，返回空值
	return QString();
}

bool ConfigDialog::isRtspMode() const
{
	// 兼容旧接口，返回true
	return true;
}

void ConfigDialog::onModeChanged(int index)
{
	// 空实现，保留函数以解决链接错误
	Q_UNUSED(index);
}

void ConfigDialog::accept()
{
	// 保存设置
	saveSettings();

	// 调用父类方法关闭对话框
	QDialog::accept();
}
