﻿#ifndef CONFIGDIALOG_H
#define CONFIGDIALOG_H

#include <QDialog>

namespace Ui
{
	class ConfigDialog;
}

class ConfigDialog : public QDialog
{
	Q_OBJECT

public:
	explicit ConfigDialog(QWidget *parent = 0);
	~ConfigDialog();

	QString getIP() const;
	int getPort() const;
	QString getRtspUrl() const;
	bool isRtspMode() const;

private:
	Ui::ConfigDialog *ui;

	void loadSettings();
	void saveSettings();

public slots:
	void accept() Q_DECL_OVERRIDE;
	void onModeChanged(int index);
};

#endif // CONFIGDIALOG_H
