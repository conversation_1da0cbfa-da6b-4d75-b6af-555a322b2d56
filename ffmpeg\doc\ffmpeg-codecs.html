<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 6.8, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Codecs Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Codecs Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<div class="Contents_element" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Codec-Options" href="#Codec-Options">2 Codec Options</a></li>
  <li><a id="toc-Decoders" href="#Decoders">3 Decoders</a></li>
  <li><a id="toc-Video-Decoders" href="#Video-Decoders">4 Video Decoders</a>
  <ul class="no-bullet">
    <li><a id="toc-av1" href="#av1">4.1 av1</a>
    <ul class="no-bullet">
      <li><a id="toc-Options" href="#Options">4.1.1 Options</a></li>
    </ul></li>
    <li><a id="toc-rawvideo" href="#rawvideo">4.2 rawvideo</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-1" href="#Options-1">4.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libdav1d" href="#libdav1d">4.3 libdav1d</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-2" href="#Options-2">4.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libdavs2" href="#libdavs2">4.4 libdavs2</a></li>
    <li><a id="toc-libuavs3d" href="#libuavs3d">4.5 libuavs3d</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-3" href="#Options-3">4.5.1 Options</a></li>
    </ul></li>
    <li><a id="toc-QSV-Decoders" href="#QSV-Decoders">4.6 QSV Decoders</a>
    <ul class="no-bullet">
      <li><a id="toc-Common-Options" href="#Common-Options">4.6.1 Common Options</a></li>
      <li><a id="toc-HEVC-Options" href="#HEVC-Options">4.6.2 HEVC Options</a></li>
    </ul></li>
    <li><a id="toc-v210" href="#v210">4.7 v210</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-4" href="#Options-4">4.7.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Audio-Decoders" href="#Audio-Decoders">5 Audio Decoders</a>
  <ul class="no-bullet">
    <li><a id="toc-ac3" href="#ac3">5.1 ac3</a>
    <ul class="no-bullet">
      <li><a id="toc-AC_002d3-Decoder-Options" href="#AC_002d3-Decoder-Options">5.1.1 AC-3 Decoder Options</a></li>
    </ul></li>
    <li><a id="toc-flac-1" href="#flac-1">5.2 flac</a>
    <ul class="no-bullet">
      <li><a id="toc-FLAC-Decoder-options" href="#FLAC-Decoder-options">5.2.1 FLAC Decoder options</a></li>
    </ul></li>
    <li><a id="toc-ffwavesynth" href="#ffwavesynth">5.3 ffwavesynth</a></li>
    <li><a id="toc-libcelt" href="#libcelt">5.4 libcelt</a></li>
    <li><a id="toc-libgsm" href="#libgsm">5.5 libgsm</a></li>
    <li><a id="toc-libilbc" href="#libilbc">5.6 libilbc</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-5" href="#Options-5">5.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopencore_002damrnb" href="#libopencore_002damrnb">5.7 libopencore-amrnb</a></li>
    <li><a id="toc-libopencore_002damrwb" href="#libopencore_002damrwb">5.8 libopencore-amrwb</a></li>
    <li><a id="toc-libopus" href="#libopus">5.9 libopus</a></li>
  </ul></li>
  <li><a id="toc-Subtitles-Decoders" href="#Subtitles-Decoders">6 Subtitles Decoders</a>
  <ul class="no-bullet">
    <li><a id="toc-libaribb24" href="#libaribb24">6.1 libaribb24</a>
    <ul class="no-bullet">
      <li><a id="toc-libaribb24-Decoder-Options" href="#libaribb24-Decoder-Options">6.1.1 libaribb24 Decoder Options</a></li>
    </ul></li>
    <li><a id="toc-dvbsub" href="#dvbsub">6.2 dvbsub</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-6" href="#Options-6">6.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-dvdsub" href="#dvdsub">6.3 dvdsub</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-7" href="#Options-7">6.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libzvbi_002dteletext" href="#libzvbi_002dteletext">6.4 libzvbi-teletext</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-8" href="#Options-8">6.4.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Encoders" href="#Encoders">7 Encoders</a></li>
  <li><a id="toc-Audio-Encoders" href="#Audio-Encoders">8 Audio Encoders</a>
  <ul class="no-bullet">
    <li><a id="toc-aac" href="#aac">8.1 aac</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-9" href="#Options-9">8.1.1 Options</a></li>
    </ul></li>
    <li><a id="toc-ac3-and-ac3_005ffixed" href="#ac3-and-ac3_005ffixed">8.2 ac3 and ac3_fixed</a>
    <ul class="no-bullet">
      <li><a id="toc-AC_002d3-Metadata" href="#AC_002d3-Metadata">8.2.1 AC-3 Metadata</a>
      <ul class="no-bullet">
        <li><a id="toc-Metadata-Control-Options" href="#Metadata-Control-Options">8.2.1.1 Metadata Control Options</a></li>
        <li><a id="toc-Downmix-Levels" href="#Downmix-Levels">8.2.1.2 Downmix Levels</a></li>
        <li><a id="toc-Audio-Production-Information" href="#Audio-Production-Information">8.2.1.3 Audio Production Information</a></li>
        <li><a id="toc-Other-Metadata-Options" href="#Other-Metadata-Options">8.2.1.4 Other Metadata Options</a></li>
      </ul></li>
      <li><a id="toc-Extended-Bitstream-Information" href="#Extended-Bitstream-Information">8.2.2 Extended Bitstream Information</a>
      <ul class="no-bullet">
        <li><a id="toc-Extended-Bitstream-Information-_002d-Part-1" href="#Extended-Bitstream-Information-_002d-Part-1">8.2.2.1 Extended Bitstream Information - Part 1</a></li>
        <li><a id="toc-Extended-Bitstream-Information-_002d-Part-2" href="#Extended-Bitstream-Information-_002d-Part-2">8.2.2.2 Extended Bitstream Information - Part 2</a></li>
      </ul></li>
      <li><a id="toc-Other-AC_002d3-Encoding-Options" href="#Other-AC_002d3-Encoding-Options">8.2.3 Other AC-3 Encoding Options</a></li>
      <li><a id="toc-Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options" href="#Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options">8.2.4 Floating-Point-Only AC-3 Encoding Options</a></li>
    </ul></li>
    <li><a id="toc-flac-2" href="#flac-2">8.3 flac</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-10" href="#Options-10">8.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-opus" href="#opus">8.4 opus</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-11" href="#Options-11">8.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libfdk_005faac" href="#libfdk_005faac">8.5 libfdk_aac</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-12" href="#Options-12">8.5.1 Options</a></li>
      <li><a id="toc-Examples" href="#Examples">8.5.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-libmp3lame-1" href="#libmp3lame-1">8.6 libmp3lame</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-13" href="#Options-13">8.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopencore_002damrnb-1" href="#libopencore_002damrnb-1">8.7 libopencore-amrnb</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-14" href="#Options-14">8.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopus-1" href="#libopus-1">8.8 libopus</a>
    <ul class="no-bullet">
      <li><a id="toc-Option-Mapping" href="#Option-Mapping">8.8.1 Option Mapping</a></li>
    </ul></li>
    <li><a id="toc-libshine-1" href="#libshine-1">8.9 libshine</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-15" href="#Options-15">8.9.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libtwolame" href="#libtwolame">8.10 libtwolame</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-16" href="#Options-16">8.10.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libvo_002damrwbenc" href="#libvo_002damrwbenc">8.11 libvo-amrwbenc</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-17" href="#Options-17">8.11.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libvorbis" href="#libvorbis">8.12 libvorbis</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-18" href="#Options-18">8.12.1 Options</a></li>
    </ul></li>
    <li><a id="toc-mjpeg" href="#mjpeg">8.13 mjpeg</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-19" href="#Options-19">8.13.1 Options</a></li>
    </ul></li>
    <li><a id="toc-wavpack" href="#wavpack">8.14 wavpack</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-20" href="#Options-20">8.14.1 Options</a>
      <ul class="no-bullet">
        <li><a id="toc-Shared-options" href="#Shared-options">8.14.1.1 Shared options</a></li>
        <li><a id="toc-Private-options" href="#Private-options">8.14.1.2 Private options</a></li>
      </ul></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Video-Encoders" href="#Video-Encoders">9 Video Encoders</a>
  <ul class="no-bullet">
    <li><a id="toc-a64_005fmulti_002c-a64_005fmulti5" href="#a64_005fmulti_002c-a64_005fmulti5">9.1 a64_multi, a64_multi5</a></li>
    <li><a id="toc-Cinepak" href="#Cinepak">9.2 Cinepak</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-21" href="#Options-21">9.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-GIF" href="#GIF">9.3 GIF</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-22" href="#Options-22">9.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-Hap" href="#Hap">9.4 Hap</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-23" href="#Options-23">9.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-jpeg2000" href="#jpeg2000">9.5 jpeg2000</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-24" href="#Options-24">9.5.1 Options</a></li>
    </ul></li>
    <li><a id="toc-librav1e" href="#librav1e">9.6 librav1e</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-25" href="#Options-25">9.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libaom_002dav1" href="#libaom_002dav1">9.7 libaom-av1</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-26" href="#Options-26">9.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libsvtav1" href="#libsvtav1">9.8 libsvtav1</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-27" href="#Options-27">9.8.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libjxl" href="#libjxl">9.9 libjxl</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-28" href="#Options-28">9.9.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libkvazaar" href="#libkvazaar">9.10 libkvazaar</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-29" href="#Options-29">9.10.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libopenh264" href="#libopenh264">9.11 libopenh264</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-30" href="#Options-30">9.11.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libtheora" href="#libtheora">9.12 libtheora</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-31" href="#Options-31">9.12.1 Options</a></li>
      <li><a id="toc-Examples-1" href="#Examples-1">9.12.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-libvpx" href="#libvpx">9.13 libvpx</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-32" href="#Options-32">9.13.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libwebp" href="#libwebp">9.14 libwebp</a>
    <ul class="no-bullet">
      <li><a id="toc-Pixel-Format" href="#Pixel-Format">9.14.1 Pixel Format</a></li>
      <li><a id="toc-Options-33" href="#Options-33">9.14.2 Options</a></li>
    </ul></li>
    <li><a id="toc-libx264_002c-libx264rgb" href="#libx264_002c-libx264rgb">9.15 libx264, libx264rgb</a>
    <ul class="no-bullet">
      <li><a id="toc-Supported-Pixel-Formats" href="#Supported-Pixel-Formats">9.15.1 Supported Pixel Formats</a></li>
      <li><a id="toc-Options-34" href="#Options-34">9.15.2 Options</a></li>
    </ul></li>
    <li><a id="toc-libx265" href="#libx265">9.16 libx265</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-35" href="#Options-35">9.16.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxavs2" href="#libxavs2">9.17 libxavs2</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-36" href="#Options-36">9.17.1 Options</a></li>
    </ul></li>
    <li><a id="toc-libxvid" href="#libxvid">9.18 libxvid</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-37" href="#Options-37">9.18.1 Options</a></li>
    </ul></li>
    <li><a id="toc-MediaFoundation" href="#MediaFoundation">9.19 MediaFoundation</a></li>
    <li><a id="toc-mpeg2" href="#mpeg2">9.20 mpeg2</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-38" href="#Options-38">9.20.1 Options</a></li>
    </ul></li>
    <li><a id="toc-png" href="#png">9.21 png</a>
    <ul class="no-bullet">
      <li><a id="toc-Private-options-1" href="#Private-options-1">9.21.1 Private options</a></li>
    </ul></li>
    <li><a id="toc-ProRes" href="#ProRes">9.22 ProRes</a>
    <ul class="no-bullet">
      <li><a id="toc-Private-Options-for-prores_002dks" href="#Private-Options-for-prores_002dks">9.22.1 Private Options for prores-ks</a></li>
      <li><a id="toc-Speed-considerations" href="#Speed-considerations">9.22.2 Speed considerations</a></li>
    </ul></li>
    <li><a id="toc-QSV-Encoders" href="#QSV-Encoders">9.23 QSV Encoders</a>
    <ul class="no-bullet">
      <li><a id="toc-Ratecontrol-Method" href="#Ratecontrol-Method">9.23.1 Ratecontrol Method</a></li>
      <li><a id="toc-Global-Options-_002d_003e-MSDK-Options" href="#Global-Options-_002d_003e-MSDK-Options">9.23.2 Global Options -&gt; MSDK Options</a></li>
      <li><a id="toc-Common-Options-1" href="#Common-Options-1">9.23.3 Common Options</a></li>
      <li><a id="toc-Runtime-Options" href="#Runtime-Options">9.23.4 Runtime Options</a></li>
      <li><a id="toc-H264-options" href="#H264-options">9.23.5 H264 options</a></li>
      <li><a id="toc-HEVC-Options-1" href="#HEVC-Options-1">9.23.6 HEVC Options</a></li>
      <li><a id="toc-MPEG2-Options" href="#MPEG2-Options">9.23.7 MPEG2 Options</a></li>
      <li><a id="toc-VP9-Options" href="#VP9-Options">9.23.8 VP9 Options</a></li>
    </ul></li>
    <li><a id="toc-snow" href="#snow">9.24 snow</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-39" href="#Options-39">9.24.1 Options</a></li>
    </ul></li>
    <li><a id="toc-VAAPI-encoders" href="#VAAPI-encoders">9.25 VAAPI encoders</a></li>
    <li><a id="toc-vbn" href="#vbn">9.26 vbn</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-40" href="#Options-40">9.26.1 Options</a></li>
    </ul></li>
    <li><a id="toc-vc2" href="#vc2">9.27 vc2</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-41" href="#Options-41">9.27.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Subtitles-Encoders" href="#Subtitles-Encoders">10 Subtitles Encoders</a>
  <ul class="no-bullet">
    <li><a id="toc-dvdsub-1" href="#dvdsub-1">10.1 dvdsub</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-42" href="#Options-42">10.1.1 Options</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-See-Also" href="#See-Also">11 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">12 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the codecs (decoders and encoders) provided by
the libavcodec library.
</p>

<span id="codec_002doptions"></span><a name="Codec-Options"></a>
<h2 class="chapter">2 Codec Options<span class="pull-right"><a class="anchor hidden-xs" href="#Codec-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Codec-Options" aria-hidden="true">TOC</a></span></h2>

<p>libavcodec provides some generic global options, which can be set on
all the encoders and decoders. In addition each codec may support
so-called private options, which are specific for a given codec.
</p>
<p>Sometimes, a global option may only affect a specific kind of codec,
and may be nonsensical or ignored by another, so you need to be aware
of the meaning of the specified options. Also some options are
meant only for decoding or encoding.
</p>
<p>Options may be set by specifying -<var>option</var> <var>value</var> in the
FFmpeg tools, or by setting the value explicitly in the
<code>AVCodecContext</code> options or using the <samp>libavutil/opt.h</samp> API
for programmatic use.
</p>
<p>The list of supported options follow:
</p>
<dl compact="compact">
<dt><span><samp>b <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd><p>Set bitrate in bits/s. Default value is 200K.
</p>
</dd>
<dt><span><samp>ab <var>integer</var> (<em>encoding,audio</em>)</samp></span></dt>
<dd><p>Set audio bitrate (in bits/s). Default value is 128K.
</p>
</dd>
<dt><span><samp>bt <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set video bitrate tolerance (in bits/s). In 1-pass mode, bitrate
tolerance specifies how far ratecontrol is willing to deviate from the
target average bitrate value. This is not related to min/max
bitrate. Lowering tolerance too much has an adverse effect on quality.
</p>
</dd>
<dt><span><samp>flags <var>flags</var> (<em>decoding/encoding,audio,video,subtitles</em>)</samp></span></dt>
<dd><p>Set generic flags.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>mv4</samp>&rsquo;</span></dt>
<dd><p>Use four motion vector by macroblock (mpeg4).
</p></dd>
<dt><span>&lsquo;<samp>qpel</samp>&rsquo;</span></dt>
<dd><p>Use 1/4 pel motion compensation.
</p></dd>
<dt><span>&lsquo;<samp>loop</samp>&rsquo;</span></dt>
<dd><p>Use loop filter.
</p></dd>
<dt><span>&lsquo;<samp>qscale</samp>&rsquo;</span></dt>
<dd><p>Use fixed qscale.
</p></dd>
<dt><span>&lsquo;<samp>pass1</samp>&rsquo;</span></dt>
<dd><p>Use internal 2pass ratecontrol in first pass mode.
</p></dd>
<dt><span>&lsquo;<samp>pass2</samp>&rsquo;</span></dt>
<dd><p>Use internal 2pass ratecontrol in second pass mode.
</p></dd>
<dt><span>&lsquo;<samp>gray</samp>&rsquo;</span></dt>
<dd><p>Only decode/encode grayscale.
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>Set error[?] variables during encoding.
</p></dd>
<dt><span>&lsquo;<samp>truncated</samp>&rsquo;</span></dt>
<dd><p>Input bitstream might be randomly truncated.
</p></dd>
<dt><span>&lsquo;<samp>drop_changed</samp>&rsquo;</span></dt>
<dd><p>Don&rsquo;t output frames whose parameters differ from first decoded frame in stream.
Error AVERROR_INPUT_CHANGED is returned when a frame is dropped.
</p>
</dd>
<dt><span>&lsquo;<samp>ildct</samp>&rsquo;</span></dt>
<dd><p>Use interlaced DCT.
</p></dd>
<dt><span>&lsquo;<samp>low_delay</samp>&rsquo;</span></dt>
<dd><p>Force low delay.
</p></dd>
<dt><span>&lsquo;<samp>global_header</samp>&rsquo;</span></dt>
<dd><p>Place global headers in extradata instead of every keyframe.
</p></dd>
<dt><span>&lsquo;<samp>bitexact</samp>&rsquo;</span></dt>
<dd><p>Only write platform-, build- and time-independent data. (except (I)DCT).
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
</p></dd>
<dt><span>&lsquo;<samp>aic</samp>&rsquo;</span></dt>
<dd><p>Apply H263 advanced intra coding / mpeg4 ac prediction.
</p></dd>
<dt><span>&lsquo;<samp>ilme</samp>&rsquo;</span></dt>
<dd><p>Apply interlaced motion estimation.
</p></dd>
<dt><span>&lsquo;<samp>cgop</samp>&rsquo;</span></dt>
<dd><p>Use closed gop.
</p></dd>
<dt><span>&lsquo;<samp>output_corrupt</samp>&rsquo;</span></dt>
<dd><p>Output even potentially corrupted frames.
</p></dd>
</dl>

</dd>
<dt><span><samp>time_base <var>rational number</var></samp></span></dt>
<dd><p>Set codec time base.
</p>
<p>It is the fundamental unit of time (in seconds) in terms of which
frame timestamps are represented. For fixed-fps content, timebase
should be <code>1 / frame_rate</code> and timestamp increments should be
identically 1.
</p>
</dd>
<dt><span><samp>g <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set the group of picture (GOP) size. Default value is 12.
</p>
</dd>
<dt><span><samp>ar <var>integer</var> (<em>decoding/encoding,audio</em>)</samp></span></dt>
<dd><p>Set audio sampling rate (in Hz).
</p>
</dd>
<dt><span><samp>ac <var>integer</var> (<em>decoding/encoding,audio</em>)</samp></span></dt>
<dd><p>Set number of audio channels.
</p>
</dd>
<dt><span><samp>cutoff <var>integer</var> (<em>encoding,audio</em>)</samp></span></dt>
<dd><p>Set cutoff bandwidth. (Supported only by selected encoders, see
their respective documentation sections.)
</p>
</dd>
<dt><span><samp>frame_size <var>integer</var> (<em>encoding,audio</em>)</samp></span></dt>
<dd><p>Set audio frame size.
</p>
<p>Each submitted frame except the last must contain exactly frame_size
samples per channel. May be 0 when the codec has
CODEC_CAP_VARIABLE_FRAME_SIZE set, in that case the frame size is not
restricted. It is set by some decoders to indicate constant frame
size.
</p>
</dd>
<dt><span><samp>frame_number <var>integer</var></samp></span></dt>
<dd><p>Set the frame number.
</p>
</dd>
<dt><span><samp>delay <var>integer</var></samp></span></dt>
<dt><span><samp>qcomp <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set video quantizer scale compression (VBR). It is used as a constant
in the ratecontrol equation. Recommended range for default rc_eq:
0.0-1.0.
</p>
</dd>
<dt><span><samp>qblur <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set video quantizer scale blur (VBR).
</p>
</dd>
<dt><span><samp>qmin <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set min video quantizer scale (VBR). Must be included between -1 and
69, default value is 2.
</p>
</dd>
<dt><span><samp>qmax <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set max video quantizer scale (VBR). Must be included between -1 and
1024, default value is 31.
</p>
</dd>
<dt><span><samp>qdiff <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set max difference between the quantizer scale (VBR).
</p>
</dd>
<dt><span><samp>bf <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set max number of B frames between non-B-frames.
</p>
<p>Must be an integer between -1 and 16. 0 means that B-frames are
disabled. If a value of -1 is used, it will choose an automatic value
depending on the encoder.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><span><samp>b_qfactor <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set qp factor between P and B frames.
</p>
</dd>
<dt><span><samp>codec_tag <var>integer</var></samp></span></dt>
<dt><span><samp>bug <var>flags</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Workaround not auto detected encoder bugs.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>autodetect</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>xvid_ilace</samp>&rsquo;</span></dt>
<dd><p>Xvid interlacing bug (autodetected if fourcc==XVIX)
</p></dd>
<dt><span>&lsquo;<samp>ump4</samp>&rsquo;</span></dt>
<dd><p>(autodetected if fourcc==UMP4)
</p></dd>
<dt><span>&lsquo;<samp>no_padding</samp>&rsquo;</span></dt>
<dd><p>padding bug (autodetected)
</p></dd>
<dt><span>&lsquo;<samp>amv</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>qpel_chroma</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>std_qpel</samp>&rsquo;</span></dt>
<dd><p>old standard qpel (autodetected per fourcc/version)
</p></dd>
<dt><span>&lsquo;<samp>qpel_chroma2</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>direct_blocksize</samp>&rsquo;</span></dt>
<dd><p>direct-qpel-blocksize bug (autodetected per fourcc/version)
</p></dd>
<dt><span>&lsquo;<samp>edge</samp>&rsquo;</span></dt>
<dd><p>edge padding bug (autodetected per fourcc/version)
</p></dd>
<dt><span>&lsquo;<samp>hpel_chroma</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>dc_clip</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>ms</samp>&rsquo;</span></dt>
<dd><p>Workaround various bugs in microsoft broken decoders.
</p></dd>
<dt><span>&lsquo;<samp>trunc</samp>&rsquo;</span></dt>
<dd><p>trancated frames
</p></dd>
</dl>

</dd>
<dt><span><samp>strict <var>integer</var> (<em>decoding/encoding,audio,video</em>)</samp></span></dt>
<dd><p>Specify how strictly to follow the standards.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>very</samp>&rsquo;</span></dt>
<dd><p>strictly conform to an older more strict version of the spec or reference software
</p></dd>
<dt><span>&lsquo;<samp>strict</samp>&rsquo;</span></dt>
<dd><p>strictly conform to all the things in the spec no matter what consequences
</p></dd>
<dt><span>&lsquo;<samp>normal</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>unofficial</samp>&rsquo;</span></dt>
<dd><p>allow unofficial extensions
</p></dd>
<dt><span>&lsquo;<samp>experimental</samp>&rsquo;</span></dt>
<dd><p>allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
</p></dd>
</dl>

</dd>
<dt><span><samp>b_qoffset <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set QP offset between P and B frames.
</p>
</dd>
<dt><span><samp>err_detect <var>flags</var> (<em>decoding,audio,video</em>)</samp></span></dt>
<dd><p>Set error detection flags.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>crccheck</samp>&rsquo;</span></dt>
<dd><p>verify embedded CRCs
</p></dd>
<dt><span>&lsquo;<samp>bitstream</samp>&rsquo;</span></dt>
<dd><p>detect bitstream specification deviations
</p></dd>
<dt><span>&lsquo;<samp>buffer</samp>&rsquo;</span></dt>
<dd><p>detect improper bitstream length
</p></dd>
<dt><span>&lsquo;<samp>explode</samp>&rsquo;</span></dt>
<dd><p>abort decoding on minor error detection
</p></dd>
<dt><span>&lsquo;<samp>ignore_err</samp>&rsquo;</span></dt>
<dd><p>ignore decoding errors, and continue decoding.
This is useful if you want to analyze the content of a video and thus want
everything to be decoded no matter what. This option will not result in a video
that is pleasing to watch in case of errors.
</p></dd>
<dt><span>&lsquo;<samp>careful</samp>&rsquo;</span></dt>
<dd><p>consider things that violate the spec and have not been seen in the wild as errors
</p></dd>
<dt><span>&lsquo;<samp>compliant</samp>&rsquo;</span></dt>
<dd><p>consider all spec non compliancies as errors
</p></dd>
<dt><span>&lsquo;<samp>aggressive</samp>&rsquo;</span></dt>
<dd><p>consider things that a sane encoder should not do as an error
</p></dd>
</dl>

</dd>
<dt><span><samp>has_b_frames <var>integer</var></samp></span></dt>
<dt><span><samp>block_align <var>integer</var></samp></span></dt>
<dt><span><samp>rc_override_count <var>integer</var></samp></span></dt>
<dt><span><samp>maxrate <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd><p>Set max bitrate tolerance (in bits/s). Requires bufsize to be set.
</p>
</dd>
<dt><span><samp>minrate <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd><p>Set min bitrate tolerance (in bits/s). Most useful in setting up a CBR
encode. It is of little use elsewise.
</p>
</dd>
<dt><span><samp>bufsize <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd><p>Set ratecontrol buffer size (in bits).
</p>
</dd>
<dt><span><samp>i_qfactor <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set QP factor between P and I frames.
</p>
</dd>
<dt><span><samp>i_qoffset <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set QP offset between P and I frames.
</p>
</dd>
<dt><span><samp>dct <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set DCT algorithm.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>autoselect a good one (default)
</p></dd>
<dt><span>&lsquo;<samp>fastint</samp>&rsquo;</span></dt>
<dd><p>fast integer
</p></dd>
<dt><span>&lsquo;<samp>int</samp>&rsquo;</span></dt>
<dd><p>accurate integer
</p></dd>
<dt><span>&lsquo;<samp>mmx</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>altivec</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>faan</samp>&rsquo;</span></dt>
<dd><p>floating point AAN DCT
</p></dd>
</dl>

</dd>
<dt><span><samp>lumi_mask <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Compress bright areas stronger than medium ones.
</p>
</dd>
<dt><span><samp>tcplx_mask <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set temporal complexity masking.
</p>
</dd>
<dt><span><samp>scplx_mask <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set spatial complexity masking.
</p>
</dd>
<dt><span><samp>p_mask <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set inter masking.
</p>
</dd>
<dt><span><samp>dark_mask <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Compress dark areas stronger than medium ones.
</p>
</dd>
<dt><span><samp>idct <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Select IDCT implementation.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>int</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simple</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simplemmx</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simpleauto</samp>&rsquo;</span></dt>
<dd><p>Automatically pick a IDCT compatible with the simple one
</p>
</dd>
<dt><span>&lsquo;<samp>arm</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>altivec</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>sh4</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simplearm</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simplearmv5te</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simplearmv6</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simpleneon</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>xvid</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>faani</samp>&rsquo;</span></dt>
<dd><p>floating point AAN IDCT
</p></dd>
</dl>

</dd>
<dt><span><samp>slice_count <var>integer</var></samp></span></dt>
<dt><span><samp>ec <var>flags</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Set error concealment strategy.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>guess_mvs</samp>&rsquo;</span></dt>
<dd><p>iterative motion vector (MV) search (slow)
</p></dd>
<dt><span>&lsquo;<samp>deblock</samp>&rsquo;</span></dt>
<dd><p>use strong deblock filter for damaged MBs
</p></dd>
<dt><span>&lsquo;<samp>favor_inter</samp>&rsquo;</span></dt>
<dd><p>favor predicting from the previous frame instead of the current
</p></dd>
</dl>

</dd>
<dt><span><samp>bits_per_coded_sample <var>integer</var></samp></span></dt>
<dt><span><samp>aspect <var>rational number</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set sample aspect ratio.
</p>
</dd>
<dt><span><samp>sar <var>rational number</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set sample aspect ratio. Alias to <var>aspect</var>.
</p>
</dd>
<dt><span><samp>debug <var>flags</var> (<em>decoding/encoding,audio,video,subtitles</em>)</samp></span></dt>
<dd><p>Print specific debug info.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>pict</samp>&rsquo;</span></dt>
<dd><p>picture info
</p></dd>
<dt><span>&lsquo;<samp>rc</samp>&rsquo;</span></dt>
<dd><p>rate control
</p></dd>
<dt><span>&lsquo;<samp>bitstream</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>mb_type</samp>&rsquo;</span></dt>
<dd><p>macroblock (MB) type
</p></dd>
<dt><span>&lsquo;<samp>qp</samp>&rsquo;</span></dt>
<dd><p>per-block quantization parameter (QP)
</p></dd>
<dt><span>&lsquo;<samp>dct_coeff</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>green_metadata</samp>&rsquo;</span></dt>
<dd><p>display complexity metadata for the upcoming frame, GoP or for a given duration.
</p>
</dd>
<dt><span>&lsquo;<samp>skip</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>startcode</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>er</samp>&rsquo;</span></dt>
<dd><p>error recognition
</p></dd>
<dt><span>&lsquo;<samp>mmco</samp>&rsquo;</span></dt>
<dd><p>memory management control operations (H.264)
</p></dd>
<dt><span>&lsquo;<samp>bugs</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>buffers</samp>&rsquo;</span></dt>
<dd><p>picture buffer allocations
</p></dd>
<dt><span>&lsquo;<samp>thread_ops</samp>&rsquo;</span></dt>
<dd><p>threading operations
</p></dd>
<dt><span>&lsquo;<samp>nomc</samp>&rsquo;</span></dt>
<dd><p>skip motion compensation
</p></dd>
</dl>

</dd>
<dt><span><samp>cmp <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set full pel me compare function.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt><span>&lsquo;<samp>sse</samp>&rsquo;</span></dt>
<dd><p>sum of squared errors
</p></dd>
<dt><span>&lsquo;<samp>satd</samp>&rsquo;</span></dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt><span>&lsquo;<samp>dct</samp>&rsquo;</span></dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt><span>&lsquo;<samp>bit</samp>&rsquo;</span></dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>0
</p></dd>
<dt><span>&lsquo;<samp>vsad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt><span>&lsquo;<samp>vsse</samp>&rsquo;</span></dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt><span>&lsquo;<samp>nsse</samp>&rsquo;</span></dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt><span>&lsquo;<samp>w53</samp>&rsquo;</span></dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>w97</samp>&rsquo;</span></dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>dctmax</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>subcmp <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set sub pel me compare function.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt><span>&lsquo;<samp>sse</samp>&rsquo;</span></dt>
<dd><p>sum of squared errors
</p></dd>
<dt><span>&lsquo;<samp>satd</samp>&rsquo;</span></dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt><span>&lsquo;<samp>dct</samp>&rsquo;</span></dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt><span>&lsquo;<samp>bit</samp>&rsquo;</span></dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>0
</p></dd>
<dt><span>&lsquo;<samp>vsad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt><span>&lsquo;<samp>vsse</samp>&rsquo;</span></dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt><span>&lsquo;<samp>nsse</samp>&rsquo;</span></dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt><span>&lsquo;<samp>w53</samp>&rsquo;</span></dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>w97</samp>&rsquo;</span></dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>dctmax</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>mbcmp <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set macroblock compare function.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt><span>&lsquo;<samp>sse</samp>&rsquo;</span></dt>
<dd><p>sum of squared errors
</p></dd>
<dt><span>&lsquo;<samp>satd</samp>&rsquo;</span></dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt><span>&lsquo;<samp>dct</samp>&rsquo;</span></dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt><span>&lsquo;<samp>bit</samp>&rsquo;</span></dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>0
</p></dd>
<dt><span>&lsquo;<samp>vsad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt><span>&lsquo;<samp>vsse</samp>&rsquo;</span></dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt><span>&lsquo;<samp>nsse</samp>&rsquo;</span></dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt><span>&lsquo;<samp>w53</samp>&rsquo;</span></dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>w97</samp>&rsquo;</span></dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>dctmax</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>ildctcmp <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set interlaced dct compare function.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt><span>&lsquo;<samp>sse</samp>&rsquo;</span></dt>
<dd><p>sum of squared errors
</p></dd>
<dt><span>&lsquo;<samp>satd</samp>&rsquo;</span></dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt><span>&lsquo;<samp>dct</samp>&rsquo;</span></dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt><span>&lsquo;<samp>bit</samp>&rsquo;</span></dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>0
</p></dd>
<dt><span>&lsquo;<samp>vsad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt><span>&lsquo;<samp>vsse</samp>&rsquo;</span></dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt><span>&lsquo;<samp>nsse</samp>&rsquo;</span></dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt><span>&lsquo;<samp>w53</samp>&rsquo;</span></dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>w97</samp>&rsquo;</span></dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>dctmax</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>dia_size <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set diamond type &amp; size for motion estimation.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>(1024, INT_MAX)</samp>&rsquo;</span></dt>
<dd><p>full motion estimation(slowest)
</p></dd>
<dt><span>&lsquo;<samp>(768, 1024]</samp>&rsquo;</span></dt>
<dd><p>umh motion estimation
</p></dd>
<dt><span>&lsquo;<samp>(512, 768]</samp>&rsquo;</span></dt>
<dd><p>hex motion estimation
</p></dd>
<dt><span>&lsquo;<samp>(256, 512]</samp>&rsquo;</span></dt>
<dd><p>l2s diamond motion estimation
</p></dd>
<dt><span>&lsquo;<samp>[2,256]</samp>&rsquo;</span></dt>
<dd><p>var diamond motion estimation
</p></dd>
<dt><span>&lsquo;<samp>(-1,  2)</samp>&rsquo;</span></dt>
<dd><p>small diamond motion estimation
</p></dd>
<dt><span>&lsquo;<samp>-1</samp>&rsquo;</span></dt>
<dd><p>funny diamond motion estimation
</p></dd>
<dt><span>&lsquo;<samp>(INT_MIN, -1)</samp>&rsquo;</span></dt>
<dd><p>sab diamond motion estimation
</p></dd>
</dl>

</dd>
<dt><span><samp>last_pred <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set amount of motion predictors from the previous frame.
</p>
</dd>
<dt><span><samp>precmp <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set pre motion estimation compare function.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute differences, fast (default)
</p></dd>
<dt><span>&lsquo;<samp>sse</samp>&rsquo;</span></dt>
<dd><p>sum of squared errors
</p></dd>
<dt><span>&lsquo;<samp>satd</samp>&rsquo;</span></dt>
<dd><p>sum of absolute Hadamard transformed differences
</p></dd>
<dt><span>&lsquo;<samp>dct</samp>&rsquo;</span></dt>
<dd><p>sum of absolute DCT transformed differences
</p></dd>
<dt><span>&lsquo;<samp>psnr</samp>&rsquo;</span></dt>
<dd><p>sum of squared quantization errors (avoid, low quality)
</p></dd>
<dt><span>&lsquo;<samp>bit</samp>&rsquo;</span></dt>
<dd><p>number of bits needed for the block
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>rate distortion optimal, slow
</p></dd>
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>0
</p></dd>
<dt><span>&lsquo;<samp>vsad</samp>&rsquo;</span></dt>
<dd><p>sum of absolute vertical differences
</p></dd>
<dt><span>&lsquo;<samp>vsse</samp>&rsquo;</span></dt>
<dd><p>sum of squared vertical differences
</p></dd>
<dt><span>&lsquo;<samp>nsse</samp>&rsquo;</span></dt>
<dd><p>noise preserving sum of squared differences
</p></dd>
<dt><span>&lsquo;<samp>w53</samp>&rsquo;</span></dt>
<dd><p>5/3 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>w97</samp>&rsquo;</span></dt>
<dd><p>9/7 wavelet, only used in snow
</p></dd>
<dt><span>&lsquo;<samp>dctmax</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>pre_dia_size <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set diamond type &amp; size for motion estimation pre-pass.
</p>
</dd>
<dt><span><samp>subq <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set sub pel motion estimation quality.
</p>
</dd>
<dt><span><samp>me_range <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set limit motion vectors range (1023 for DivX player).
</p>
</dd>
<dt><span><samp>global_quality <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dt><span><samp>slice_flags <var>integer</var></samp></span></dt>
<dt><span><samp>mbd <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set macroblock decision algorithm (high quality mode).
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>simple</samp>&rsquo;</span></dt>
<dd><p>use mbcmp (default)
</p></dd>
<dt><span>&lsquo;<samp>bits</samp>&rsquo;</span></dt>
<dd><p>use fewest bits
</p></dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>use best rate distortion
</p></dd>
</dl>

</dd>
<dt><span><samp>rc_init_occupancy <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set number of bits which should be loaded into the rc buffer before
decoding starts.
</p>
</dd>
<dt><span><samp>flags2 <var>flags</var> (<em>decoding/encoding,audio,video,subtitles</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>fast</samp>&rsquo;</span></dt>
<dd><p>Allow non spec compliant speedup tricks.
</p></dd>
<dt><span>&lsquo;<samp>noout</samp>&rsquo;</span></dt>
<dd><p>Skip bitstream encoding.
</p></dd>
<dt><span>&lsquo;<samp>ignorecrop</samp>&rsquo;</span></dt>
<dd><p>Ignore cropping information from sps.
</p></dd>
<dt><span>&lsquo;<samp>local_header</samp>&rsquo;</span></dt>
<dd><p>Place global headers at every keyframe instead of in extradata.
</p></dd>
<dt><span>&lsquo;<samp>chunks</samp>&rsquo;</span></dt>
<dd><p>Frame data might be split into multiple chunks.
</p></dd>
<dt><span>&lsquo;<samp>showall</samp>&rsquo;</span></dt>
<dd><p>Show all frames before the first keyframe.
</p></dd>
<dt><span>&lsquo;<samp>export_mvs</samp>&rsquo;</span></dt>
<dd><p>Export motion vectors into frame side-data (see <code>AV_FRAME_DATA_MOTION_VECTORS</code>)
for codecs that support it. See also <samp>doc/examples/export_mvs.c</samp>.
</p></dd>
<dt><span>&lsquo;<samp>skip_manual</samp>&rsquo;</span></dt>
<dd><p>Do not skip samples and export skip information as frame side data.
</p></dd>
<dt><span>&lsquo;<samp>ass_ro_flush_noop</samp>&rsquo;</span></dt>
<dd><p>Do not reset ASS ReadOrder field on flush.
</p></dd>
</dl>

</dd>
<dt><span><samp>export_side_data <var>flags</var> (<em>decoding/encoding,audio,video,subtitles</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>mvs</samp>&rsquo;</span></dt>
<dd><p>Export motion vectors into frame side-data (see <code>AV_FRAME_DATA_MOTION_VECTORS</code>)
for codecs that support it. See also <samp>doc/examples/export_mvs.c</samp>.
</p></dd>
<dt><span>&lsquo;<samp>prft</samp>&rsquo;</span></dt>
<dd><p>Export encoder Producer Reference Time into packet side-data (see <code>AV_PKT_DATA_PRFT</code>)
for codecs that support it.
</p></dd>
<dt><span>&lsquo;<samp>venc_params</samp>&rsquo;</span></dt>
<dd><p>Export video encoding parameters through frame side data (see <code>AV_FRAME_DATA_VIDEO_ENC_PARAMS</code>)
for codecs that support it. At present, those are H.264 and VP9.
</p></dd>
<dt><span>&lsquo;<samp>film_grain</samp>&rsquo;</span></dt>
<dd><p>Export film grain parameters through frame side data (see <code>AV_FRAME_DATA_FILM_GRAIN_PARAMS</code>).
Supported at present by AV1 decoders.
</p></dd>
</dl>

</dd>
<dt><span><samp>threads <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Set the number of threads to be used, in case the selected codec
implementation supports multi-threading.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto, 0</samp>&rsquo;</span></dt>
<dd><p>automatically select the number of threads to set
</p></dd>
</dl>

<p>Default value is &lsquo;<samp>auto</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>dc <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set intra_dc_precision.
</p>
</dd>
<dt><span><samp>nssew <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set nsse weight.
</p>
</dd>
<dt><span><samp>skip_top <var>integer</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Set number of macroblock rows at the top which are skipped.
</p>
</dd>
<dt><span><samp>skip_bottom <var>integer</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Set number of macroblock rows at the bottom which are skipped.
</p>
</dd>
<dt><span><samp>profile <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd>
<p>Set encoder codec profile. Default value is &lsquo;<samp>unknown</samp>&rsquo;. Encoder specific
profiles are documented in the relevant encoder documentation.
</p>
</dd>
<dt><span><samp>level <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>lowres <var>integer</var> (<em>decoding,audio,video</em>)</samp></span></dt>
<dd><p>Decode at 1= 1/2, 2=1/4, 3=1/8 resolutions.
</p>
</dd>
<dt><span><samp>mblmin <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set min macroblock lagrange factor (VBR).
</p>
</dd>
<dt><span><samp>mblmax <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set max macroblock lagrange factor (VBR).
</p>
</dd>
<dt><span><samp>skip_loop_filter <var>integer</var> (<em>decoding,video</em>)</samp></span></dt>
<dt><span><samp>skip_idct        <var>integer</var> (<em>decoding,video</em>)</samp></span></dt>
<dt><span><samp>skip_frame       <var>integer</var> (<em>decoding,video</em>)</samp></span></dt>
<dd>
<p>Make decoder discard processing depending on the frame type selected
by the option value.
</p>
<p><samp>skip_loop_filter</samp> skips frame loop filtering, <samp>skip_idct</samp>
skips frame IDCT/dequantization, <samp>skip_frame</samp> skips decoding.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dd><p>Discard no frame.
</p>
</dd>
<dt><span>&lsquo;<samp>default</samp>&rsquo;</span></dt>
<dd><p>Discard useless frames like 0-sized frames.
</p>
</dd>
<dt><span>&lsquo;<samp>noref</samp>&rsquo;</span></dt>
<dd><p>Discard all non-reference frames.
</p>
</dd>
<dt><span>&lsquo;<samp>bidir</samp>&rsquo;</span></dt>
<dd><p>Discard all bidirectional frames.
</p>
</dd>
<dt><span>&lsquo;<samp>nokey</samp>&rsquo;</span></dt>
<dd><p>Discard all frames excepts keyframes.
</p>
</dd>
<dt><span>&lsquo;<samp>nointra</samp>&rsquo;</span></dt>
<dd><p>Discard all frames except I frames.
</p>
</dd>
<dt><span>&lsquo;<samp>all</samp>&rsquo;</span></dt>
<dd><p>Discard all frames.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp>default</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>bidir_refine <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Refine the two motion vectors used in bidirectional macroblocks.
</p>
</dd>
<dt><span><samp>keyint_min <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set minimum interval between IDR-frames.
</p>
</dd>
<dt><span><samp>refs <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Set reference frames to consider for motion compensation.
</p>
</dd>
<dt><span><samp>trellis <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dd><p>Set rate-distortion optimal quantization.
</p>
</dd>
<dt><span><samp>mv0_threshold <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dt><span><samp>compression_level <var>integer</var> (<em>encoding,audio,video</em>)</samp></span></dt>
<dt><span><samp>bits_per_raw_sample <var>integer</var></samp></span></dt>
<dt><span><samp>channel_layout <var>integer</var> (<em>decoding/encoding,audio</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p></dd>
<dt><span><samp>request_channel_layout <var>integer</var> (<em>decoding,audio</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p></dd>
<dt><span><samp>rc_max_vbv_use <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dt><span><samp>rc_min_vbv_use <var>float</var> (<em>encoding,video</em>)</samp></span></dt>
<dt><span><samp>ticks_per_frame <var>integer</var> (<em>decoding/encoding,audio,video</em>)</samp></span></dt>
<dt><span><samp>color_primaries <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dd><p>BT.709
</p></dd>
<dt><span>&lsquo;<samp>bt470m</samp>&rsquo;</span></dt>
<dd><p>BT.470 M
</p></dd>
<dt><span>&lsquo;<samp>bt470bg</samp>&rsquo;</span></dt>
<dd><p>BT.470 BG
</p></dd>
<dt><span>&lsquo;<samp>smpte170m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt><span>&lsquo;<samp>smpte240m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt><span>&lsquo;<samp>film</samp>&rsquo;</span></dt>
<dd><p>Film
</p></dd>
<dt><span>&lsquo;<samp>bt2020</samp>&rsquo;</span></dt>
<dd><p>BT.2020
</p></dd>
<dt><span>&lsquo;<samp>smpte428</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte428_1</samp>&rsquo;</span></dt>
<dd><p>SMPTE ST 428-1
</p></dd>
<dt><span>&lsquo;<samp>smpte431</samp>&rsquo;</span></dt>
<dd><p>SMPTE 431-2
</p></dd>
<dt><span>&lsquo;<samp>smpte432</samp>&rsquo;</span></dt>
<dd><p>SMPTE 432-1
</p></dd>
<dt><span>&lsquo;<samp>jedec-p22</samp>&rsquo;</span></dt>
<dd><p>JEDEC P22
</p></dd>
</dl>

</dd>
<dt><span><samp>color_trc <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dd><p>BT.709
</p></dd>
<dt><span>&lsquo;<samp>gamma22</samp>&rsquo;</span></dt>
<dd><p>BT.470 M
</p></dd>
<dt><span>&lsquo;<samp>gamma28</samp>&rsquo;</span></dt>
<dd><p>BT.470 BG
</p></dd>
<dt><span>&lsquo;<samp>smpte170m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt><span>&lsquo;<samp>smpte240m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt><span>&lsquo;<samp>linear</samp>&rsquo;</span></dt>
<dd><p>Linear
</p></dd>
<dt><span>&lsquo;<samp>log</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>log100</samp>&rsquo;</span></dt>
<dd><p>Log
</p></dd>
<dt><span>&lsquo;<samp>log_sqrt</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>log316</samp>&rsquo;</span></dt>
<dd><p>Log square root
</p></dd>
<dt><span>&lsquo;<samp>iec61966_2_4</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>iec61966-2-4</samp>&rsquo;</span></dt>
<dd><p>IEC 61966-2-4
</p></dd>
<dt><span>&lsquo;<samp>bt1361</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt1361e</samp>&rsquo;</span></dt>
<dd><p>BT.1361
</p></dd>
<dt><span>&lsquo;<samp>iec61966_2_1</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>iec61966-2-1</samp>&rsquo;</span></dt>
<dd><p>IEC 61966-2-1
</p></dd>
<dt><span>&lsquo;<samp>bt2020_10</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt2020_10bit</samp>&rsquo;</span></dt>
<dd><p>BT.2020 - 10 bit
</p></dd>
<dt><span>&lsquo;<samp>bt2020_12</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt2020_12bit</samp>&rsquo;</span></dt>
<dd><p>BT.2020 - 12 bit
</p></dd>
<dt><span>&lsquo;<samp>smpte2084</samp>&rsquo;</span></dt>
<dd><p>SMPTE ST 2084
</p></dd>
<dt><span>&lsquo;<samp>smpte428</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte428_1</samp>&rsquo;</span></dt>
<dd><p>SMPTE ST 428-1
</p></dd>
<dt><span>&lsquo;<samp>arib-std-b67</samp>&rsquo;</span></dt>
<dd><p>ARIB STD-B67
</p></dd>
</dl>

</dd>
<dt><span><samp>colorspace <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>rgb</samp>&rsquo;</span></dt>
<dd><p>RGB
</p></dd>
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dd><p>BT.709
</p></dd>
<dt><span>&lsquo;<samp>fcc</samp>&rsquo;</span></dt>
<dd><p>FCC
</p></dd>
<dt><span>&lsquo;<samp>bt470bg</samp>&rsquo;</span></dt>
<dd><p>BT.470 BG
</p></dd>
<dt><span>&lsquo;<samp>smpte170m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 170 M
</p></dd>
<dt><span>&lsquo;<samp>smpte240m</samp>&rsquo;</span></dt>
<dd><p>SMPTE 240 M
</p></dd>
<dt><span>&lsquo;<samp>ycocg</samp>&rsquo;</span></dt>
<dd><p>YCOCG
</p></dd>
<dt><span>&lsquo;<samp>bt2020nc</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt2020_ncl</samp>&rsquo;</span></dt>
<dd><p>BT.2020 NCL
</p></dd>
<dt><span>&lsquo;<samp>bt2020c</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt2020_cl</samp>&rsquo;</span></dt>
<dd><p>BT.2020 CL
</p></dd>
<dt><span>&lsquo;<samp>smpte2085</samp>&rsquo;</span></dt>
<dd><p>SMPTE 2085
</p></dd>
<dt><span>&lsquo;<samp>chroma-derived-nc</samp>&rsquo;</span></dt>
<dd><p>Chroma-derived NCL
</p></dd>
<dt><span>&lsquo;<samp>chroma-derived-c</samp>&rsquo;</span></dt>
<dd><p>Chroma-derived CL
</p></dd>
<dt><span>&lsquo;<samp>ictcp</samp>&rsquo;</span></dt>
<dd><p>ICtCp
</p></dd>
</dl>

</dd>
<dt><span><samp>color_range <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>If used as input parameter, it serves as a hint to the decoder, which
color_range the input has.
Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>tv</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>mpeg</samp>&rsquo;</span></dt>
<dd><p>MPEG (219*2^(n-8))
</p></dd>
<dt><span>&lsquo;<samp>pc</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>jpeg</samp>&rsquo;</span></dt>
<dd><p>JPEG (2^n-1)
</p></dd>
</dl>

</dd>
<dt><span><samp>chroma_sample_location <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>left</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>center</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>topleft</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>top</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bottomleft</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bottom</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>log_level_offset <var>integer</var></samp></span></dt>
<dd><p>Set the log level offset.
</p>
</dd>
<dt><span><samp>slices <var>integer</var> (<em>encoding,video</em>)</samp></span></dt>
<dd><p>Number of slices, used in parallelized encoding.
</p>
</dd>
<dt><span><samp>thread_type <var>flags</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Select which multithreading methods to use.
</p>
<p>Use of &lsquo;<samp>frame</samp>&rsquo; will increase decoding delay by one frame per
thread, so clients which cannot provide future frames should not use
it.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>slice</samp>&rsquo;</span></dt>
<dd><p>Decode more than one part of a single frame at once.
</p>
<p>Multithreading using slices works only when the video was encoded with
slices.
</p>
</dd>
<dt><span>&lsquo;<samp>frame</samp>&rsquo;</span></dt>
<dd><p>Decode more than one frame at once.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp>slice+frame</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>audio_service_type <var>integer</var> (<em>encoding,audio</em>)</samp></span></dt>
<dd><p>Set audio service type.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>ma</samp>&rsquo;</span></dt>
<dd><p>Main Audio Service
</p></dd>
<dt><span>&lsquo;<samp>ef</samp>&rsquo;</span></dt>
<dd><p>Effects
</p></dd>
<dt><span>&lsquo;<samp>vi</samp>&rsquo;</span></dt>
<dd><p>Visually Impaired
</p></dd>
<dt><span>&lsquo;<samp>hi</samp>&rsquo;</span></dt>
<dd><p>Hearing Impaired
</p></dd>
<dt><span>&lsquo;<samp>di</samp>&rsquo;</span></dt>
<dd><p>Dialogue
</p></dd>
<dt><span>&lsquo;<samp>co</samp>&rsquo;</span></dt>
<dd><p>Commentary
</p></dd>
<dt><span>&lsquo;<samp>em</samp>&rsquo;</span></dt>
<dd><p>Emergency
</p></dd>
<dt><span>&lsquo;<samp>vo</samp>&rsquo;</span></dt>
<dd><p>Voice Over
</p></dd>
<dt><span>&lsquo;<samp>ka</samp>&rsquo;</span></dt>
<dd><p>Karaoke
</p></dd>
</dl>

</dd>
<dt><span><samp>request_sample_fmt <var>sample_fmt</var> (<em>decoding,audio</em>)</samp></span></dt>
<dd><p>Set sample format audio decoders should prefer. Default value is
<code>none</code>.
</p>
</dd>
<dt><span><samp>pkt_timebase <var>rational number</var></samp></span></dt>
<dt><span><samp>sub_charenc <var>encoding</var> (<em>decoding,subtitles</em>)</samp></span></dt>
<dd><p>Set the input subtitles character encoding.
</p>
</dd>
<dt><span><samp>field_order  <var>field_order</var> (<em>video</em>)</samp></span></dt>
<dd><p>Set/override the field order of the video.
Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>progressive</samp>&rsquo;</span></dt>
<dd><p>Progressive video
</p></dd>
<dt><span>&lsquo;<samp>tt</samp>&rsquo;</span></dt>
<dd><p>Interlaced video, top field coded and displayed first
</p></dd>
<dt><span>&lsquo;<samp>bb</samp>&rsquo;</span></dt>
<dd><p>Interlaced video, bottom field coded and displayed first
</p></dd>
<dt><span>&lsquo;<samp>tb</samp>&rsquo;</span></dt>
<dd><p>Interlaced video, top coded first, bottom displayed first
</p></dd>
<dt><span>&lsquo;<samp>bt</samp>&rsquo;</span></dt>
<dd><p>Interlaced video, bottom coded first, top displayed first
</p></dd>
</dl>

</dd>
<dt><span><samp>skip_alpha <var>bool</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Set to 1 to disable processing alpha (transparency). This works like the
&lsquo;<samp>gray</samp>&rsquo; flag in the <samp>flags</samp> option which skips chroma information
instead of alpha. Default is 0.
</p>
</dd>
<dt><span><samp>codec_whitelist <var>list</var> (<em>input</em>)</samp></span></dt>
<dd><p>&quot;,&quot; separated list of allowed decoders. By default all are allowed.
</p>
</dd>
<dt><span><samp>dump_separator <var>string</var> (<em>input</em>)</samp></span></dt>
<dd><p>Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
</p><div class="example">
<pre class="example">ffprobe -dump_separator &quot;
                          &quot;  -i ~/videos/matrixbench_mpeg2.mpg
</pre></div>

</dd>
<dt><span><samp>max_pixels <var>integer</var> (<em>decoding/encoding,video</em>)</samp></span></dt>
<dd><p>Maximum number of pixels per image. This value can be used to avoid out of
memory failures due to large images.
</p>
</dd>
<dt><span><samp>apply_cropping <var>bool</var> (<em>decoding,video</em>)</samp></span></dt>
<dd><p>Enable cropping if cropping parameters are multiples of the required
alignment for the left and top parameters. If the alignment is not met the
cropping will be partially applied to maintain alignment.
Default is 1 (enabled).
Note: The required alignment depends on if <code>AV_CODEC_FLAG_UNALIGNED</code> is set and the
CPU. <code>AV_CODEC_FLAG_UNALIGNED</code> cannot be changed from the command line. Also hardware
decoders will not apply left/top Cropping.
</p>

</dd>
</dl>


<a name="Decoders"></a>
<h2 class="chapter">3 Decoders<span class="pull-right"><a class="anchor hidden-xs" href="#Decoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Decoders" aria-hidden="true">TOC</a></span></h2>

<p>Decoders are configured elements in FFmpeg which allow the decoding of
multimedia streams.
</p>
<p>When you configure your FFmpeg build, all the supported native decoders
are enabled by default. Decoders requiring an external library must be enabled
manually via the corresponding <code>--enable-lib</code> option. You can list all
available decoders using the configure option <code>--list-decoders</code>.
</p>
<p>You can disable all the decoders with the configure option
<code>--disable-decoders</code> and selectively enable / disable single decoders
with the options <code>--enable-decoder=<var>DECODER</var></code> /
<code>--disable-decoder=<var>DECODER</var></code>.
</p>
<p>The option <code>-decoders</code> of the ff* tools will display the list of
enabled decoders.
</p>

<a name="Video-Decoders"></a>
<h2 class="chapter">4 Video Decoders<span class="pull-right"><a class="anchor hidden-xs" href="#Video-Decoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Video-Decoders" aria-hidden="true">TOC</a></span></h2>

<p>A description of some of the currently available video decoders
follows.
</p>
<a name="av1"></a>
<h3 class="section">4.1 av1<span class="pull-right"><a class="anchor hidden-xs" href="#av1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-av1" aria-hidden="true">TOC</a></span></h3>

<p>AOMedia Video 1 (AV1) decoder.
</p>
<a name="Options"></a>
<h4 class="subsection">4.1.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>operating_point</samp></span></dt>
<dd><p>Select an operating point of a scalable AV1 bitstream (0 - 31). Default is 0.
</p>
</dd>
</dl>

<a name="rawvideo"></a>
<h3 class="section">4.2 rawvideo<span class="pull-right"><a class="anchor hidden-xs" href="#rawvideo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rawvideo" aria-hidden="true">TOC</a></span></h3>

<p>Raw video decoder.
</p>
<p>This decoder decodes rawvideo streams.
</p>
<a name="Options-1"></a>
<h4 class="subsection">4.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-1" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>top <var>top_field_first</var></samp></span></dt>
<dd><p>Specify the assumed field type of the input video.
</p><dl compact="compact">
<dt><span><samp>-1</samp></span></dt>
<dd><p>the video is assumed to be progressive (default)
</p></dd>
<dt><span><samp>0</samp></span></dt>
<dd><p>bottom-field-first is assumed
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dd><p>top-field-first is assumed
</p></dd>
</dl>

</dd>
</dl>

<a name="libdav1d"></a>
<h3 class="section">4.3 libdav1d<span class="pull-right"><a class="anchor hidden-xs" href="#libdav1d" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libdav1d" aria-hidden="true">TOC</a></span></h3>

<p>dav1d AV1 decoder.
</p>
<p>libdav1d allows libavcodec to decode the AOMedia Video 1 (AV1) codec.
Requires the presence of the libdav1d headers and library during configuration.
You need to explicitly configure the build with <code>--enable-libdav1d</code>.
</p>
<a name="Options-2"></a>
<h4 class="subsection">4.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-2" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libdav1d wrapper.
</p>
<dl compact="compact">
<dt><span><samp>framethreads</samp></span></dt>
<dd><p>Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d &gt;= 1.0 and will be removed in the future. Use the
global option <code>threads</code> instead.
</p>
</dd>
<dt><span><samp>tilethreads</samp></span></dt>
<dd><p>Set amount of tile threads to use during decoding. The default value is 0 (autodetect).
This option is deprecated for libdav1d &gt;= 1.0 and will be removed in the future. Use the
global option <code>threads</code> instead.
</p>
</dd>
<dt><span><samp>filmgrain</samp></span></dt>
<dd><p>Apply film grain to the decoded video if present in the bitstream. Defaults to the
internal default of the library.
This option is deprecated and will be removed in the future. See the global option
<code>export_side_data</code> to export Film Grain parameters instead of applying it.
</p>
</dd>
<dt><span><samp>oppoint</samp></span></dt>
<dd><p>Select an operating point of a scalable AV1 bitstream (0 - 31). Defaults to the
internal default of the library.
</p>
</dd>
<dt><span><samp>alllayers</samp></span></dt>
<dd><p>Output all spatial layers of a scalable AV1 bitstream. The default value is false.
</p>
</dd>
</dl>

<a name="libdavs2"></a>
<h3 class="section">4.4 libdavs2<span class="pull-right"><a class="anchor hidden-xs" href="#libdavs2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libdavs2" aria-hidden="true">TOC</a></span></h3>

<p>AVS2-P2/IEEE1857.4 video decoder wrapper.
</p>
<p>This decoder allows libavcodec to decode AVS2 streams with davs2 library.
</p>

<a name="libuavs3d"></a>
<h3 class="section">4.5 libuavs3d<span class="pull-right"><a class="anchor hidden-xs" href="#libuavs3d" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libuavs3d" aria-hidden="true">TOC</a></span></h3>

<p>AVS3-P2/IEEE1857.10 video decoder.
</p>
<p>libuavs3d allows libavcodec to decode AVS3 streams.
Requires the presence of the libuavs3d headers and library during configuration.
You need to explicitly configure the build with <code>--enable-libuavs3d</code>.
</p>
<a name="Options-3"></a>
<h4 class="subsection">4.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-3" aria-hidden="true">TOC</a></span></h4>

<p>The following option is supported by the libuavs3d wrapper.
</p>
<dl compact="compact">
<dt><span><samp>frame_threads</samp></span></dt>
<dd><p>Set amount of frame threads to use during decoding. The default value is 0 (autodetect).
</p>
</dd>
</dl>

<a name="QSV-Decoders"></a>
<h3 class="section">4.6 QSV Decoders<span class="pull-right"><a class="anchor hidden-xs" href="#QSV-Decoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-QSV-Decoders" aria-hidden="true">TOC</a></span></h3>

<p>The family of Intel QuickSync Video decoders (VC1, MPEG-2, H.264, HEVC,
JPEG/MJPEG, VP8, VP9, AV1).
</p>
<a name="Common-Options"></a>
<h4 class="subsection">4.6.1 Common Options<span class="pull-right"><a class="anchor hidden-xs" href="#Common-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Common-Options" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by all qsv decoders.
</p>
<dl compact="compact">
<dt><span><samp><var>async_depth</var></samp></span></dt>
<dd><p>Internal parallelization depth, the higher the value the higher the latency.
</p>
</dd>
<dt><span><samp><var>gpu_copy</var></samp></span></dt>
<dd><p>A GPU-accelerated copy between video and system memory
</p><dl compact="compact">
<dt><span>&lsquo;<samp>default</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>on</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>off</samp>&rsquo;</span></dt>
</dl>

</dd>
</dl>

<a name="HEVC-Options"></a>
<h4 class="subsection">4.6.2 HEVC Options<span class="pull-right"><a class="anchor hidden-xs" href="#HEVC-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-HEVC-Options" aria-hidden="true">TOC</a></span></h4>
<p>Extra options for hevc_qsv.
</p>
<dl compact="compact">
<dt><span><samp><var>load_plugin</var></samp></span></dt>
<dd><p>A user plugin to load in an internal session
</p><dl compact="compact">
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hevc_sw</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hevc_hw</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>load_plugins</var></samp></span></dt>
<dd><p>A :-separate list of hexadecimal plugin UIDs to load in an internal session
</p>
</dd>
</dl>

<a name="v210"></a>
<h3 class="section">4.7 v210<span class="pull-right"><a class="anchor hidden-xs" href="#v210" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-v210" aria-hidden="true">TOC</a></span></h3>

<p>Uncompressed 4:2:2 10-bit decoder.
</p>
<a name="Options-4"></a>
<h4 class="subsection">4.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-4" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>custom_stride</samp></span></dt>
<dd><p>Set the line size of the v210 data in bytes. The default value is 0
(autodetect). You can use the special -1 value for a strideless v210 as seen in
BOXX files.
</p>
</dd>
</dl>


<a name="Audio-Decoders"></a>
<h2 class="chapter">5 Audio Decoders<span class="pull-right"><a class="anchor hidden-xs" href="#Audio-Decoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Audio-Decoders" aria-hidden="true">TOC</a></span></h2>

<p>A description of some of the currently available audio decoders
follows.
</p>
<a name="ac3"></a>
<h3 class="section">5.1 ac3<span class="pull-right"><a class="anchor hidden-xs" href="#ac3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ac3" aria-hidden="true">TOC</a></span></h3>

<p>AC-3 audio decoder.
</p>
<p>This decoder implements part of ATSC A/52:2010 and ETSI TS 102 366, as well as
the undocumented RealAudio 3 (a.k.a. dnet).
</p>
<a name="AC_002d3-Decoder-Options"></a>
<h4 class="subsection">5.1.1 AC-3 Decoder Options<span class="pull-right"><a class="anchor hidden-xs" href="#AC_002d3-Decoder-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-AC_002d3-Decoder-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-drc_scale <var>value</var></samp></span></dt>
<dd><p>Dynamic Range Scale Factor. The factor to apply to dynamic range values
from the AC-3 stream. This factor is applied exponentially. The default value is 1.
There are 3 notable scale factor ranges:
</p><dl compact="compact">
<dt><span><samp>drc_scale == 0</samp></span></dt>
<dd><p>DRC disabled. Produces full range audio.
</p></dd>
<dt><span><samp>0 &lt; drc_scale &lt;= 1</samp></span></dt>
<dd><p>DRC enabled.  Applies a fraction of the stream DRC value.
Audio reproduction is between full range and full compression.
</p></dd>
<dt><span><samp>drc_scale &gt; 1</samp></span></dt>
<dd><p>DRC enabled. Applies drc_scale asymmetrically.
Loud sounds are fully compressed.  Soft sounds are enhanced.
</p></dd>
</dl>

</dd>
</dl>

<a name="flac-1"></a>
<h3 class="section">5.2 flac<span class="pull-right"><a class="anchor hidden-xs" href="#flac-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flac-1" aria-hidden="true">TOC</a></span></h3>

<p>FLAC audio decoder.
</p>
<p>This decoder aims to implement the complete FLAC specification from Xiph.
</p>
<a name="FLAC-Decoder-options"></a>
<h4 class="subsection">5.2.1 FLAC Decoder options<span class="pull-right"><a class="anchor hidden-xs" href="#FLAC-Decoder-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-FLAC-Decoder-options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-use_buggy_lpc</samp></span></dt>
<dd><p>The lavc FLAC encoder used to produce buggy streams with high lpc values
(like the default value). This option makes it possible to decode such streams
correctly by using lavc&rsquo;s old buggy lpc logic for decoding.
</p>
</dd>
</dl>

<a name="ffwavesynth"></a>
<h3 class="section">5.3 ffwavesynth<span class="pull-right"><a class="anchor hidden-xs" href="#ffwavesynth" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ffwavesynth" aria-hidden="true">TOC</a></span></h3>

<p>Internal wave synthesizer.
</p>
<p>This decoder generates wave patterns according to predefined sequences. Its
use is purely internal and the format of the data it accepts is not publicly
documented.
</p>
<a name="libcelt"></a>
<h3 class="section">5.4 libcelt<span class="pull-right"><a class="anchor hidden-xs" href="#libcelt" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libcelt" aria-hidden="true">TOC</a></span></h3>

<p>libcelt decoder wrapper.
</p>
<p>libcelt allows libavcodec to decode the Xiph CELT ultra-low delay audio codec.
Requires the presence of the libcelt headers and library during configuration.
You need to explicitly configure the build with <code>--enable-libcelt</code>.
</p>
<a name="libgsm"></a>
<h3 class="section">5.5 libgsm<span class="pull-right"><a class="anchor hidden-xs" href="#libgsm" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libgsm" aria-hidden="true">TOC</a></span></h3>

<p>libgsm decoder wrapper.
</p>
<p>libgsm allows libavcodec to decode the GSM full rate audio codec. Requires
the presence of the libgsm headers and library during configuration. You need
to explicitly configure the build with <code>--enable-libgsm</code>.
</p>
<p>This decoder supports both the ordinary GSM and the Microsoft variant.
</p>
<a name="libilbc"></a>
<h3 class="section">5.6 libilbc<span class="pull-right"><a class="anchor hidden-xs" href="#libilbc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libilbc" aria-hidden="true">TOC</a></span></h3>

<p>libilbc decoder wrapper.
</p>
<p>libilbc allows libavcodec to decode the Internet Low Bitrate Codec (iLBC)
audio codec. Requires the presence of the libilbc headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libilbc</code>.
</p>
<a name="Options-5"></a>
<h4 class="subsection">5.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-5" aria-hidden="true">TOC</a></span></h4>

<p>The following option is supported by the libilbc wrapper.
</p>
<dl compact="compact">
<dt><span><samp>enhance</samp></span></dt>
<dd>
<p>Enable the enhancement of the decoded audio when set to 1. The default
value is 0 (disabled).
</p>
</dd>
</dl>

<a name="libopencore_002damrnb"></a>
<h3 class="section">5.7 libopencore-amrnb<span class="pull-right"><a class="anchor hidden-xs" href="#libopencore_002damrnb" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopencore_002damrnb" aria-hidden="true">TOC</a></span></h3>

<p>libopencore-amrnb decoder wrapper.
</p>
<p>libopencore-amrnb allows libavcodec to decode the Adaptive Multi-Rate
Narrowband audio codec. Using it requires the presence of the
libopencore-amrnb headers and library during configuration. You need to
explicitly configure the build with <code>--enable-libopencore-amrnb</code>.
</p>
<p>An FFmpeg native decoder for AMR-NB exists, so users can decode AMR-NB
without this library.
</p>
<a name="libopencore_002damrwb"></a>
<h3 class="section">5.8 libopencore-amrwb<span class="pull-right"><a class="anchor hidden-xs" href="#libopencore_002damrwb" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopencore_002damrwb" aria-hidden="true">TOC</a></span></h3>

<p>libopencore-amrwb decoder wrapper.
</p>
<p>libopencore-amrwb allows libavcodec to decode the Adaptive Multi-Rate
Wideband audio codec. Using it requires the presence of the
libopencore-amrwb headers and library during configuration. You need to
explicitly configure the build with <code>--enable-libopencore-amrwb</code>.
</p>
<p>An FFmpeg native decoder for AMR-WB exists, so users can decode AMR-WB
without this library.
</p>
<a name="libopus"></a>
<h3 class="section">5.9 libopus<span class="pull-right"><a class="anchor hidden-xs" href="#libopus" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopus" aria-hidden="true">TOC</a></span></h3>

<p>libopus decoder wrapper.
</p>
<p>libopus allows libavcodec to decode the Opus Interactive Audio Codec.
Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libopus</code>.
</p>
<p>An FFmpeg native decoder for Opus exists, so users can decode Opus
without this library.
</p>

<a name="Subtitles-Decoders"></a>
<h2 class="chapter">6 Subtitles Decoders<span class="pull-right"><a class="anchor hidden-xs" href="#Subtitles-Decoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Subtitles-Decoders" aria-hidden="true">TOC</a></span></h2>

<a name="libaribb24"></a>
<h3 class="section">6.1 libaribb24<span class="pull-right"><a class="anchor hidden-xs" href="#libaribb24" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libaribb24" aria-hidden="true">TOC</a></span></h3>

<p>ARIB STD-B24 caption decoder.
</p>
<p>Implements profiles A and C of the ARIB STD-B24 standard.
</p>
<a name="libaribb24-Decoder-Options"></a>
<h4 class="subsection">6.1.1 libaribb24 Decoder Options<span class="pull-right"><a class="anchor hidden-xs" href="#libaribb24-Decoder-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libaribb24-Decoder-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-aribb24-base-path <var>path</var></samp></span></dt>
<dd><p>Sets the base path for the libaribb24 library. This is utilized for reading of
configuration files (for custom unicode conversions), and for dumping of
non-text symbols as images under that location.
</p>
<p>Unset by default.
</p>
</dd>
<dt><span><samp>-aribb24-skip-ruby-text <var>boolean</var></samp></span></dt>
<dd><p>Tells the decoder wrapper to skip text blocks that contain half-height ruby
text.
</p>
<p>Enabled by default.
</p>
</dd>
</dl>

<a name="dvbsub"></a>
<h3 class="section">6.2 dvbsub<span class="pull-right"><a class="anchor hidden-xs" href="#dvbsub" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dvbsub" aria-hidden="true">TOC</a></span></h3>

<a name="Options-6"></a>
<h4 class="subsection">6.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-6" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>compute_clut</samp></span></dt>
<dd><dl compact="compact">
<dt><span><samp>-2</samp></span></dt>
<dd><p>Compute clut once if no matching CLUT is in the stream.
</p></dd>
<dt><span><samp>-1</samp></span></dt>
<dd><p>Compute clut if no matching CLUT is in the stream.
</p></dd>
<dt><span><samp>0</samp></span></dt>
<dd><p>Never compute CLUT
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dd><p>Always compute CLUT and override the one provided in the stream.
</p></dd>
</dl>
</dd>
<dt><span><samp>dvb_substream</samp></span></dt>
<dd><p>Selects the dvb substream, or all substreams if -1 which is default.
</p>
</dd>
</dl>

<a name="dvdsub"></a>
<h3 class="section">6.3 dvdsub<span class="pull-right"><a class="anchor hidden-xs" href="#dvdsub" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dvdsub" aria-hidden="true">TOC</a></span></h3>

<p>This codec decodes the bitmap subtitles used in DVDs; the same subtitles can
also be found in VobSub file pairs and in some Matroska files.
</p>
<a name="Options-7"></a>
<h4 class="subsection">6.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-7" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>palette</samp></span></dt>
<dd><p>Specify the global palette used by the bitmaps. When stored in VobSub, the
palette is normally specified in the index file; in Matroska, the palette is
stored in the codec extra-data in the same format as in VobSub. In DVDs, the
palette is stored in the IFO file, and therefore not available when reading
from dumped VOB files.
</p>
<p>The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example <code>0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b</code>.
</p>
</dd>
<dt><span><samp>ifo_palette</samp></span></dt>
<dd><p>Specify the IFO file from which the global palette is obtained.
(experimental)
</p>
</dd>
<dt><span><samp>forced_subs_only</samp></span></dt>
<dd><p>Only decode subtitle entries marked as forced. Some titles have forced
and non-forced subtitles in the same track. Setting this flag to <code>1</code>
will only keep the forced subtitles. Default value is <code>0</code>.
</p></dd>
</dl>

<a name="libzvbi_002dteletext"></a>
<h3 class="section">6.4 libzvbi-teletext<span class="pull-right"><a class="anchor hidden-xs" href="#libzvbi_002dteletext" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libzvbi_002dteletext" aria-hidden="true">TOC</a></span></h3>

<p>Libzvbi allows libavcodec to decode DVB teletext pages and DVB teletext
subtitles. Requires the presence of the libzvbi headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libzvbi</code>.
</p>
<a name="Options-8"></a>
<h4 class="subsection">6.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-8" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>txt_page</samp></span></dt>
<dd><p>List of teletext page numbers to decode. Pages that do not match the specified
list are dropped. You may use the special <code>*</code> string to match all pages,
or <code>subtitle</code> to match all subtitle pages.
Default value is *.
</p></dd>
<dt><span><samp>txt_default_region</samp></span></dt>
<dd><p>Set default character set used for decoding, a value between 0 and 87 (see
ETS 300 706, Section 15, Table 32). Default value is -1, which does not
override the libzvbi default. This option is needed for some legacy level 1.0
transmissions which cannot signal the proper charset.
</p></dd>
<dt><span><samp>txt_chop_top</samp></span></dt>
<dd><p>Discards the top teletext line. Default value is 1.
</p></dd>
<dt><span><samp>txt_format</samp></span></dt>
<dd><p>Specifies the format of the decoded subtitles.
</p><dl compact="compact">
<dt><span><samp>bitmap</samp></span></dt>
<dd><p>The default format, you should use this for teletext pages, because certain
graphics and colors cannot be expressed in simple text or even ASS.
</p></dd>
<dt><span><samp>text</samp></span></dt>
<dd><p>Simple text based output without formatting.
</p></dd>
<dt><span><samp>ass</samp></span></dt>
<dd><p>Formatted ASS output, subtitle pages and teletext pages are returned in
different styles, subtitle pages are stripped down to text, but an effort is
made to keep the text alignment and the formatting.
</p></dd>
</dl>
</dd>
<dt><span><samp>txt_left</samp></span></dt>
<dd><p>X offset of generated bitmaps, default is 0.
</p></dd>
<dt><span><samp>txt_top</samp></span></dt>
<dd><p>Y offset of generated bitmaps, default is 0.
</p></dd>
<dt><span><samp>txt_chop_spaces</samp></span></dt>
<dd><p>Chops leading and trailing spaces and removes empty lines from the generated
text. This option is useful for teletext based subtitles where empty spaces may
be present at the start or at the end of the lines or empty lines may be
present between the subtitle lines because of double-sized teletext characters.
Default value is 1.
</p></dd>
<dt><span><samp>txt_duration</samp></span></dt>
<dd><p>Sets the display duration of the decoded teletext pages or subtitles in
milliseconds. Default value is -1 which means infinity or until the next
subtitle event comes.
</p></dd>
<dt><span><samp>txt_transparent</samp></span></dt>
<dd><p>Force transparent background of the generated teletext bitmaps. Default value
is 0 which means an opaque background.
</p></dd>
<dt><span><samp>txt_opacity</samp></span></dt>
<dd><p>Sets the opacity (0-255) of the teletext background. If
<samp>txt_transparent</samp> is not set, it only affects characters between a start
box and an end box, typically subtitles. Default value is 0 if
<samp>txt_transparent</samp> is set, 255 otherwise.
</p>
</dd>
</dl>

<a name="Encoders"></a>
<h2 class="chapter">7 Encoders<span class="pull-right"><a class="anchor hidden-xs" href="#Encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Encoders" aria-hidden="true">TOC</a></span></h2>

<p>Encoders are configured elements in FFmpeg which allow the encoding of
multimedia streams.
</p>
<p>When you configure your FFmpeg build, all the supported native encoders
are enabled by default. Encoders requiring an external library must be enabled
manually via the corresponding <code>--enable-lib</code> option. You can list all
available encoders using the configure option <code>--list-encoders</code>.
</p>
<p>You can disable all the encoders with the configure option
<code>--disable-encoders</code> and selectively enable / disable single encoders
with the options <code>--enable-encoder=<var>ENCODER</var></code> /
<code>--disable-encoder=<var>ENCODER</var></code>.
</p>
<p>The option <code>-encoders</code> of the ff* tools will display the list of
enabled encoders.
</p>

<a name="Audio-Encoders"></a>
<h2 class="chapter">8 Audio Encoders<span class="pull-right"><a class="anchor hidden-xs" href="#Audio-Encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Audio-Encoders" aria-hidden="true">TOC</a></span></h2>

<p>A description of some of the currently available audio encoders
follows.
</p>
<span id="aacenc"></span><a name="aac"></a>
<h3 class="section">8.1 aac<span class="pull-right"><a class="anchor hidden-xs" href="#aac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aac" aria-hidden="true">TOC</a></span></h3>

<p>Advanced Audio Coding (AAC) encoder.
</p>
<p>This encoder is the default AAC encoder, natively implemented into FFmpeg.
</p>
<a name="Options-9"></a>
<h4 class="subsection">8.1.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-9" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bit rate in bits/s. Setting this automatically activates constant bit rate
(CBR) mode. If this option is unspecified it is set to 128kbps.
</p>
</dd>
<dt><span><samp>q</samp></span></dt>
<dd><p>Set quality for variable bit rate (VBR) mode. This option is valid only using
the <code>ffmpeg</code> command-line tool. For library interface users, use
<samp>global_quality</samp>.
</p>
</dd>
<dt><span><samp>cutoff</samp></span></dt>
<dd><p>Set cutoff frequency. If unspecified will allow the encoder to dynamically
adjust the cutoff to improve clarity on low bitrates.
</p>
</dd>
<dt><span><samp>aac_coder</samp></span></dt>
<dd><p>Set AAC encoder coding method. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>twoloop</samp>&rsquo;</span></dt>
<dd><p>Two loop searching (TLS) method. This is the default method.
</p>
<p>This method first sets quantizers depending on band thresholds and then tries
to find an optimal combination by adding or subtracting a specific value from
all quantizers and adjusting some individual quantizer a little.  Will tune
itself based on whether <samp>aac_is</samp>, <samp>aac_ms</samp> and <samp>aac_pns</samp>
are enabled.
</p>
</dd>
<dt><span>&lsquo;<samp>anmr</samp>&rsquo;</span></dt>
<dd><p>Average noise to mask ratio (ANMR) trellis-based solution.
</p>
<p>This is an experimental coder which currently produces a lower quality, is more
unstable and is slower than the default twoloop coder but has potential.
Currently has no support for the <samp>aac_is</samp> or <samp>aac_pns</samp> options.
Not currently recommended.
</p>
</dd>
<dt><span>&lsquo;<samp>fast</samp>&rsquo;</span></dt>
<dd><p>Constant quantizer method.
</p>
<p>Uses a cheaper version of twoloop algorithm that doesn&rsquo;t try to do as many
clever adjustments. Worse with low bitrates (less than 64kbps), but is better
and much faster at higher bitrates.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>aac_ms</samp></span></dt>
<dd><p>Sets mid/side coding mode. The default value of &quot;auto&quot; will automatically use
M/S with bands which will benefit from such coding. Can be forced for all bands
using the value &quot;enable&quot;, which is mainly useful for debugging or disabled using
&quot;disable&quot;.
</p>
</dd>
<dt><span><samp>aac_is</samp></span></dt>
<dd><p>Sets intensity stereo coding tool usage. By default, it&rsquo;s enabled and will
automatically toggle IS for similar pairs of stereo bands if it&rsquo;s beneficial.
Can be disabled for debugging by setting the value to &quot;disable&quot;.
</p>
</dd>
<dt><span><samp>aac_pns</samp></span></dt>
<dd><p>Uses perceptual noise substitution to replace low entropy high frequency bands
with imperceptible white noise during the decoding process. By default, it&rsquo;s
enabled, but can be disabled for debugging purposes by using &quot;disable&quot;.
</p>
</dd>
<dt><span><samp>aac_tns</samp></span></dt>
<dd><p>Enables the use of a multitap FIR filter which spans through the high frequency
bands to hide quantization noise during the encoding process and is reverted
by the decoder. As well as decreasing unpleasant artifacts in the high range
this also reduces the entropy in the high bands and allows for more bits to
be used by the mid-low bands. By default it&rsquo;s enabled but can be disabled for
debugging by setting the option to &quot;disable&quot;.
</p>
</dd>
<dt><span><samp>aac_ltp</samp></span></dt>
<dd><p>Enables the use of the long term prediction extension which increases coding
efficiency in very low bandwidth situations such as encoding of voice or
solo piano music by extending constant harmonic peaks in bands throughout
frames. This option is implied by profile:a aac_low and is incompatible with
aac_pred. Use in conjunction with <samp>-ar</samp> to decrease the samplerate.
</p>
</dd>
<dt><span><samp>aac_pred</samp></span></dt>
<dd><p>Enables the use of a more traditional style of prediction where the spectral
coefficients transmitted are replaced by the difference of the current
coefficients minus the previous &quot;predicted&quot; coefficients. In theory and sometimes
in practice this can improve quality for low to mid bitrate audio.
This option implies the aac_main profile and is incompatible with aac_ltp.
</p>
</dd>
<dt><span><samp>profile</samp></span></dt>
<dd><p>Sets the encoding profile, possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>aac_low</samp>&rsquo;</span></dt>
<dd><p>The default, AAC &quot;Low-complexity&quot; profile. Is the most compatible and produces
decent quality.
</p>
</dd>
<dt><span>&lsquo;<samp>mpeg2_aac_low</samp>&rsquo;</span></dt>
<dd><p>Equivalent to <code>-profile:a aac_low -aac_pns 0</code>. PNS was introduced with the
MPEG4 specifications.
</p>
</dd>
<dt><span>&lsquo;<samp>aac_ltp</samp>&rsquo;</span></dt>
<dd><p>Long term prediction profile, is enabled by and will enable the <samp>aac_ltp</samp>
option. Introduced in MPEG4.
</p>
</dd>
<dt><span>&lsquo;<samp>aac_main</samp>&rsquo;</span></dt>
<dd><p>Main-type prediction profile, is enabled by and will enable the <samp>aac_pred</samp>
option. Introduced in MPEG2.
</p>
</dd>
</dl>
<p>If this option is unspecified it is set to &lsquo;<samp>aac_low</samp>&rsquo;.
</p></dd>
</dl>

<a name="ac3-and-ac3_005ffixed"></a>
<h3 class="section">8.2 ac3 and ac3_fixed<span class="pull-right"><a class="anchor hidden-xs" href="#ac3-and-ac3_005ffixed" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ac3-and-ac3_005ffixed" aria-hidden="true">TOC</a></span></h3>

<p>AC-3 audio encoders.
</p>
<p>These encoders implement part of ATSC A/52:2010 and ETSI TS 102 366, as well as
the undocumented RealAudio 3 (a.k.a. dnet).
</p>
<p>The <var>ac3</var> encoder uses floating-point math, while the <var>ac3_fixed</var>
encoder only uses fixed-point integer math. This does not mean that one is
always faster, just that one or the other may be better suited to a
particular system. The <var>ac3_fixed</var> encoder is not the default codec for
any of the output formats, so it must be specified explicitly using the option
<code>-acodec ac3_fixed</code> in order to use it.
</p>
<a name="AC_002d3-Metadata"></a>
<h4 class="subsection">8.2.1 AC-3 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#AC_002d3-Metadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-AC_002d3-Metadata" aria-hidden="true">TOC</a></span></h4>

<p>The AC-3 metadata options are used to set parameters that describe the audio,
but in most cases do not affect the audio encoding itself. Some of the options
do directly affect or influence the decoding and playback of the resulting
bitstream, while others are just for informational purposes. A few of the
options will add bits to the output stream that could otherwise be used for
audio data, and will thus affect the quality of the output. Those will be
indicated accordingly with a note in the option list below.
</p>
<p>These parameters are described in detail in several publicly-available
documents.
</p><ul>
<li> <a href="http://www.atsc.org/cms/standards/a_52-2010.pdf">A/52:2010 - Digital Audio Compression (AC-3) (E-AC-3) Standard</a>
</li><li> <a href="http://www.atsc.org/cms/standards/a_54a_with_corr_1.pdf">A/54 - Guide to the Use of the ATSC Digital Television Standard</a>
</li><li> <a href="http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/18_Metadata.Guide.pdf">Dolby Metadata Guide</a>
</li><li> <a href="http://www.dolby.com/uploadedFiles/zz-_Shared_Assets/English_PDFs/Professional/46_DDEncodingGuidelines.pdf">Dolby Digital Professional Encoding Guidelines</a>
</li></ul>

<a name="Metadata-Control-Options"></a>
<h4 class="subsubsection">8.2.1.1 Metadata Control Options<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata-Control-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata-Control-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-per_frame_metadata <var>boolean</var></samp></span></dt>
<dd><p>Allow Per-Frame Metadata. Specifies if the encoder should check for changing
metadata for each frame.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dd><p>The metadata values set at initialization will be used for every frame in the
stream. (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dd><p>Metadata values can be changed before encoding each frame.
</p></dd>
</dl>

</dd>
</dl>

<a name="Downmix-Levels"></a>
<h4 class="subsubsection">8.2.1.2 Downmix Levels<span class="pull-right"><a class="anchor hidden-xs" href="#Downmix-Levels" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Downmix-Levels" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-center_mixlev <var>level</var></samp></span></dt>
<dd><p>Center Mix Level. The amount of gain the decoder should apply to the center
channel when downmixing to stereo. This field will only be written to the
bitstream if a center channel is present. The value is specified as a scale
factor. There are 3 valid values:
</p><dl compact="compact">
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3dB gain
</p></dd>
<dt><span><samp>0.595</samp></span></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6dB gain
</p></dd>
</dl>

</dd>
<dt><span><samp>-surround_mixlev <var>level</var></samp></span></dt>
<dd><p>Surround Mix Level. The amount of gain the decoder should apply to the surround
channel(s) when downmixing to stereo. This field will only be written to the
bitstream if one or more surround channels are present. The value is specified
as a scale factor.  There are 3 valid values:
</p><dl compact="compact">
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3dB gain
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6dB gain (default)
</p></dd>
<dt><span><samp>0.000</samp></span></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
</dl>

<a name="Audio-Production-Information"></a>
<h4 class="subsubsection">8.2.1.3 Audio Production Information<span class="pull-right"><a class="anchor hidden-xs" href="#Audio-Production-Information" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Audio-Production-Information" aria-hidden="true">TOC</a></span></h4>
<p>Audio Production Information is optional information describing the mixing
environment.  Either none or both of the fields are written to the bitstream.
</p>
<dl compact="compact">
<dt><span><samp>-mixing_level <var>number</var></samp></span></dt>
<dd><p>Mixing Level. Specifies peak sound pressure level (SPL) in the production
environment when the mix was mastered. Valid values are 80 to 111, or -1 for
unknown or not indicated. The default value is -1, but that value cannot be
used if the Audio Production Information is written to the bitstream. Therefore,
if the <code>room_type</code> option is not the default value, the <code>mixing_level</code>
option must not be -1.
</p>
</dd>
<dt><span><samp>-room_type <var>type</var></samp></span></dt>
<dd><p>Room Type. Describes the equalization used during the final mixing session at
the studio or on the dubbing stage. A large room is a dubbing stage with the
industry standard X-curve equalization; a small room has flat equalization.
This field will not be written to the bitstream if both the <code>mixing_level</code>
option and the <code>room_type</code> option have the default values.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>notindicated</samp></span></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>large</samp></span></dt>
<dd><p>Large Room
</p></dd>
<dt><span><samp>2</samp></span></dt>
<dt><span><samp>small</samp></span></dt>
<dd><p>Small Room
</p></dd>
</dl>

</dd>
</dl>

<a name="Other-Metadata-Options"></a>
<h4 class="subsubsection">8.2.1.4 Other Metadata Options<span class="pull-right"><a class="anchor hidden-xs" href="#Other-Metadata-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Other-Metadata-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-copyright <var>boolean</var></samp></span></dt>
<dd><p>Copyright Indicator. Specifies whether a copyright exists for this audio.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>No Copyright Exists (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Copyright Exists
</p></dd>
</dl>

</dd>
<dt><span><samp>-dialnorm <var>value</var></samp></span></dt>
<dd><p>Dialogue Normalization. Indicates how far the average dialogue level of the
program is below digital 100% full scale (0 dBFS). This parameter determines a
level shift during audio reproduction that sets the average volume of the
dialogue to a preset level. The goal is to match volume level between program
sources. A value of -31dB will result in no volume level change, relative to
the source volume, during audio reproduction. Valid values are whole numbers in
the range -31 to -1, with -31 being the default.
</p>
</dd>
<dt><span><samp>-dsur_mode <var>mode</var></samp></span></dt>
<dd><p>Dolby Surround Mode. Specifies whether the stereo signal uses Dolby Surround
(Pro Logic). This field will only be written to the bitstream if the audio
stream is stereo. Using this option does <b>NOT</b> mean the encoder will actually
apply Dolby Surround processing.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>notindicated</samp></span></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>Not Dolby Surround Encoded
</p></dd>
<dt><span><samp>2</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Dolby Surround Encoded
</p></dd>
</dl>

</dd>
<dt><span><samp>-original <var>boolean</var></samp></span></dt>
<dd><p>Original Bit Stream Indicator. Specifies whether this audio is from the
original source and not a copy.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>Not Original Source
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Original Source (default)
</p></dd>
</dl>

</dd>
</dl>

<a name="Extended-Bitstream-Information"></a>
<h4 class="subsection">8.2.2 Extended Bitstream Information<span class="pull-right"><a class="anchor hidden-xs" href="#Extended-Bitstream-Information" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Extended-Bitstream-Information" aria-hidden="true">TOC</a></span></h4>
<p>The extended bitstream options are part of the Alternate Bit Stream Syntax as
specified in Annex D of the A/52:2010 standard. It is grouped into 2 parts.
If any one parameter in a group is specified, all values in that group will be
written to the bitstream.  Default values are used for those that are written
but have not been specified.  If the mixing levels are written, the decoder
will use these values instead of the ones specified in the <code>center_mixlev</code>
and <code>surround_mixlev</code> options if it supports the Alternate Bit Stream
Syntax.
</p>
<a name="Extended-Bitstream-Information-_002d-Part-1"></a>
<h4 class="subsubsection">8.2.2.1 Extended Bitstream Information - Part 1<span class="pull-right"><a class="anchor hidden-xs" href="#Extended-Bitstream-Information-_002d-Part-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Extended-Bitstream-Information-_002d-Part-1" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-dmix_mode <var>mode</var></samp></span></dt>
<dd><p>Preferred Stereo Downmix Mode. Allows the user to select either Lt/Rt
(Dolby Surround) or Lo/Ro (normal stereo) as the preferred stereo downmix mode.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>notindicated</samp></span></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>ltrt</samp></span></dt>
<dd><p>Lt/Rt Downmix Preferred
</p></dd>
<dt><span><samp>2</samp></span></dt>
<dt><span><samp>loro</samp></span></dt>
<dd><p>Lo/Ro Downmix Preferred
</p></dd>
</dl>

</dd>
<dt><span><samp>-ltrt_cmixlev <var>level</var></samp></span></dt>
<dd><p>Lt/Rt Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lt/Rt mode.
</p><dl compact="compact">
<dt><span><samp>1.414</samp></span></dt>
<dd><p>Apply +3dB gain
</p></dd>
<dt><span><samp>1.189</samp></span></dt>
<dd><p>Apply +1.5dB gain
</p></dd>
<dt><span><samp>1.000</samp></span></dt>
<dd><p>Apply 0dB gain
</p></dd>
<dt><span><samp>0.841</samp></span></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><span><samp>0.595</samp></span></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6.0dB gain
</p></dd>
<dt><span><samp>0.000</samp></span></dt>
<dd><p>Silence Center Channel
</p></dd>
</dl>

</dd>
<dt><span><samp>-ltrt_surmixlev <var>level</var></samp></span></dt>
<dd><p>Lt/Rt Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lt/Rt mode.
</p><dl compact="compact">
<dt><span><samp>0.841</samp></span></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><span><samp>0.595</samp></span></dt>
<dd><p>Apply -4.5dB gain
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6.0dB gain (default)
</p></dd>
<dt><span><samp>0.000</samp></span></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
<dt><span><samp>-loro_cmixlev <var>level</var></samp></span></dt>
<dd><p>Lo/Ro Center Mix Level. The amount of gain the decoder should apply to the
center channel when downmixing to stereo in Lo/Ro mode.
</p><dl compact="compact">
<dt><span><samp>1.414</samp></span></dt>
<dd><p>Apply +3dB gain
</p></dd>
<dt><span><samp>1.189</samp></span></dt>
<dd><p>Apply +1.5dB gain
</p></dd>
<dt><span><samp>1.000</samp></span></dt>
<dd><p>Apply 0dB gain
</p></dd>
<dt><span><samp>0.841</samp></span></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><span><samp>0.595</samp></span></dt>
<dd><p>Apply -4.5dB gain (default)
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6.0dB gain
</p></dd>
<dt><span><samp>0.000</samp></span></dt>
<dd><p>Silence Center Channel
</p></dd>
</dl>

</dd>
<dt><span><samp>-loro_surmixlev <var>level</var></samp></span></dt>
<dd><p>Lo/Ro Surround Mix Level. The amount of gain the decoder should apply to the
surround channel(s) when downmixing to stereo in Lo/Ro mode.
</p><dl compact="compact">
<dt><span><samp>0.841</samp></span></dt>
<dd><p>Apply -1.5dB gain
</p></dd>
<dt><span><samp>0.707</samp></span></dt>
<dd><p>Apply -3.0dB gain
</p></dd>
<dt><span><samp>0.595</samp></span></dt>
<dd><p>Apply -4.5dB gain
</p></dd>
<dt><span><samp>0.500</samp></span></dt>
<dd><p>Apply -6.0dB gain (default)
</p></dd>
<dt><span><samp>0.000</samp></span></dt>
<dd><p>Silence Surround Channel(s)
</p></dd>
</dl>

</dd>
</dl>

<a name="Extended-Bitstream-Information-_002d-Part-2"></a>
<h4 class="subsubsection">8.2.2.2 Extended Bitstream Information - Part 2<span class="pull-right"><a class="anchor hidden-xs" href="#Extended-Bitstream-Information-_002d-Part-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Extended-Bitstream-Information-_002d-Part-2" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-dsurex_mode <var>mode</var></samp></span></dt>
<dd><p>Dolby Surround EX Mode. Indicates whether the stream uses Dolby Surround EX
(7.1 matrixed to 5.1). Using this option does <b>NOT</b> mean the encoder will actually
apply Dolby Surround EX processing.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>notindicated</samp></span></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Dolby Surround EX Off
</p></dd>
<dt><span><samp>2</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>Dolby Surround EX On
</p></dd>
</dl>

</dd>
<dt><span><samp>-dheadphone_mode <var>mode</var></samp></span></dt>
<dd><p>Dolby Headphone Mode. Indicates whether the stream uses Dolby Headphone
encoding (multi-channel matrixed to 2.0 for use with headphones). Using this
option does <b>NOT</b> mean the encoder will actually apply Dolby Headphone
processing.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>notindicated</samp></span></dt>
<dd><p>Not Indicated (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Dolby Headphone Off
</p></dd>
<dt><span><samp>2</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>Dolby Headphone On
</p></dd>
</dl>

</dd>
<dt><span><samp>-ad_conv_type <var>type</var></samp></span></dt>
<dd><p>A/D Converter Type. Indicates whether the audio has passed through HDCD A/D
conversion.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>standard</samp></span></dt>
<dd><p>Standard A/D Converter (default)
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>hdcd</samp></span></dt>
<dd><p>HDCD A/D Converter
</p></dd>
</dl>

</dd>
</dl>

<a name="Other-AC_002d3-Encoding-Options"></a>
<h4 class="subsection">8.2.3 Other AC-3 Encoding Options<span class="pull-right"><a class="anchor hidden-xs" href="#Other-AC_002d3-Encoding-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Other-AC_002d3-Encoding-Options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-stereo_rematrixing <var>boolean</var></samp></span></dt>
<dd><p>Stereo Rematrixing. Enables/Disables use of rematrixing for stereo input. This
is an optional AC-3 feature that increases quality by selectively encoding
the left/right channels as mid/side. This option is enabled by default, and it
is highly recommended that it be left as enabled except for testing purposes.
</p>
</dd>
<dt><span><samp>cutoff <var>frequency</var></samp></span></dt>
<dd><p>Set lowpass cutoff frequency. If unspecified, the encoder selects a default
determined by various other encoding parameters.
</p>
</dd>
</dl>

<a name="Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options"></a>
<h4 class="subsection">8.2.4 Floating-Point-Only AC-3 Encoding Options<span class="pull-right"><a class="anchor hidden-xs" href="#Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Floating_002dPoint_002dOnly-AC_002d3-Encoding-Options" aria-hidden="true">TOC</a></span></h4>

<p>These options are only valid for the floating-point encoder and do not exist
for the fixed-point encoder due to the corresponding features not being
implemented in fixed-point.
</p>
<dl compact="compact">
<dt><span><samp>-channel_coupling <var>boolean</var></samp></span></dt>
<dd><p>Enables/Disables use of channel coupling, which is an optional AC-3 feature
that increases quality by combining high frequency information from multiple
channels into a single channel. The per-channel high frequency information is
sent with less accuracy in both the frequency and time domains. This allows
more bits to be used for lower frequencies while preserving enough information
to reconstruct the high frequencies. This option is enabled by default for the
floating-point encoder and should generally be left as enabled except for
testing purposes or to increase encoding speed.
</p><dl compact="compact">
<dt><span><samp>-1</samp></span></dt>
<dt><span><samp>auto</samp></span></dt>
<dd><p>Selected by Encoder (default)
</p></dd>
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>off</samp></span></dt>
<dd><p>Disable Channel Coupling
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>on</samp></span></dt>
<dd><p>Enable Channel Coupling
</p></dd>
</dl>

</dd>
<dt><span><samp>-cpl_start_band <var>number</var></samp></span></dt>
<dd><p>Coupling Start Band. Sets the channel coupling start band, from 1 to 15. If a
value higher than the bandwidth is used, it will be reduced to 1 less than the
coupling end band. If <var>auto</var> is used, the start band will be determined by
the encoder based on the bit rate, sample rate, and channel layout. This option
has no effect if channel coupling is disabled.
</p><dl compact="compact">
<dt><span><samp>-1</samp></span></dt>
<dt><span><samp>auto</samp></span></dt>
<dd><p>Selected by Encoder (default)
</p></dd>
</dl>

</dd>
</dl>

<span id="flac"></span><a name="flac-2"></a>
<h3 class="section">8.3 flac<span class="pull-right"><a class="anchor hidden-xs" href="#flac-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flac-2" aria-hidden="true">TOC</a></span></h3>

<p>FLAC (Free Lossless Audio Codec) Encoder
</p>
<a name="Options-10"></a>
<h4 class="subsection">8.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-10" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by FFmpeg&rsquo;s flac encoder.
</p>
<dl compact="compact">
<dt><span><samp>compression_level</samp></span></dt>
<dd><p>Sets the compression level, which chooses defaults for many other options
if they are not set explicitly. Valid values are from 0 to 12, 5 is the
default.
</p>
</dd>
<dt><span><samp>frame_size</samp></span></dt>
<dd><p>Sets the size of the frames in samples per channel.
</p>
</dd>
<dt><span><samp>lpc_coeff_precision</samp></span></dt>
<dd><p>Sets the LPC coefficient precision, valid values are from 1 to 15, 15 is the
default.
</p>
</dd>
<dt><span><samp>lpc_type</samp></span></dt>
<dd><p>Sets the first stage LPC algorithm
</p><dl compact="compact">
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dd><p>LPC is not used
</p>
</dd>
<dt><span>&lsquo;<samp>fixed</samp>&rsquo;</span></dt>
<dd><p>fixed LPC coefficients
</p>
</dd>
<dt><span>&lsquo;<samp>levinson</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>cholesky</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>lpc_passes</samp></span></dt>
<dd><p>Number of passes to use for Cholesky factorization during LPC analysis
</p>
</dd>
<dt><span><samp>min_partition_order</samp></span></dt>
<dd><p>The minimum partition order
</p>
</dd>
<dt><span><samp>max_partition_order</samp></span></dt>
<dd><p>The maximum partition order
</p>
</dd>
<dt><span><samp>prediction_order_method</samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>estimation</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>2level</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>4level</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>8level</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>search</samp>&rsquo;</span></dt>
<dd><p>Bruteforce search
</p></dd>
<dt><span>&lsquo;<samp>log</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>ch_mode</samp></span></dt>
<dd><p>Channel mode
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>The mode is chosen automatically for each frame
</p></dd>
<dt><span>&lsquo;<samp>indep</samp>&rsquo;</span></dt>
<dd><p>Channels are independently coded
</p></dd>
<dt><span>&lsquo;<samp>left_side</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>right_side</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>mid_side</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>exact_rice_parameters</samp></span></dt>
<dd><p>Chooses if rice parameters are calculated exactly or approximately.
if set to 1 then they are chosen exactly, which slows the code down slightly and
improves compression slightly.
</p>
</dd>
<dt><span><samp>multi_dim_quant</samp></span></dt>
<dd><p>Multi Dimensional Quantization. If set to 1 then a 2nd stage LPC algorithm is
applied after the first stage to finetune the coefficients. This is quite slow
and slightly improves compression.
</p>
</dd>
</dl>

<span id="opusenc"></span><a name="opus"></a>
<h3 class="section">8.4 opus<span class="pull-right"><a class="anchor hidden-xs" href="#opus" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-opus" aria-hidden="true">TOC</a></span></h3>

<p>Opus encoder.
</p>
<p>This is a native FFmpeg encoder for the Opus format. Currently its in development and
only implements the CELT part of the codec. Its quality is usually worse and at best
is equal to the libopus encoder.
</p>
<a name="Options-11"></a>
<h4 class="subsection">8.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-11" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bit rate in bits/s. If unspecified it uses the number of channels and the layout
to make a good guess.
</p>
</dd>
<dt><span><samp>opus_delay</samp></span></dt>
<dd><p>Sets the maximum delay in milliseconds. Lower delays than 20ms will very quickly
decrease quality.
</p></dd>
</dl>

<span id="libfdk_002daac_002denc"></span><a name="libfdk_005faac"></a>
<h3 class="section">8.5 libfdk_aac<span class="pull-right"><a class="anchor hidden-xs" href="#libfdk_005faac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libfdk_005faac" aria-hidden="true">TOC</a></span></h3>

<p>libfdk-aac AAC (Advanced Audio Coding) encoder wrapper.
</p>
<p>The libfdk-aac library is based on the Fraunhofer FDK AAC code from
the Android project.
</p>
<p>Requires the presence of the libfdk-aac headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libfdk-aac</code>. The library is also incompatible with GPL,
so if you allow the use of GPL, you should configure with
<code>--enable-gpl --enable-nonfree --enable-libfdk-aac</code>.
</p>
<p>This encoder has support for the AAC-HE profiles.
</p>
<p>VBR encoding, enabled through the <samp>vbr</samp> or <samp>flags
+qscale</samp> options, is experimental and only works with some
combinations of parameters.
</p>
<p>Support for encoding 7.1 audio is only available with libfdk-aac 0.1.3 or
higher.
</p>
<p>For more information see the fdk-aac project at
<a href="http://sourceforge.net/p/opencore-amr/fdk-aac/">http://sourceforge.net/p/opencore-amr/fdk-aac/</a>.
</p>
<a name="Options-12"></a>
<h4 class="subsection">8.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-12" aria-hidden="true">TOC</a></span></h4>

<p>The following options are mapped on the shared FFmpeg codec options.
</p>
<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bit rate in bits/s. If the bitrate is not explicitly specified, it
is automatically set to a suitable value depending on the selected
profile.
</p>
<p>In case VBR mode is enabled the option is ignored.
</p>
</dd>
<dt><span><samp>ar</samp></span></dt>
<dd><p>Set audio sampling rate (in Hz).
</p>
</dd>
<dt><span><samp>channels</samp></span></dt>
<dd><p>Set the number of audio channels.
</p>
</dd>
<dt><span><samp>flags +qscale</samp></span></dt>
<dd><p>Enable fixed quality, VBR (Variable Bit Rate) mode.
Note that VBR is implicitly enabled when the <samp>vbr</samp> value is
positive.
</p>
</dd>
<dt><span><samp>cutoff</samp></span></dt>
<dd><p>Set cutoff frequency. If not specified (or explicitly set to 0) it
will use a value automatically computed by the library. Default value
is 0.
</p>
</dd>
<dt><span><samp>profile</samp></span></dt>
<dd><p>Set audio profile.
</p>
<p>The following profiles are recognized:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>aac_low</samp>&rsquo;</span></dt>
<dd><p>Low Complexity AAC (LC)
</p>
</dd>
<dt><span>&lsquo;<samp>aac_he</samp>&rsquo;</span></dt>
<dd><p>High Efficiency AAC (HE-AAC)
</p>
</dd>
<dt><span>&lsquo;<samp>aac_he_v2</samp>&rsquo;</span></dt>
<dd><p>High Efficiency AAC version 2 (HE-AACv2)
</p>
</dd>
<dt><span>&lsquo;<samp>aac_ld</samp>&rsquo;</span></dt>
<dd><p>Low Delay AAC (LD)
</p>
</dd>
<dt><span>&lsquo;<samp>aac_eld</samp>&rsquo;</span></dt>
<dd><p>Enhanced Low Delay AAC (ELD)
</p></dd>
</dl>

<p>If not specified it is set to &lsquo;<samp>aac_low</samp>&rsquo;.
</p></dd>
</dl>

<p>The following are private options of the libfdk_aac encoder.
</p>
<dl compact="compact">
<dt><span><samp>afterburner</samp></span></dt>
<dd><p>Enable afterburner feature if set to 1, disabled if set to 0. This
improves the quality but also the required processing power.
</p>
<p>Default value is 1.
</p>
</dd>
<dt><span><samp>eld_sbr</samp></span></dt>
<dd><p>Enable SBR (Spectral Band Replication) for ELD if set to 1, disabled
if set to 0.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><span><samp>eld_v2</samp></span></dt>
<dd><p>Enable ELDv2 (LD-MPS extension for ELD stereo signals) for ELDv2 if set to 1,
disabled if set to 0.
</p>
<p>Note that option is available when fdk-aac version (AACENCODER_LIB_VL0.AACENCODER_LIB_VL1.AACENCODER_LIB_VL2) &gt; (4.0.0).
</p>
<p>Default value is 0.
</p>
</dd>
<dt><span><samp>signaling</samp></span></dt>
<dd><p>Set SBR/PS signaling style.
</p>
<p>It can assume one of the following values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>default</samp>&rsquo;</span></dt>
<dd><p>choose signaling implicitly (explicit hierarchical by default,
implicit if global header is disabled)
</p>
</dd>
<dt><span>&lsquo;<samp>implicit</samp>&rsquo;</span></dt>
<dd><p>implicit backwards compatible signaling
</p>
</dd>
<dt><span>&lsquo;<samp>explicit_sbr</samp>&rsquo;</span></dt>
<dd><p>explicit SBR, implicit PS signaling
</p>
</dd>
<dt><span>&lsquo;<samp>explicit_hierarchical</samp>&rsquo;</span></dt>
<dd><p>explicit hierarchical signaling
</p></dd>
</dl>

<p>Default value is &lsquo;<samp>default</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>latm</samp></span></dt>
<dd><p>Output LATM/LOAS encapsulated data if set to 1, disabled if set to 0.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><span><samp>header_period</samp></span></dt>
<dd><p>Set StreamMuxConfig and PCE repetition period (in frames) for sending
in-band configuration buffers within LATM/LOAS transport layer.
</p>
<p>Must be a 16-bits non-negative integer.
</p>
<p>Default value is 0.
</p>
</dd>
<dt><span><samp>vbr</samp></span></dt>
<dd><p>Set VBR mode, from 1 to 5. 1 is lowest quality (though still pretty
good) and 5 is highest quality. A value of 0 will disable VBR, and CBR
(Constant Bit Rate) is enabled.
</p>
<p>Currently only the &lsquo;<samp>aac_low</samp>&rsquo; profile supports VBR encoding.
</p>
<p>VBR modes 1-5 correspond to roughly the following average bit rates:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>1</samp>&rsquo;</span></dt>
<dd><p>32 kbps/channel
</p></dd>
<dt><span>&lsquo;<samp>2</samp>&rsquo;</span></dt>
<dd><p>40 kbps/channel
</p></dd>
<dt><span>&lsquo;<samp>3</samp>&rsquo;</span></dt>
<dd><p>48-56 kbps/channel
</p></dd>
<dt><span>&lsquo;<samp>4</samp>&rsquo;</span></dt>
<dd><p>64 kbps/channel
</p></dd>
<dt><span>&lsquo;<samp>5</samp>&rsquo;</span></dt>
<dd><p>about 80-96 kbps/channel
</p></dd>
</dl>

<p>Default value is 0.
</p></dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">8.5.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Use <code>ffmpeg</code> to convert an audio file to VBR AAC in an M4A (MP4)
container:
<div class="example">
<pre class="example">ffmpeg -i input.wav -codec:a libfdk_aac -vbr 3 output.m4a
</pre></div>

</li><li> Use <code>ffmpeg</code> to convert an audio file to CBR 64k kbps AAC, using the
High-Efficiency AAC profile:
<div class="example">
<pre class="example">ffmpeg -i input.wav -c:a libfdk_aac -profile:a aac_he -b:a 64k output.m4a
</pre></div>
</li></ul>

<span id="libmp3lame"></span><a name="libmp3lame-1"></a>
<h3 class="section">8.6 libmp3lame<span class="pull-right"><a class="anchor hidden-xs" href="#libmp3lame-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libmp3lame-1" aria-hidden="true">TOC</a></span></h3>

<p>LAME (Lame Ain&rsquo;t an MP3 Encoder) MP3 encoder wrapper.
</p>
<p>Requires the presence of the libmp3lame headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libmp3lame</code>.
</p>
<p>See <a href="#libshine">libshine</a> for a fixed-point MP3 encoder, although with a
lower quality.
</p>
<a name="Options-13"></a>
<h4 class="subsection">8.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-13" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libmp3lame wrapper. The
<code>lame</code>-equivalent of the options are listed in parentheses.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>-b</em>)</samp></span></dt>
<dd><p>Set bitrate expressed in bits/s for CBR or ABR. LAME <code>bitrate</code> is
expressed in kilobits/s.
</p>
</dd>
<dt><span><samp>q (<em>-V</em>)</samp></span></dt>
<dd><p>Set constant quality setting for VBR. This option is valid only
using the <code>ffmpeg</code> command-line tool. For library interface
users, use <samp>global_quality</samp>.
</p>
</dd>
<dt><span><samp>compression_level (<em>-q</em>)</samp></span></dt>
<dd><p>Set algorithm quality. Valid arguments are integers in the 0-9 range,
with 0 meaning highest quality but slowest, and 9 meaning fastest
while producing the worst quality.
</p>
</dd>
<dt><span><samp>cutoff (<em>--lowpass</em>)</samp></span></dt>
<dd><p>Set lowpass cutoff frequency. If unspecified, the encoder dynamically
adjusts the cutoff.
</p>
</dd>
<dt><span><samp>reservoir</samp></span></dt>
<dd><p>Enable use of bit reservoir when set to 1. Default value is 1. LAME
has this enabled by default, but can be overridden by use
<samp>--nores</samp> option.
</p>
</dd>
<dt><span><samp>joint_stereo (<em>-m j</em>)</samp></span></dt>
<dd><p>Enable the encoder to use (on a frame by frame basis) either L/R
stereo or mid/side stereo. Default value is 1.
</p>
</dd>
<dt><span><samp>abr (<em>--abr</em>)</samp></span></dt>
<dd><p>Enable the encoder to use ABR when set to 1. The <code>lame</code>
<samp>--abr</samp> sets the target bitrate, while this options only
tells FFmpeg to use ABR still relies on <samp>b</samp> to set bitrate.
</p>
</dd>
</dl>

<a name="libopencore_002damrnb-1"></a>
<h3 class="section">8.7 libopencore-amrnb<span class="pull-right"><a class="anchor hidden-xs" href="#libopencore_002damrnb-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopencore_002damrnb-1" aria-hidden="true">TOC</a></span></h3>

<p>OpenCORE Adaptive Multi-Rate Narrowband encoder.
</p>
<p>Requires the presence of the libopencore-amrnb headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libopencore-amrnb --enable-version3</code>.
</p>
<p>This is a mono-only encoder. Officially it only supports 8000Hz sample rate,
but you can override it by setting <samp>strict</samp> to &lsquo;<samp>unofficial</samp>&rsquo; or
lower.
</p>
<a name="Options-14"></a>
<h4 class="subsection">8.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-14" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bitrate in bits per second. Only the following bitrates are supported,
otherwise libavcodec will round to the nearest valid bitrate.
</p>
<dl compact="compact">
<dt><span><samp>4750</samp></span></dt>
<dt><span><samp>5150</samp></span></dt>
<dt><span><samp>5900</samp></span></dt>
<dt><span><samp>6700</samp></span></dt>
<dt><span><samp>7400</samp></span></dt>
<dt><span><samp>7950</samp></span></dt>
<dt><span><samp>10200</samp></span></dt>
<dt><span><samp>12200</samp></span></dt>
</dl>

</dd>
<dt><span><samp>dtx</samp></span></dt>
<dd><p>Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
</p>
</dd>
</dl>

<a name="libopus-1"></a>
<h3 class="section">8.8 libopus<span class="pull-right"><a class="anchor hidden-xs" href="#libopus-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopus-1" aria-hidden="true">TOC</a></span></h3>

<p>libopus Opus Interactive Audio Codec encoder wrapper.
</p>
<p>Requires the presence of the libopus headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libopus</code>.
</p>
<a name="Option-Mapping"></a>
<h4 class="subsection">8.8.1 Option Mapping<span class="pull-right"><a class="anchor hidden-xs" href="#Option-Mapping" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Option-Mapping" aria-hidden="true">TOC</a></span></h4>

<p>Most libopus options are modelled after the <code>opusenc</code> utility from
opus-tools. The following is an option mapping chart describing options
supported by the libopus wrapper, and their <code>opusenc</code>-equivalent
in parentheses.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>bitrate</em>)</samp></span></dt>
<dd><p>Set the bit rate in bits/s.  FFmpeg&rsquo;s <samp>b</samp> option is
expressed in bits/s, while <code>opusenc</code>&rsquo;s <samp>bitrate</samp> in
kilobits/s.
</p>
</dd>
<dt><span><samp>vbr (<em>vbr</em>, <em>hard-cbr</em>, and <em>cvbr</em>)</samp></span></dt>
<dd><p>Set VBR mode. The FFmpeg <samp>vbr</samp> option has the following
valid arguments, with the <code>opusenc</code> equivalent options
in parentheses:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>off (<em>hard-cbr</em>)</samp>&rsquo;</span></dt>
<dd><p>Use constant bit rate encoding.
</p>
</dd>
<dt><span>&lsquo;<samp>on (<em>vbr</em>)</samp>&rsquo;</span></dt>
<dd><p>Use variable bit rate encoding (the default).
</p>
</dd>
<dt><span>&lsquo;<samp>constrained (<em>cvbr</em>)</samp>&rsquo;</span></dt>
<dd><p>Use constrained variable bit rate encoding.
</p></dd>
</dl>

</dd>
<dt><span><samp>compression_level (<em>comp</em>)</samp></span></dt>
<dd><p>Set encoding algorithm complexity. Valid options are integers in
the 0-10 range. 0 gives the fastest encodes but lower quality, while 10
gives the highest quality but slowest encoding. The default is 10.
</p>
</dd>
<dt><span><samp>frame_duration (<em>framesize</em>)</samp></span></dt>
<dd><p>Set maximum frame size, or duration of a frame in milliseconds. The
argument must be exactly the following: 2.5, 5, 10, 20, 40, 60. Smaller
frame sizes achieve lower latency but less quality at a given bitrate.
Sizes greater than 20ms are only interesting at fairly low bitrates.
The default is 20ms.
</p>
</dd>
<dt><span><samp>packet_loss (<em>expect-loss</em>)</samp></span></dt>
<dd><p>Set expected packet loss percentage. The default is 0.
</p>
</dd>
<dt><span><samp>fec (<em>n/a</em>)</samp></span></dt>
<dd><p>Enable inband forward error correction. <samp>packet_loss</samp> must be non-zero
to take advantage - frequency of FEC &rsquo;side-data&rsquo; is proportional to expected packet loss.
Default is disabled.
</p>
</dd>
<dt><span><samp>application (N.A.)</samp></span></dt>
<dd><p>Set intended application type. Valid options are listed below:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>voip</samp>&rsquo;</span></dt>
<dd><p>Favor improved speech intelligibility.
</p></dd>
<dt><span>&lsquo;<samp>audio</samp>&rsquo;</span></dt>
<dd><p>Favor faithfulness to the input (the default).
</p></dd>
<dt><span>&lsquo;<samp>lowdelay</samp>&rsquo;</span></dt>
<dd><p>Restrict to only the lowest delay modes.
</p></dd>
</dl>

</dd>
<dt><span><samp>cutoff (N.A.)</samp></span></dt>
<dd><p>Set cutoff bandwidth in Hz. The argument must be exactly one of the
following: 4000, 6000, 8000, 12000, or 20000, corresponding to
narrowband, mediumband, wideband, super wideband, and fullband
respectively. The default is 0 (cutoff disabled).
</p>
</dd>
<dt><span><samp>mapping_family (<em>mapping_family</em>)</samp></span></dt>
<dd><p>Set channel mapping family to be used by the encoder. The default value of -1
uses mapping family 0 for mono and stereo inputs, and mapping family 1
otherwise. The default also disables the surround masking and LFE bandwidth
optimzations in libopus, and requires that the input contains 8 channels or
fewer.
</p>
<p>Other values include 0 for mono and stereo, 1 for surround sound with masking
and LFE bandwidth optimizations, and 255 for independent streams with an
unspecified channel layout.
</p>
</dd>
<dt><span><samp>apply_phase_inv (N.A.) (requires libopus &gt;= 1.2)</samp></span></dt>
<dd><p>If set to 0, disables the use of phase inversion for intensity stereo,
improving the quality of mono downmixes, but slightly reducing normal stereo
quality. The default is 1 (phase inversion enabled).
</p>
</dd>
</dl>

<span id="libshine"></span><a name="libshine-1"></a>
<h3 class="section">8.9 libshine<span class="pull-right"><a class="anchor hidden-xs" href="#libshine-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libshine-1" aria-hidden="true">TOC</a></span></h3>

<p>Shine Fixed-Point MP3 encoder wrapper.
</p>
<p>Shine is a fixed-point MP3 encoder. It has a far better performance on
platforms without an FPU, e.g. armel CPUs, and some phones and tablets.
However, as it is more targeted on performance than quality, it is not on par
with LAME and other production-grade encoders quality-wise. Also, according to
the project&rsquo;s homepage, this encoder may not be free of bugs as the code was
written a long time ago and the project was dead for at least 5 years.
</p>
<p>This encoder only supports stereo and mono input. This is also CBR-only.
</p>
<p>The original project (last updated in early 2007) is at
<a href="http://sourceforge.net/projects/libshine-fxp/">http://sourceforge.net/projects/libshine-fxp/</a>. We only support the
updated fork by the Savonet/Liquidsoap project at <a href="https://github.com/savonet/shine">https://github.com/savonet/shine</a>.
</p>
<p>Requires the presence of the libshine headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libshine</code>.
</p>
<p>See also <a href="#libmp3lame">libmp3lame</a>.
</p>
<a name="Options-15"></a>
<h4 class="subsection">8.9.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-15" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libshine wrapper. The
<code>shineenc</code>-equivalent of the options are listed in parentheses.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>-b</em>)</samp></span></dt>
<dd><p>Set bitrate expressed in bits/s for CBR. <code>shineenc</code> <samp>-b</samp> option
is expressed in kilobits/s.
</p>
</dd>
</dl>

<a name="libtwolame"></a>
<h3 class="section">8.10 libtwolame<span class="pull-right"><a class="anchor hidden-xs" href="#libtwolame" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libtwolame" aria-hidden="true">TOC</a></span></h3>

<p>TwoLAME MP2 encoder wrapper.
</p>
<p>Requires the presence of the libtwolame headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libtwolame</code>.
</p>
<a name="Options-16"></a>
<h4 class="subsection">8.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-16" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libtwolame wrapper. The
<code>twolame</code>-equivalent options follow the FFmpeg ones and are in
parentheses.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>-b</em>)</samp></span></dt>
<dd><p>Set bitrate expressed in bits/s for CBR. <code>twolame</code> <samp>b</samp>
option is expressed in kilobits/s. Default value is 128k.
</p>
</dd>
<dt><span><samp>q (<em>-V</em>)</samp></span></dt>
<dd><p>Set quality for experimental VBR support. Maximum value range is
from -50 to 50, useful range is from -10 to 10. The higher the
value, the better the quality. This option is valid only using the
<code>ffmpeg</code> command-line tool. For library interface users,
use <samp>global_quality</samp>.
</p>
</dd>
<dt><span><samp>mode (<em>--mode</em>)</samp></span></dt>
<dd><p>Set the mode of the resulting audio. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Choose mode automatically based on the input. This is the default.
</p></dd>
<dt><span>&lsquo;<samp>stereo</samp>&rsquo;</span></dt>
<dd><p>Stereo
</p></dd>
<dt><span>&lsquo;<samp>joint_stereo</samp>&rsquo;</span></dt>
<dd><p>Joint stereo
</p></dd>
<dt><span>&lsquo;<samp>dual_channel</samp>&rsquo;</span></dt>
<dd><p>Dual channel
</p></dd>
<dt><span>&lsquo;<samp>mono</samp>&rsquo;</span></dt>
<dd><p>Mono
</p></dd>
</dl>

</dd>
<dt><span><samp>psymodel (<em>--psyc-mode</em>)</samp></span></dt>
<dd><p>Set psychoacoustic model to use in encoding. The argument must be
an integer between -1 and 4, inclusive. The higher the value, the
better the quality. The default value is 3.
</p>
</dd>
<dt><span><samp>energy_levels (<em>--energy</em>)</samp></span></dt>
<dd><p>Enable energy levels extensions when set to 1. The default value is
0 (disabled).
</p>
</dd>
<dt><span><samp>error_protection (<em>--protect</em>)</samp></span></dt>
<dd><p>Enable CRC error protection when set to 1. The default value is 0
(disabled).
</p>
</dd>
<dt><span><samp>copyright (<em>--copyright</em>)</samp></span></dt>
<dd><p>Set MPEG audio copyright flag when set to 1. The default value is 0
(disabled).
</p>
</dd>
<dt><span><samp>original (<em>--original</em>)</samp></span></dt>
<dd><p>Set MPEG audio original flag when set to 1. The default value is 0
(disabled).
</p>
</dd>
</dl>

<a name="libvo_002damrwbenc"></a>
<h3 class="section">8.11 libvo-amrwbenc<span class="pull-right"><a class="anchor hidden-xs" href="#libvo_002damrwbenc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libvo_002damrwbenc" aria-hidden="true">TOC</a></span></h3>

<p>VisualOn Adaptive Multi-Rate Wideband encoder.
</p>
<p>Requires the presence of the libvo-amrwbenc headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libvo-amrwbenc --enable-version3</code>.
</p>
<p>This is a mono-only encoder. Officially it only supports 16000Hz sample
rate, but you can override it by setting <samp>strict</samp> to
&lsquo;<samp>unofficial</samp>&rsquo; or lower.
</p>
<a name="Options-17"></a>
<h4 class="subsection">8.11.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-17" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bitrate in bits/s. Only the following bitrates are supported, otherwise
libavcodec will round to the nearest valid bitrate.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>6600</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>8850</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>12650</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>14250</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>15850</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>18250</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>19850</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>23050</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>23850</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>dtx</samp></span></dt>
<dd><p>Allow discontinuous transmission (generate comfort noise) when set to 1. The
default value is 0 (disabled).
</p>
</dd>
</dl>

<a name="libvorbis"></a>
<h3 class="section">8.12 libvorbis<span class="pull-right"><a class="anchor hidden-xs" href="#libvorbis" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libvorbis" aria-hidden="true">TOC</a></span></h3>

<p>libvorbis encoder wrapper.
</p>
<p>Requires the presence of the libvorbisenc headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libvorbis</code>.
</p>
<a name="Options-18"></a>
<h4 class="subsection">8.12.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-18" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-18" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libvorbis wrapper. The
<code>oggenc</code>-equivalent of the options are listed in parentheses.
</p>
<p>To get a more accurate and extensive documentation of the libvorbis
options, consult the libvorbisenc&rsquo;s and <code>oggenc</code>&rsquo;s documentations.
See <a href="http://xiph.org/vorbis/">http://xiph.org/vorbis/</a>,
<a href="http://wiki.xiph.org/Vorbis-tools">http://wiki.xiph.org/Vorbis-tools</a>, and oggenc(1).
</p>
<dl compact="compact">
<dt><span><samp>b (<em>-b</em>)</samp></span></dt>
<dd><p>Set bitrate expressed in bits/s for ABR. <code>oggenc</code> <samp>-b</samp> is
expressed in kilobits/s.
</p>
</dd>
<dt><span><samp>q (<em>-q</em>)</samp></span></dt>
<dd><p>Set constant quality setting for VBR. The value should be a float
number in the range of -1.0 to 10.0. The higher the value, the better
the quality. The default value is &lsquo;<samp>3.0</samp>&rsquo;.
</p>
<p>This option is valid only using the <code>ffmpeg</code> command-line tool.
For library interface users, use <samp>global_quality</samp>.
</p>
</dd>
<dt><span><samp>cutoff (<em>--advanced-encode-option lowpass_frequency=N</em>)</samp></span></dt>
<dd><p>Set cutoff bandwidth in Hz, a value of 0 disables cutoff. <code>oggenc</code>&rsquo;s
related option is expressed in kHz. The default value is &lsquo;<samp>0</samp>&rsquo; (cutoff
disabled).
</p>
</dd>
<dt><span><samp>minrate (<em>-m</em>)</samp></span></dt>
<dd><p>Set minimum bitrate expressed in bits/s. <code>oggenc</code> <samp>-m</samp> is
expressed in kilobits/s.
</p>
</dd>
<dt><span><samp>maxrate (<em>-M</em>)</samp></span></dt>
<dd><p>Set maximum bitrate expressed in bits/s. <code>oggenc</code> <samp>-M</samp> is
expressed in kilobits/s. This only has effect on ABR mode.
</p>
</dd>
<dt><span><samp>iblock (<em>--advanced-encode-option impulse_noisetune=N</em>)</samp></span></dt>
<dd><p>Set noise floor bias for impulse blocks. The value is a float number from
-15.0 to 0.0. A negative bias instructs the encoder to pay special attention
to the crispness of transients in the encoded audio. The tradeoff for better
transient response is a higher bitrate.
</p>
</dd>
</dl>

<span id="mjpegenc"></span><a name="mjpeg"></a>
<h3 class="section">8.13 mjpeg<span class="pull-right"><a class="anchor hidden-xs" href="#mjpeg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mjpeg" aria-hidden="true">TOC</a></span></h3>

<p>Motion JPEG encoder.
</p>
<a name="Options-19"></a>
<h4 class="subsection">8.13.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-19" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-19" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>huffman</samp></span></dt>
<dd><p>Set the huffman encoding strategy. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>default</samp>&rsquo;</span></dt>
<dd><p>Use the default huffman tables. This is the default strategy.
</p>
</dd>
<dt><span>&lsquo;<samp>optimal</samp>&rsquo;</span></dt>
<dd><p>Compute and use optimal huffman tables.
</p>
</dd>
</dl>
</dd>
</dl>

<span id="wavpackenc"></span><a name="wavpack"></a>
<h3 class="section">8.14 wavpack<span class="pull-right"><a class="anchor hidden-xs" href="#wavpack" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-wavpack" aria-hidden="true">TOC</a></span></h3>

<p>WavPack lossless audio encoder.
</p>
<a name="Options-20"></a>
<h4 class="subsection">8.14.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-20" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-20" aria-hidden="true">TOC</a></span></h4>

<p>The equivalent options for <code>wavpack</code> command line utility are listed in
parentheses.
</p>
<a name="Shared-options"></a>
<h4 class="subsubsection">8.14.1.1 Shared options<span class="pull-right"><a class="anchor hidden-xs" href="#Shared-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Shared-options" aria-hidden="true">TOC</a></span></h4>

<p>The following shared options are effective for this encoder. Only special notes
about this particular encoder will be documented here. For the general meaning
of the options, see <a href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<dl compact="compact">
<dt><span><samp>frame_size (<em>--blocksize</em>)</samp></span></dt>
<dd><p>For this encoder, the range for this option is between 128 and 131072. Default
is automatically decided based on sample rate and number of channel.
</p>
<p>For the complete formula of calculating default, see
<samp>libavcodec/wavpackenc.c</samp>.
</p>
</dd>
<dt><span><samp>compression_level (<em>-f</em>, <em>-h</em>, <em>-hh</em>, and <em>-x</em>)</samp></span></dt>
</dl>

<a name="Private-options"></a>
<h4 class="subsubsection">8.14.1.2 Private options<span class="pull-right"><a class="anchor hidden-xs" href="#Private-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Private-options" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>joint_stereo (<em>-j</em>)</samp></span></dt>
<dd><p>Set whether to enable joint stereo. Valid values are:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>on (<em>1</em>)</samp>&rsquo;</span></dt>
<dd><p>Force mid/side audio encoding.
</p></dd>
<dt><span>&lsquo;<samp>off (<em>0</em>)</samp>&rsquo;</span></dt>
<dd><p>Force left/right audio encoding.
</p></dd>
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Let the encoder decide automatically.
</p></dd>
</dl>

</dd>
<dt><span><samp>optimize_mono</samp></span></dt>
<dd><p>Set whether to enable optimization for mono. This option is only effective for
non-mono streams. Available values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>on</samp>&rsquo;</span></dt>
<dd><p>enabled
</p></dd>
<dt><span>&lsquo;<samp>off</samp>&rsquo;</span></dt>
<dd><p>disabled
</p></dd>
</dl>

</dd>
</dl>


<a name="Video-Encoders"></a>
<h2 class="chapter">9 Video Encoders<span class="pull-right"><a class="anchor hidden-xs" href="#Video-Encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Video-Encoders" aria-hidden="true">TOC</a></span></h2>

<p>A description of some of the currently available video encoders
follows.
</p>
<a name="a64_005fmulti_002c-a64_005fmulti5"></a>
<h3 class="section">9.1 a64_multi, a64_multi5<span class="pull-right"><a class="anchor hidden-xs" href="#a64_005fmulti_002c-a64_005fmulti5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-a64_005fmulti_002c-a64_005fmulti5" aria-hidden="true">TOC</a></span></h3>

<p>A64 / Commodore 64 multicolor charset encoder. <code>a64_multi5</code> is extended with 5th color (colram).
</p>
<a name="Cinepak"></a>
<h3 class="section">9.2 Cinepak<span class="pull-right"><a class="anchor hidden-xs" href="#Cinepak" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Cinepak" aria-hidden="true">TOC</a></span></h3>

<p>Cinepak aka CVID encoder.
Compatible with Windows 3.1 and vintage MacOS.
</p>
<a name="Options-21"></a>
<h4 class="subsection">9.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-21" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-21" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>g <var>integer</var></samp></span></dt>
<dd><p>Keyframe interval.
A keyframe is inserted at least every <code>-g</code> frames, sometimes sooner.
</p>
</dd>
<dt><span><samp>q:v <var>integer</var></samp></span></dt>
<dd><p>Quality factor. Lower is better. Higher gives lower bitrate.
The following table lists bitrates when encoding akiyo_cif.y4m for various values of <code>-q:v</code> with <code>-g 100</code>:
</p>
<dl compact="compact">
<dt><span><samp><code>-q:v 1</code> 1918 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 2</code> 1735 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 4</code> 1500 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 10</code> 1041 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 20</code> 826 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 40</code> 553 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 100</code> 394 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 200</code> 312 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 400</code> 266 kb/s</samp></span></dt>
<dt><span><samp><code>-q:v 1000</code> 237 kb/s</samp></span></dt>
</dl>

</dd>
<dt><span><samp>max_extra_cb_iterations <var>integer</var></samp></span></dt>
<dd><p>Max extra codebook recalculation passes, more is better and slower.
</p>
</dd>
<dt><span><samp>skip_empty_cb <var>boolean</var></samp></span></dt>
<dd><p>Avoid wasting bytes, ignore vintage MacOS decoder.
</p>
</dd>
<dt><span><samp>max_strips <var>integer</var></samp></span></dt>
<dt><span><samp>min_strips <var>integer</var></samp></span></dt>
<dd><p>The minimum and maximum number of strips to use.
Wider range sometimes improves quality.
More strips is generally better quality but costs more bits.
Fewer strips tend to yield more keyframes.
Vintage compatible is 1..3.
</p>
</dd>
<dt><span><samp>strip_number_adaptivity <var>integer</var></samp></span></dt>
<dd><p>How much number of strips is allowed to change between frames.
Higher is better but slower.
</p>
</dd>
</dl>

<a name="GIF"></a>
<h3 class="section">9.3 GIF<span class="pull-right"><a class="anchor hidden-xs" href="#GIF" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-GIF" aria-hidden="true">TOC</a></span></h3>

<p>GIF image/animation encoder.
</p>
<a name="Options-22"></a>
<h4 class="subsection">9.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-22" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-22" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>gifflags <var>integer</var></samp></span></dt>
<dd><p>Sets the flags used for GIF encoding.
</p>
<dl compact="compact">
<dt><span><samp>offsetting</samp></span></dt>
<dd><p>Enables picture offsetting.
</p>
<p>Default is enabled.
</p>
</dd>
<dt><span><samp>transdiff</samp></span></dt>
<dd><p>Enables transparency detection between frames.
</p>
<p>Default is enabled.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>gifimage <var>integer</var></samp></span></dt>
<dd><p>Enables encoding one full GIF image per frame, rather than an animated GIF.
</p>
<p>Default value is <samp>0</samp>.
</p>
</dd>
<dt><span><samp>global_palette <var>integer</var></samp></span></dt>
<dd><p>Writes a palette to the global GIF header where feasible.
</p>
<p>If disabled, every frame will always have a palette written, even if there
is a global palette supplied.
</p>
<p>Default value is <samp>1</samp>.
</p>
</dd>
</dl>

<a name="Hap"></a>
<h3 class="section">9.4 Hap<span class="pull-right"><a class="anchor hidden-xs" href="#Hap" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Hap" aria-hidden="true">TOC</a></span></h3>

<p>Vidvox Hap video encoder.
</p>
<a name="Options-23"></a>
<h4 class="subsection">9.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-23" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-23" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>format <var>integer</var></samp></span></dt>
<dd><p>Specifies the Hap format to encode.
</p>
<dl compact="compact">
<dt><span><samp>hap</samp></span></dt>
<dt><span><samp>hap_alpha</samp></span></dt>
<dt><span><samp>hap_q</samp></span></dt>
</dl>

<p>Default value is <samp>hap</samp>.
</p>
</dd>
<dt><span><samp>chunks <var>integer</var></samp></span></dt>
<dd><p>Specifies the number of chunks to split frames into, between 1 and 64. This
permits multithreaded decoding of large frames, potentially at the cost of
data-rate. The encoder may modify this value to divide frames evenly.
</p>
<p>Default value is <var>1</var>.
</p>
</dd>
<dt><span><samp>compressor <var>integer</var></samp></span></dt>
<dd><p>Specifies the second-stage compressor to use. If set to <samp>none</samp>,
<samp>chunks</samp> will be limited to 1, as chunked uncompressed frames offer no
benefit.
</p>
<dl compact="compact">
<dt><span><samp>none</samp></span></dt>
<dt><span><samp>snappy</samp></span></dt>
</dl>

<p>Default value is <samp>snappy</samp>.
</p>
</dd>
</dl>

<a name="jpeg2000"></a>
<h3 class="section">9.5 jpeg2000<span class="pull-right"><a class="anchor hidden-xs" href="#jpeg2000" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-jpeg2000" aria-hidden="true">TOC</a></span></h3>

<p>The native jpeg 2000 encoder is lossy by default, the <code>-q:v</code>
option can be used to set the encoding quality. Lossless encoding
can be selected with <code>-pred 1</code>.
</p>
<a name="Options-24"></a>
<h4 class="subsection">9.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-24" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-24" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>format <var>integer</var></samp></span></dt>
<dd><p>Can be set to either <code>j2k</code> or <code>jp2</code> (the default) that
makes it possible to store non-rgb pix_fmts.
</p>
</dd>
<dt><span><samp>tile_width <var>integer</var></samp></span></dt>
<dd><p>Sets tile width. Range is 1 to 1073741824. Default is 256.
</p>
</dd>
<dt><span><samp>tile_height <var>integer</var></samp></span></dt>
<dd><p>Sets tile height. Range is 1 to 1073741824. Default is 256.
</p>
</dd>
<dt><span><samp>pred <var>integer</var></samp></span></dt>
<dd><p>Allows setting the discrete wavelet transform (DWT) type
</p><dl compact="compact">
<dt><span><samp>dwt97int (Lossy)</samp></span></dt>
<dt><span><samp>dwt53 (Lossless)</samp></span></dt>
</dl>
<p>Default is <code>dwt97int</code>
</p>
</dd>
<dt><span><samp>sop <var>boolean</var></samp></span></dt>
<dd><p>Enable this to add SOP marker at the start of each packet. Disabled by default.
</p>
</dd>
<dt><span><samp>eph <var>boolean</var></samp></span></dt>
<dd><p>Enable this to add EPH marker at the end of each packet header. Disabled by default.
</p>
</dd>
<dt><span><samp>prog <var>integer</var></samp></span></dt>
<dd><p>Sets the progression order to be used by the encoder.
Possible values are:
</p><dl compact="compact">
<dt><span><samp>lrcp</samp></span></dt>
<dt><span><samp>rlcp</samp></span></dt>
<dt><span><samp>rpcl</samp></span></dt>
<dt><span><samp>pcrl</samp></span></dt>
<dt><span><samp>cprl</samp></span></dt>
</dl>
<p>Set to <code>lrcp</code> by default.
</p>
</dd>
<dt><span><samp>layer_rates <var>string</var></samp></span></dt>
<dd><p>By default, when this option is not used, compression is done using the quality metric.
This option allows for compression using compression ratio. The compression ratio for each
level could be specified. The compression ratio of a layer <code>l</code> species the what ratio of
total file size is contained in the first <code>l</code> layers.
</p>
<p>Example usage:
</p>
<div class="example">
<pre class="example">ffmpeg -i input.bmp -c:v jpeg2000 -layer_rates &quot;100,10,1&quot; output.j2k
</pre></div>

<p>This would compress the image to contain 3 layers, where the data contained in the
first layer would be compressed by 1000 times, compressed by 100 in the first two layers,
and shall contain all data while using all 3 layers.
</p>
</dd>
</dl>

<a name="librav1e"></a>
<h3 class="section">9.6 librav1e<span class="pull-right"><a class="anchor hidden-xs" href="#librav1e" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-librav1e" aria-hidden="true">TOC</a></span></h3>

<p>rav1e AV1 encoder wrapper.
</p>
<p>Requires the presence of the rav1e headers and library during configuration.
You need to explicitly configure the build with <code>--enable-librav1e</code>.
</p>
<a name="Options-25"></a>
<h4 class="subsection">9.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-25" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-25" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>qmax</samp></span></dt>
<dd><p>Sets the maximum quantizer to use when using bitrate mode.
</p>
</dd>
<dt><span><samp>qmin</samp></span></dt>
<dd><p>Sets the minimum quantizer to use when using bitrate mode.
</p>
</dd>
<dt><span><samp>qp</samp></span></dt>
<dd><p>Uses quantizer mode to encode at the given quantizer (0-255).
</p>
</dd>
<dt><span><samp>speed</samp></span></dt>
<dd><p>Selects the speed preset (0-10) to encode with.
</p>
</dd>
<dt><span><samp>tiles</samp></span></dt>
<dd><p>Selects how many tiles to encode with.
</p>
</dd>
<dt><span><samp>tile-rows</samp></span></dt>
<dd><p>Selects how many rows of tiles to encode with.
</p>
</dd>
<dt><span><samp>tile-columns</samp></span></dt>
<dd><p>Selects how many columns of tiles to encode with.
</p>
</dd>
<dt><span><samp>rav1e-params</samp></span></dt>
<dd><p>Set rav1e options using a list of <var>key</var>=<var>value</var> pairs separated
by &quot;:&quot;. See <code>rav1e --help</code> for a list of options.
</p>
<p>For example to specify librav1e encoding options with <samp>-rav1e-params</samp>:
</p>
<div class="example">
<pre class="example">ffmpeg -i input -c:v librav1e -b:v 500K -rav1e-params speed=5:low_latency=true output.mp4
</pre></div>

</dd>
</dl>

<a name="libaom_002dav1"></a>
<h3 class="section">9.7 libaom-av1<span class="pull-right"><a class="anchor hidden-xs" href="#libaom_002dav1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libaom_002dav1" aria-hidden="true">TOC</a></span></h3>

<p>libaom AV1 encoder wrapper.
</p>
<p>Requires the presence of the libaom headers and library during
configuration.  You need to explicitly configure the build with
<code>--enable-libaom</code>.
</p>
<a name="Options-26"></a>
<h4 class="subsection">9.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-26" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-26" aria-hidden="true">TOC</a></span></h4>

<p>The wrapper supports the following standard libavcodec options:
</p>
<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set bitrate target in bits/second.  By default this will use
variable-bitrate mode.  If <samp>maxrate</samp> and <samp>minrate</samp> are
also set to the same value then it will use constant-bitrate mode,
otherwise if <samp>crf</samp> is set as well then it will use
constrained-quality mode.
</p>
</dd>
<dt><span><samp>g keyint_min</samp></span></dt>
<dd><p>Set key frame placement.  The GOP size sets the maximum distance between
key frames; if zero the output stream will be intra-only.  The minimum
distance is ignored unless it is the same as the GOP size, in which case
key frames will always appear at a fixed interval.  Not set by default,
so without this option the library has completely free choice about
where to place key frames.
</p>
</dd>
<dt><span><samp>qmin qmax</samp></span></dt>
<dd><p>Set minimum/maximum quantisation values.  Valid range is from 0 to 63
(warning: this does not match the quantiser values actually used by AV1
- divide by four to map real quantiser values to this range).  Defaults
to min/max (no constraint).
</p>
</dd>
<dt><span><samp>minrate maxrate bufsize rc_init_occupancy</samp></span></dt>
<dd><p>Set rate control buffering parameters.  Not used if not set - defaults
to unconstrained variable bitrate.
</p>
</dd>
<dt><span><samp>threads</samp></span></dt>
<dd><p>Set the number of threads to use while encoding.  This may require the
<samp>tiles</samp> or <samp>row-mt</samp> options to also be set to actually
use the specified number of threads fully. Defaults to the number of
hardware threads supported by the host machine.
</p>
</dd>
<dt><span><samp>profile</samp></span></dt>
<dd><p>Set the encoding profile.  Defaults to using the profile which matches
the bit depth and chroma subsampling of the input.
</p>
</dd>
</dl>

<p>The wrapper also has some specific options:
</p>
<dl compact="compact">
<dt><span><samp>cpu-used</samp></span></dt>
<dd><p>Set the quality/encoding speed tradeoff.  Valid range is from 0 to 8,
higher numbers indicating greater speed and lower quality.  The default
value is 1, which will be slow and high quality.
</p>
</dd>
<dt><span><samp>auto-alt-ref</samp></span></dt>
<dd><p>Enable use of alternate reference frames.  Defaults to the internal
default of the library.
</p>
</dd>
<dt><span><samp>arnr-max-frames (<em>frames</em>)</samp></span></dt>
<dd><p>Set altref noise reduction max frame count. Default is -1.
</p>
</dd>
<dt><span><samp>arnr-strength (<em>strength</em>)</samp></span></dt>
<dd><p>Set altref noise reduction filter strength. Range is -1 to 6. Default is -1.
</p>
</dd>
<dt><span><samp>aq-mode (<em>aq-mode</em>)</samp></span></dt>
<dd><p>Set adaptive quantization mode. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>0</em>)</samp>&rsquo;</span></dt>
<dd><p>Disabled.
</p>
</dd>
<dt><span>&lsquo;<samp>variance (<em>1</em>)</samp>&rsquo;</span></dt>
<dd><p>Variance-based.
</p>
</dd>
<dt><span>&lsquo;<samp>complexity (<em>2</em>)</samp>&rsquo;</span></dt>
<dd><p>Complexity-based.
</p>
</dd>
<dt><span>&lsquo;<samp>cyclic (<em>3</em>)</samp>&rsquo;</span></dt>
<dd><p>Cyclic refresh.
</p></dd>
</dl>

</dd>
<dt><span><samp>tune (<em>tune</em>)</samp></span></dt>
<dd><p>Set the distortion metric the encoder is tuned with. Default is <code>psnr</code>.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>psnr (<em>0</em>)</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>ssim (<em>1</em>)</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>lag-in-frames</samp></span></dt>
<dd><p>Set the maximum number of frames which the encoder may keep in flight
at any one time for lookahead purposes.  Defaults to the internal
default of the library.
</p>
</dd>
<dt><span><samp>error-resilience</samp></span></dt>
<dd><p>Enable error resilience features:
</p><dl compact="compact">
<dt><span><samp>default</samp></span></dt>
<dd><p>Improve resilience against losses of whole frames.
</p></dd>
</dl>
<p>Not enabled by default.
</p>
</dd>
<dt><span><samp>crf</samp></span></dt>
<dd><p>Set the quality/size tradeoff for constant-quality (no bitrate target)
and constrained-quality (with maximum bitrate target) modes. Valid
range is 0 to 63, higher numbers indicating lower quality and smaller
output size.  Only used if set; by default only the bitrate target is
used.
</p>
</dd>
<dt><span><samp>static-thresh</samp></span></dt>
<dd><p>Set a change threshold on blocks below which they will be skipped by
the encoder.  Defined in arbitrary units as a nonnegative integer,
defaulting to zero (no blocks are skipped).
</p>
</dd>
<dt><span><samp>drop-threshold</samp></span></dt>
<dd><p>Set a threshold for dropping frames when close to rate control bounds.
Defined as a percentage of the target buffer - when the rate control
buffer falls below this percentage, frames will be dropped until it
has refilled above the threshold.  Defaults to zero (no frames are
dropped).
</p>
</dd>
<dt><span><samp>denoise-noise-level (<em>level</em>)</samp></span></dt>
<dd><p>Amount of noise to be removed for grain synthesis. Grain synthesis is disabled if
this option is not set or set to 0.
</p>
</dd>
<dt><span><samp>denoise-block-size (<em>pixels</em>)</samp></span></dt>
<dd><p>Block size used for denoising for grain synthesis. If not set, AV1 codec
uses the default value of 32.
</p>
</dd>
<dt><span><samp>undershoot-pct (<em>pct</em>)</samp></span></dt>
<dd><p>Set datarate undershoot (min) percentage of the target bitrate. Range is -1 to 100.
Default is -1.
</p>
</dd>
<dt><span><samp>overshoot-pct (<em>pct</em>)</samp></span></dt>
<dd><p>Set datarate overshoot (max) percentage of the target bitrate. Range is -1 to 1000.
Default is -1.
</p>
</dd>
<dt><span><samp>minsection-pct (<em>pct</em>)</samp></span></dt>
<dd><p>Minimum percentage variation of the GOP bitrate from the target bitrate. If minsection-pct
is not set, the libaomenc wrapper computes it as follows: <code>(minrate * 100 / bitrate)</code>.
Range is -1 to 100. Default is -1 (unset).
</p>
</dd>
<dt><span><samp>maxsection-pct (<em>pct</em>)</samp></span></dt>
<dd><p>Maximum percentage variation of the GOP bitrate from the target bitrate. If maxsection-pct
is not set, the libaomenc wrapper computes it as follows: <code>(maxrate * 100 / bitrate)</code>.
Range is -1 to 5000. Default is -1 (unset).
</p>
</dd>
<dt><span><samp>frame-parallel (<em>boolean</em>)</samp></span></dt>
<dd><p>Enable frame parallel decodability features. Default is true.
</p>
</dd>
<dt><span><samp>tiles</samp></span></dt>
<dd><p>Set the number of tiles to encode the input video with, as columns x
rows.  Larger numbers allow greater parallelism in both encoding and
decoding, but may decrease coding efficiency.  Defaults to the minimum
number of tiles required by the size of the input video (this is 1x1
(that is, a single tile) for sizes up to and including 4K).
</p>
</dd>
<dt><span><samp>tile-columns tile-rows</samp></span></dt>
<dd><p>Set the number of tiles as log2 of the number of tile rows and columns.
Provided for compatibility with libvpx/VP9.
</p>
</dd>
<dt><span><samp>row-mt (Requires libaom &gt;= 1.0.0-759-g90a15f4f2)</samp></span></dt>
<dd><p>Enable row based multi-threading. Disabled by default.
</p>
</dd>
<dt><span><samp>enable-cdef (<em>boolean</em>)</samp></span></dt>
<dd><p>Enable Constrained Directional Enhancement Filter. The libaom-av1
encoder enables CDEF by default.
</p>
</dd>
<dt><span><samp>enable-restoration (<em>boolean</em>)</samp></span></dt>
<dd><p>Enable Loop Restoration Filter. Default is true for libaom-av1.
</p>
</dd>
<dt><span><samp>enable-global-motion (<em>boolean</em>)</samp></span></dt>
<dd><p>Enable the use of global motion for block prediction. Default is true.
</p>
</dd>
<dt><span><samp>enable-intrabc (<em>boolean</em>)</samp></span></dt>
<dd><p>Enable block copy mode for intra block prediction. This mode is
useful for screen content. Default is true.
</p>
</dd>
<dt><span><samp>enable-rect-partitions (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable rectangular partitions. Default is true.
</p>
</dd>
<dt><span><samp>enable-1to4-partitions (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable 1:4/4:1 partitions. Default is true.
</p>
</dd>
<dt><span><samp>enable-ab-partitions (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable AB shape partitions. Default is true.
</p>
</dd>
<dt><span><samp>enable-angle-delta (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable angle delta intra prediction. Default is true.
</p>
</dd>
<dt><span><samp>enable-cfl-intra (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable chroma predicted from luma intra prediction. Default is true.
</p>
</dd>
<dt><span><samp>enable-filter-intra (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable filter intra predictor. Default is true.
</p>
</dd>
<dt><span><samp>enable-intra-edge-filter (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable intra edge filter. Default is true.
</p>
</dd>
<dt><span><samp>enable-smooth-intra (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable smooth intra prediction mode. Default is true.
</p>
</dd>
<dt><span><samp>enable-paeth-intra (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable paeth predictor in intra prediction. Default is true.
</p>
</dd>
<dt><span><samp>enable-palette (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable palette prediction mode. Default is true.
</p>
</dd>
<dt><span><samp>enable-flip-idtx (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable extended transform type, including FLIPADST_DCT, DCT_FLIPADST,
FLIPADST_FLIPADST, ADST_FLIPADST, FLIPADST_ADST, IDTX, V_DCT, H_DCT,
V_ADST, H_ADST, V_FLIPADST, H_FLIPADST. Default is true.
</p>
</dd>
<dt><span><samp>enable-tx64 (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable 64-pt transform. Default is true.
</p>
</dd>
<dt><span><samp>reduced-tx-type-set (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Use reduced set of transform types. Default is false.
</p>
</dd>
<dt><span><samp>use-intra-dct-only (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Use DCT only for INTRA modes. Default is false.
</p>
</dd>
<dt><span><samp>use-inter-dct-only (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Use DCT only for INTER modes. Default is false.
</p>
</dd>
<dt><span><samp>use-intra-default-tx-only (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Use Default-transform only for INTRA modes. Default is false.
</p>
</dd>
<dt><span><samp>enable-ref-frame-mvs (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable temporal mv prediction. Default is true.
</p>
</dd>
<dt><span><samp>enable-reduced-reference-set (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Use reduced set of single and compound references. Default is false.
</p>
</dd>
<dt><span><samp>enable-obmc (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable obmc. Default is true.
</p>
</dd>
<dt><span><samp>enable-dual-filter (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable dual filter. Default is true.
</p>
</dd>
<dt><span><samp>enable-diff-wtd-comp (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable difference-weighted compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-dist-wtd-comp (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable distance-weighted compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-onesided-comp (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable one sided compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-interinter-wedge (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable interinter wedge compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-interintra-wedge (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable interintra wedge compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-masked-comp (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable masked compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-interintra-comp (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable interintra compound. Default is true.
</p>
</dd>
<dt><span><samp>enable-smooth-interintra (<em>boolean</em>) (Requires libaom &gt;= v2.0.0)</samp></span></dt>
<dd><p>Enable smooth interintra mode. Default is true.
</p>
</dd>
<dt><span><samp>aom-params</samp></span></dt>
<dd><p>Set libaom options using a list of <var>key</var>=<var>value</var> pairs separated
by &quot;:&quot;. For a list of supported options, see <code>aomenc --help</code> under the
section &quot;AV1 Specific Options&quot;.
</p>
<p>For example to specify libaom encoding options with <samp>-aom-params</samp>:
</p>
<div class="example">
<pre class="example">ffmpeg -i input -c:v libaom-av1 -b:v 500K -aom-params tune=psnr:enable-tpl-model=1 output.mp4
</pre></div>

</dd>
</dl>

<a name="libsvtav1"></a>
<h3 class="section">9.8 libsvtav1<span class="pull-right"><a class="anchor hidden-xs" href="#libsvtav1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libsvtav1" aria-hidden="true">TOC</a></span></h3>

<p>SVT-AV1 encoder wrapper.
</p>
<p>Requires the presence of the SVT-AV1 headers and library during configuration.
You need to explicitly configure the build with <code>--enable-libsvtav1</code>.
</p>
<a name="Options-27"></a>
<h4 class="subsection">9.8.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-27" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-27" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>profile</samp></span></dt>
<dd><p>Set the encoding profile.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>professional</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>level</samp></span></dt>
<dd><p>Set the operating point level. For example: &rsquo;4.0&rsquo;
</p>
</dd>
<dt><span><samp>hielevel</samp></span></dt>
<dd><p>Set the Hierarchical prediction levels.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>3level</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>4level</samp>&rsquo;</span></dt>
<dd><p>This is the default.
</p></dd>
</dl>

</dd>
<dt><span><samp>tier</samp></span></dt>
<dd><p>Set the operating point tier.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dd><p>This is the default.
</p></dd>
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>qmax</samp></span></dt>
<dd><p>Set the maximum quantizer to use when using a bitrate mode.
</p>
</dd>
<dt><span><samp>qmin</samp></span></dt>
<dd><p>Set the minimum quantizer to use when using a bitrate mode.
</p>
</dd>
<dt><span><samp>crf</samp></span></dt>
<dd><p>Constant rate factor value used in crf rate control mode (0-63).
</p>
</dd>
<dt><span><samp>qp</samp></span></dt>
<dd><p>Set the quantizer used in cqp rate control mode (0-63).
</p>
</dd>
<dt><span><samp>sc_detection</samp></span></dt>
<dd><p>Enable scene change detection.
</p>
</dd>
<dt><span><samp>la_depth</samp></span></dt>
<dd><p>Set number of frames to look ahead (0-120).
</p>
</dd>
<dt><span><samp>preset</samp></span></dt>
<dd><p>Set the quality-speed tradeoff, in the range 0 to 13.  Higher values are
faster but lower quality.
</p>
</dd>
<dt><span><samp>tile_rows</samp></span></dt>
<dd><p>Set log2 of the number of rows of tiles to use (0-6).
</p>
</dd>
<dt><span><samp>tile_columns</samp></span></dt>
<dd><p>Set log2 of the number of columns of tiles to use (0-4).
</p>
</dd>
<dt><span><samp>svtav1-params</samp></span></dt>
<dd><p>Set SVT-AV1 options using a list of <var>key</var>=<var>value</var> pairs separated
by &quot;:&quot;. See the SVT-AV1 encoder user guide for a list of accepted parameters.
</p>
</dd>
</dl>

<a name="libjxl"></a>
<h3 class="section">9.9 libjxl<span class="pull-right"><a class="anchor hidden-xs" href="#libjxl" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libjxl" aria-hidden="true">TOC</a></span></h3>

<p>libjxl JPEG XL encoder wrapper.
</p>
<p>Requires the presence of the libjxl headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libjxl</code>.
</p>
<a name="Options-28"></a>
<h4 class="subsection">9.9.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-28" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-28" aria-hidden="true">TOC</a></span></h4>

<p>The libjxl wrapper supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>distance</samp></span></dt>
<dd><p>Set the target Butteraugli distance. This is a quality setting: lower
distance yields higher quality, with distance=1.0 roughly comparable to
libjpeg Quality 90 for photographic content. Setting distance=0.0 yields
true lossless encoding. Valid values range between 0.0 and 15.0, and sane
values rarely exceed 5.0. Setting distance=0.1 usually attains
transparency for most input. The default is 1.0.
</p>
</dd>
<dt><span><samp>effort</samp></span></dt>
<dd><p>Set the encoding effort used. Higher effort values produce more consistent
quality and usually produces a better quality/bpp curve, at the cost of
more CPU time required. Valid values range from 1 to 9, and the default is 7.
</p>
</dd>
<dt><span><samp>modular</samp></span></dt>
<dd><p>Force the encoder to use Modular mode instead of choosing automatically. The
default is to use VarDCT for lossy encoding and Modular for lossless. VarDCT
is generally superior to Modular for lossy encoding but does not support
lossless encoding.
</p>
</dd>
</dl>

<a name="libkvazaar"></a>
<h3 class="section">9.10 libkvazaar<span class="pull-right"><a class="anchor hidden-xs" href="#libkvazaar" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libkvazaar" aria-hidden="true">TOC</a></span></h3>

<p>Kvazaar H.265/HEVC encoder.
</p>
<p>Requires the presence of the libkvazaar headers and library during
configuration. You need to explicitly configure the build with
<samp>--enable-libkvazaar</samp>.
</p>
<a name="Options-29"></a>
<h4 class="subsection">9.10.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-29" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-29" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set target video bitrate in bit/s and enable rate control.
</p>
</dd>
<dt><span><samp>kvazaar-params</samp></span></dt>
<dd><p>Set kvazaar parameters as a list of <var>name</var>=<var>value</var> pairs separated
by commas (,). See kvazaar documentation for a list of options.
</p>
</dd>
</dl>

<a name="libopenh264"></a>
<h3 class="section">9.11 libopenh264<span class="pull-right"><a class="anchor hidden-xs" href="#libopenh264" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopenh264" aria-hidden="true">TOC</a></span></h3>

<p>Cisco libopenh264 H.264/MPEG-4 AVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libopenh264 headers and
library during configuration. You need to explicitly configure the
build with <code>--enable-libopenh264</code>. The library is detected using
<code>pkg-config</code>.
</p>
<p>For more information about the library see
<a href="http://www.openh264.org">http://www.openh264.org</a>.
</p>
<a name="Options-30"></a>
<h4 class="subsection">9.11.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-30" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-30" aria-hidden="true">TOC</a></span></h4>

<p>The following FFmpeg global options affect the configurations of the
libopenh264 encoder.
</p>
<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set the bitrate (as a number of bits per second).
</p>
</dd>
<dt><span><samp>g</samp></span></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><span><samp>maxrate</samp></span></dt>
<dd><p>Set the max bitrate (as a number of bits per second).
</p>
</dd>
<dt><span><samp>flags +global_header</samp></span></dt>
<dd><p>Set global header in the bitstream.
</p>
</dd>
<dt><span><samp>slices</samp></span></dt>
<dd><p>Set the number of slices, used in parallelized encoding. Default value
is 0. This is only used when <samp>slice_mode</samp> is set to
&lsquo;<samp>fixed</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>slice_mode</samp></span></dt>
<dd><p>Set slice mode. Can assume one of the following possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>fixed</samp>&rsquo;</span></dt>
<dd><p>a fixed number of slices
</p></dd>
<dt><span>&lsquo;<samp>rowmb</samp>&rsquo;</span></dt>
<dd><p>one slice per row of macroblocks
</p></dd>
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>automatic number of slices according to number of threads
</p></dd>
<dt><span>&lsquo;<samp>dyn</samp>&rsquo;</span></dt>
<dd><p>dynamic slicing
</p></dd>
</dl>

<p>Default value is &lsquo;<samp>auto</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>loopfilter</samp></span></dt>
<dd><p>Enable loop filter, if set to 1 (automatically enabled). To disable
set a value of 0.
</p>
</dd>
<dt><span><samp>profile</samp></span></dt>
<dd><p>Set profile restrictions. If set to the value of &lsquo;<samp>main</samp>&rsquo; enable
CABAC (set the <code>SEncParamExt.iEntropyCodingModeFlag</code> flag to 1).
</p>
</dd>
<dt><span><samp>max_nal_size</samp></span></dt>
<dd><p>Set maximum NAL size in bytes.
</p>
</dd>
<dt><span><samp>allow_skip_frames</samp></span></dt>
<dd><p>Allow skipping frames to hit the target bitrate if set to 1.
</p></dd>
</dl>

<a name="libtheora"></a>
<h3 class="section">9.12 libtheora<span class="pull-right"><a class="anchor hidden-xs" href="#libtheora" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libtheora" aria-hidden="true">TOC</a></span></h3>

<p>libtheora Theora encoder wrapper.
</p>
<p>Requires the presence of the libtheora headers and library during
configuration. You need to explicitly configure the build with
<code>--enable-libtheora</code>.
</p>
<p>For more information about the libtheora project see
<a href="http://www.theora.org/">http://www.theora.org/</a>.
</p>
<a name="Options-31"></a>
<h4 class="subsection">9.12.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-31" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-31" aria-hidden="true">TOC</a></span></h4>

<p>The following global options are mapped to internal libtheora options
which affect the quality and the bitrate of the encoded stream.
</p>
<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Set the video bitrate in bit/s for CBR (Constant Bit Rate) mode.  In
case VBR (Variable Bit Rate) mode is enabled this option is ignored.
</p>
</dd>
<dt><span><samp>flags</samp></span></dt>
<dd><p>Used to enable constant quality mode (VBR) encoding through the
<samp>qscale</samp> flag, and to enable the <code>pass1</code> and <code>pass2</code>
modes.
</p>
</dd>
<dt><span><samp>g</samp></span></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><span><samp>global_quality</samp></span></dt>
<dd><p>Set the global quality as an integer in lambda units.
</p>
<p>Only relevant when VBR mode is enabled with <code>flags +qscale</code>. The
value is converted to QP units by dividing it by <code>FF_QP2LAMBDA</code>,
clipped in the [0 - 10] range, and then multiplied by 6.3 to get a
value in the native libtheora range [0-63]. A higher value corresponds
to a higher quality.
</p>
</dd>
<dt><span><samp>q</samp></span></dt>
<dd><p>Enable VBR mode when set to a non-negative value, and set constant
quality value as a double floating point value in QP units.
</p>
<p>The value is clipped in the [0-10] range, and then multiplied by 6.3
to get a value in the native libtheora range [0-63].
</p>
<p>This option is valid only using the <code>ffmpeg</code> command-line
tool. For library interface users, use <samp>global_quality</samp>.
</p></dd>
</dl>

<a name="Examples-1"></a>
<h4 class="subsection">9.12.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Set maximum constant quality (VBR) encoding with <code>ffmpeg</code>:
<div class="example">
<pre class="example">ffmpeg -i INPUT -codec:v libtheora -q:v 10 OUTPUT.ogg
</pre></div>

</li><li> Use <code>ffmpeg</code> to convert a CBR 1000 kbps Theora video stream:
<div class="example">
<pre class="example">ffmpeg -i INPUT -codec:v libtheora -b:v 1000k OUTPUT.ogg
</pre></div>
</li></ul>

<a name="libvpx"></a>
<h3 class="section">9.13 libvpx<span class="pull-right"><a class="anchor hidden-xs" href="#libvpx" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libvpx" aria-hidden="true">TOC</a></span></h3>

<p>VP8/VP9 format supported through libvpx.
</p>
<p>Requires the presence of the libvpx headers and library during configuration.
You need to explicitly configure the build with <code>--enable-libvpx</code>.
</p>
<a name="Options-32"></a>
<h4 class="subsection">9.13.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-32" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-32" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libvpx wrapper. The
<code>vpxenc</code>-equivalent options or values are listed in parentheses
for easy migration.
</p>
<p>To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
<a href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<p>To get more documentation of the libvpx options, invoke the command
<code>ffmpeg -h encoder=libvpx</code>, <code>ffmpeg -h encoder=libvpx-vp9</code> or
<code>vpxenc --help</code>. Further information is available in the libvpx API
documentation.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>target-bitrate</em>)</samp></span></dt>
<dd><p>Set bitrate in bits/s. Note that FFmpeg&rsquo;s <samp>b</samp> option is
expressed in bits/s, while <code>vpxenc</code>&rsquo;s <samp>target-bitrate</samp> is in
kilobits/s.
</p>
</dd>
<dt><span><samp>g (<em>kf-max-dist</em>)</samp></span></dt>
<dt><span><samp>keyint_min (<em>kf-min-dist</em>)</samp></span></dt>
<dt><span><samp>qmin (<em>min-q</em>)</samp></span></dt>
<dd><p>Minimum (Best Quality) Quantizer.
</p>
</dd>
<dt><span><samp>qmax (<em>max-q</em>)</samp></span></dt>
<dd><p>Maximum (Worst Quality) Quantizer.
Can be changed per-frame.
</p>
</dd>
<dt><span><samp>bufsize (<em>buf-sz</em>, <em>buf-optimal-sz</em>)</samp></span></dt>
<dd><p>Set ratecontrol buffer size (in bits). Note <code>vpxenc</code>&rsquo;s options are
specified in milliseconds, the libvpx wrapper converts this value as follows:
<code>buf-sz = bufsize * 1000 / bitrate</code>,
<code>buf-optimal-sz = bufsize * 1000 / bitrate * 5 / 6</code>.
</p>
</dd>
<dt><span><samp>rc_init_occupancy (<em>buf-initial-sz</em>)</samp></span></dt>
<dd><p>Set number of bits which should be loaded into the rc buffer before decoding
starts. Note <code>vpxenc</code>&rsquo;s option is specified in milliseconds, the libvpx
wrapper converts this value as follows:
<code>rc_init_occupancy * 1000 / bitrate</code>.
</p>
</dd>
<dt><span><samp>undershoot-pct</samp></span></dt>
<dd><p>Set datarate undershoot (min) percentage of the target bitrate.
</p>
</dd>
<dt><span><samp>overshoot-pct</samp></span></dt>
<dd><p>Set datarate overshoot (max) percentage of the target bitrate.
</p>
</dd>
<dt><span><samp>skip_threshold (<em>drop-frame</em>)</samp></span></dt>
<dt><span><samp>qcomp (<em>bias-pct</em>)</samp></span></dt>
<dt><span><samp>maxrate (<em>maxsection-pct</em>)</samp></span></dt>
<dd><p>Set GOP max bitrate in bits/s. Note <code>vpxenc</code>&rsquo;s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: <code>(maxrate * 100 / bitrate)</code>.
</p>
</dd>
<dt><span><samp>minrate (<em>minsection-pct</em>)</samp></span></dt>
<dd><p>Set GOP min bitrate in bits/s. Note <code>vpxenc</code>&rsquo;s option is specified as a
percentage of the target bitrate, the libvpx wrapper converts this value as
follows: <code>(minrate * 100 / bitrate)</code>.
</p>
</dd>
<dt><span><samp>minrate, maxrate, b <em>end-usage=cbr</em></samp></span></dt>
<dd><p><code>(minrate == maxrate == bitrate)</code>.
</p>
</dd>
<dt><span><samp>crf (<em>end-usage=cq</em>, <em>cq-level</em>)</samp></span></dt>
<dt><span><samp>tune (<em>tune</em>)</samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>psnr (<em>psnr</em>)</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>ssim (<em>ssim</em>)</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>quality, deadline (<em>deadline</em>)</samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>best</samp>&rsquo;</span></dt>
<dd><p>Use best quality deadline. Poorly named and quite slow, this option should be
avoided as it may give worse quality output than good.
</p></dd>
<dt><span>&lsquo;<samp>good</samp>&rsquo;</span></dt>
<dd><p>Use good quality deadline. This is a good trade-off between speed and quality
when used with the <samp>cpu-used</samp> option.
</p></dd>
<dt><span>&lsquo;<samp>realtime</samp>&rsquo;</span></dt>
<dd><p>Use realtime quality deadline.
</p></dd>
</dl>

</dd>
<dt><span><samp>speed, cpu-used (<em>cpu-used</em>)</samp></span></dt>
<dd><p>Set quality/speed ratio modifier. Higher values speed up the encode at the cost
of quality.
</p>
</dd>
<dt><span><samp>nr (<em>noise-sensitivity</em>)</samp></span></dt>
<dt><span><samp>static-thresh</samp></span></dt>
<dd><p>Set a change threshold on blocks below which they will be skipped by the
encoder.
</p>
</dd>
<dt><span><samp>slices (<em>token-parts</em>)</samp></span></dt>
<dd><p>Note that FFmpeg&rsquo;s <samp>slices</samp> option gives the total number of partitions,
while <code>vpxenc</code>&rsquo;s <samp>token-parts</samp> is given as
<code>log2(partitions)</code>.
</p>
</dd>
<dt><span><samp>max-intra-rate</samp></span></dt>
<dd><p>Set maximum I-frame bitrate as a percentage of the target bitrate. A value of 0
means unlimited.
</p>
</dd>
<dt><span><samp>force_key_frames</samp></span></dt>
<dd><p><code>VPX_EFLAG_FORCE_KF</code>
</p>
</dd>
<dt><span><samp>Alternate reference frame related</samp></span></dt>
<dd><dl compact="compact">
<dt><span><samp>auto-alt-ref</samp></span></dt>
<dd><p>Enable use of alternate reference frames (2-pass only).
Values greater than 1 enable multi-layer alternate reference frames (VP9 only).
</p></dd>
<dt><span><samp>arnr-maxframes</samp></span></dt>
<dd><p>Set altref noise reduction max frame count.
</p></dd>
<dt><span><samp>arnr-type</samp></span></dt>
<dd><p>Set altref noise reduction filter type: backward, forward, centered.
</p></dd>
<dt><span><samp>arnr-strength</samp></span></dt>
<dd><p>Set altref noise reduction filter strength.
</p></dd>
<dt><span><samp>rc-lookahead, lag-in-frames (<em>lag-in-frames</em>)</samp></span></dt>
<dd><p>Set number of frames to look ahead for frametype and ratecontrol.
</p></dd>
</dl>

</dd>
<dt><span><samp>error-resilient</samp></span></dt>
<dd><p>Enable error resiliency features.
</p>
</dd>
<dt><span><samp>sharpness <var>integer</var></samp></span></dt>
<dd><p>Increase sharpness at the expense of lower PSNR.
The valid range is [0, 7].
</p>
</dd>
<dt><span><samp>ts-parameters</samp></span></dt>
<dd><p>Sets the temporal scalability configuration using a :-separated list of
key=value pairs. For example, to specify temporal scalability parameters
with <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:v libvpx -ts-parameters ts_number_layers=3:\
ts_target_bitrate=250,500,1000:ts_rate_decimator=4,2,1:\
ts_periodicity=4:ts_layer_id=0,2,1,2:ts_layering_mode=3 OUTPUT
</pre></div>
<p>Below is a brief explanation of each of the parameters, please
refer to <code>struct vpx_codec_enc_cfg</code> in <code>vpx/vpx_encoder.h</code> for more
details.
</p><dl compact="compact">
<dt><span><samp>ts_number_layers</samp></span></dt>
<dd><p>Number of temporal coding layers.
</p></dd>
<dt><span><samp>ts_target_bitrate</samp></span></dt>
<dd><p>Target bitrate for each temporal layer (in kbps).
(bitrate should be inclusive of the lower temporal layer).
</p></dd>
<dt><span><samp>ts_rate_decimator</samp></span></dt>
<dd><p>Frame rate decimation factor for each temporal layer.
</p></dd>
<dt><span><samp>ts_periodicity</samp></span></dt>
<dd><p>Length of the sequence defining frame temporal layer membership.
</p></dd>
<dt><span><samp>ts_layer_id</samp></span></dt>
<dd><p>Template defining the membership of frames to temporal layers.
</p></dd>
<dt><span><samp>ts_layering_mode</samp></span></dt>
<dd><p>(optional) Selecting the temporal structure from a set of pre-defined temporal layering modes.
Currently supports the following options.
</p><dl compact="compact">
<dt><span><samp>0</samp></span></dt>
<dd><p>No temporal layering flags are provided internally,
relies on flags being passed in using <code>metadata</code> field in <code>AVFrame</code>
with following keys.
</p><dl compact="compact">
<dt><span><samp>vp8-flags</samp></span></dt>
<dd><p>Sets the flags passed into the encoder to indicate the referencing scheme for
the current frame.
Refer to function <code>vpx_codec_encode</code> in <code>vpx/vpx_encoder.h</code> for more
details.
</p></dd>
<dt><span><samp>temporal_id</samp></span></dt>
<dd><p>Explicitly sets the temporal id of the current frame to encode.
</p></dd>
</dl>
</dd>
<dt><span><samp>2</samp></span></dt>
<dd><p>Two temporal layers. 0-1...
</p></dd>
<dt><span><samp>3</samp></span></dt>
<dd><p>Three temporal layers. 0-2-1-2...; with single reference frame.
</p></dd>
<dt><span><samp>4</samp></span></dt>
<dd><p>Same as option &quot;3&quot;, except there is a dependency between
the two temporal layer 2 frames within the temporal period.
</p></dd>
</dl>
</dd>
</dl>

</dd>
<dt><span><samp>VP9-specific options</samp></span></dt>
<dd><dl compact="compact">
<dt><span><samp>lossless</samp></span></dt>
<dd><p>Enable lossless mode.
</p></dd>
<dt><span><samp>tile-columns</samp></span></dt>
<dd><p>Set number of tile columns to use. Note this is given as
<code>log2(tile_columns)</code>. For example, 8 tile columns would be requested by
setting the <samp>tile-columns</samp> option to 3.
</p></dd>
<dt><span><samp>tile-rows</samp></span></dt>
<dd><p>Set number of tile rows to use. Note this is given as <code>log2(tile_rows)</code>.
For example, 4 tile rows would be requested by setting the <samp>tile-rows</samp>
option to 2.
</p></dd>
<dt><span><samp>frame-parallel</samp></span></dt>
<dd><p>Enable frame parallel decodability features.
</p></dd>
<dt><span><samp>aq-mode</samp></span></dt>
<dd><p>Set adaptive quantization mode (0: off (default), 1: variance 2: complexity, 3:
cyclic refresh, 4: equator360).
</p></dd>
<dt><span><samp>colorspace <em>color-space</em></samp></span></dt>
<dd><p>Set input color space. The VP9 bitstream supports signaling the following
colorspaces:
</p><dl compact="compact">
<dt><span><samp>&lsquo;<samp>rgb</samp>&rsquo; <em>sRGB</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>bt709</samp>&rsquo; <em>bt709</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>unspecified</samp>&rsquo; <em>unknown</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>bt470bg</samp>&rsquo; <em>bt601</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>smpte170m</samp>&rsquo; <em>smpte170</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>smpte240m</samp>&rsquo; <em>smpte240</em></samp></span></dt>
<dt><span><samp>&lsquo;<samp>bt2020_ncl</samp>&rsquo; <em>bt2020</em></samp></span></dt>
</dl>
</dd>
<dt><span><samp>row-mt <var>boolean</var></samp></span></dt>
<dd><p>Enable row based multi-threading.
</p></dd>
<dt><span><samp>tune-content</samp></span></dt>
<dd><p>Set content type: default (0), screen (1), film (2).
</p></dd>
<dt><span><samp>corpus-complexity</samp></span></dt>
<dd><p>Corpus VBR mode is a variant of standard VBR where the complexity distribution
midpoint is passed in rather than calculated for a specific clip or chunk.
</p>
<p>The valid range is [0, 10000]. 0 (default) uses standard VBR.
</p></dd>
<dt><span><samp>enable-tpl <var>boolean</var></samp></span></dt>
<dd><p>Enable temporal dependency model.
</p></dd>
<dt><span><samp>ref-frame-config</samp></span></dt>
<dd><p>Using per-frame metadata, set members of the structure <code>vpx_svc_ref_frame_config_t</code> in <code>vpx/vp8cx.h</code> to fine-control referencing schemes and frame buffer management.
<br>Use a :-separated list of key=value pairs.
For example,
</p><div class="example">
<pre class="example">av_dict_set(&amp;av_frame-&gt;metadata, &quot;ref-frame-config&quot;, \
&quot;rfc_update_buffer_slot=7:rfc_lst_fb_idx=0:rfc_gld_fb_idx=1:rfc_alt_fb_idx=2:rfc_reference_last=0:rfc_reference_golden=0:rfc_reference_alt_ref=0&quot;);
</pre></div>
<dl compact="compact">
<dt><span><samp>rfc_update_buffer_slot</samp></span></dt>
<dd><p>Indicates the buffer slot number to update
</p></dd>
<dt><span><samp>rfc_update_last</samp></span></dt>
<dd><p>Indicates whether to update the LAST frame
</p></dd>
<dt><span><samp>rfc_update_golden</samp></span></dt>
<dd><p>Indicates whether to update GOLDEN frame
</p></dd>
<dt><span><samp>rfc_update_alt_ref</samp></span></dt>
<dd><p>Indicates whether to update ALT_REF frame
</p></dd>
<dt><span><samp>rfc_lst_fb_idx</samp></span></dt>
<dd><p>LAST frame buffer index
</p></dd>
<dt><span><samp>rfc_gld_fb_idx</samp></span></dt>
<dd><p>GOLDEN frame buffer index
</p></dd>
<dt><span><samp>rfc_alt_fb_idx</samp></span></dt>
<dd><p>ALT_REF frame buffer index
</p></dd>
<dt><span><samp>rfc_reference_last</samp></span></dt>
<dd><p>Indicates whether to reference LAST frame
</p></dd>
<dt><span><samp>rfc_reference_golden</samp></span></dt>
<dd><p>Indicates whether to reference GOLDEN frame
</p></dd>
<dt><span><samp>rfc_reference_alt_ref</samp></span></dt>
<dd><p>Indicates whether to reference ALT_REF frame
</p></dd>
<dt><span><samp>rfc_reference_duration</samp></span></dt>
<dd><p>Indicates frame duration
</p></dd>
</dl>
</dd>
</dl>

</dd>
</dl>

<p>For more information about libvpx see:
<a href="http://www.webmproject.org/">http://www.webmproject.org/</a>
</p>
<a name="libwebp"></a>
<h3 class="section">9.14 libwebp<span class="pull-right"><a class="anchor hidden-xs" href="#libwebp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libwebp" aria-hidden="true">TOC</a></span></h3>

<p>libwebp WebP Image encoder wrapper
</p>
<p>libwebp is Google&rsquo;s official encoder for WebP images. It can encode in either
lossy or lossless mode. Lossy images are essentially a wrapper around a VP8
frame. Lossless images are a separate codec developed by Google.
</p>
<a name="Pixel-Format"></a>
<h4 class="subsection">9.14.1 Pixel Format<span class="pull-right"><a class="anchor hidden-xs" href="#Pixel-Format" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Pixel-Format" aria-hidden="true">TOC</a></span></h4>

<p>Currently, libwebp only supports YUV420 for lossy and RGB for lossless due
to limitations of the format and libwebp. Alpha is supported for either mode.
Because of API limitations, if RGB is passed in when encoding lossy or YUV is
passed in for encoding lossless, the pixel format will automatically be
converted using functions from libwebp. This is not ideal and is done only for
convenience.
</p>
<a name="Options-33"></a>
<h4 class="subsection">9.14.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-33" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-33" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>-lossless <var>boolean</var></samp></span></dt>
<dd><p>Enables/Disables use of lossless mode. Default is 0.
</p>
</dd>
<dt><span><samp>-compression_level <var>integer</var></samp></span></dt>
<dd><p>For lossy, this is a quality/speed tradeoff. Higher values give better quality
for a given size at the cost of increased encoding time. For lossless, this is
a size/speed tradeoff. Higher values give smaller size at the cost of increased
encoding time. More specifically, it controls the number of extra algorithms
and compression tools used, and varies the combination of these tools. This
maps to the <var>method</var> option in libwebp. The valid range is 0 to 6.
Default is 4.
</p>
</dd>
<dt><span><samp>-quality <var>float</var></samp></span></dt>
<dd><p>For lossy encoding, this controls image quality. For lossless encoding, this
controls the effort and time spent in compression.
Range is 0 to 100. Default is 75.
</p>
</dd>
<dt><span><samp>-preset <var>type</var></samp></span></dt>
<dd><p>Configuration preset. This does some automatic settings based on the general
type of the image.
</p><dl compact="compact">
<dt><span><samp>none</samp></span></dt>
<dd><p>Do not use a preset.
</p></dd>
<dt><span><samp>default</samp></span></dt>
<dd><p>Use the encoder default.
</p></dd>
<dt><span><samp>picture</samp></span></dt>
<dd><p>Digital picture, like portrait, inner shot
</p></dd>
<dt><span><samp>photo</samp></span></dt>
<dd><p>Outdoor photograph, with natural lighting
</p></dd>
<dt><span><samp>drawing</samp></span></dt>
<dd><p>Hand or line drawing, with high-contrast details
</p></dd>
<dt><span><samp>icon</samp></span></dt>
<dd><p>Small-sized colorful images
</p></dd>
<dt><span><samp>text</samp></span></dt>
<dd><p>Text-like
</p></dd>
</dl>

</dd>
</dl>

<a name="libx264_002c-libx264rgb"></a>
<h3 class="section">9.15 libx264, libx264rgb<span class="pull-right"><a class="anchor hidden-xs" href="#libx264_002c-libx264rgb" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libx264_002c-libx264rgb" aria-hidden="true">TOC</a></span></h3>

<p>x264 H.264/MPEG-4 AVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libx264 headers and library
during configuration. You need to explicitly configure the build with
<code>--enable-libx264</code>.
</p>
<p>libx264 supports an impressive number of features, including 8x8 and
4x4 adaptive spatial transform, adaptive B-frame placement, CAVLC/CABAC
entropy coding, interlacing (MBAFF), lossless mode, psy optimizations
for detail retention (adaptive quantization, psy-RD, psy-trellis).
</p>
<p>Many libx264 encoder options are mapped to FFmpeg global codec
options, while unique encoder options are provided through private
options. Additionally the <samp>x264opts</samp> and <samp>x264-params</samp>
private options allows one to pass a list of key=value tuples as accepted
by the libx264 <code>x264_param_parse</code> function.
</p>
<p>The x264 project website is at
<a href="http://www.videolan.org/developers/x264.html">http://www.videolan.org/developers/x264.html</a>.
</p>
<p>The libx264rgb encoder is the same as libx264, except it accepts packed RGB
pixel formats as input instead of YUV.
</p>
<a name="Supported-Pixel-Formats"></a>
<h4 class="subsection">9.15.1 Supported Pixel Formats<span class="pull-right"><a class="anchor hidden-xs" href="#Supported-Pixel-Formats" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Supported-Pixel-Formats" aria-hidden="true">TOC</a></span></h4>

<p>x264 supports 8- to 10-bit color spaces. The exact bit depth is controlled at
x264&rsquo;s configure time.
</p>
<a name="Options-34"></a>
<h4 class="subsection">9.15.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-34" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-34" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libx264 wrapper. The
<code>x264</code>-equivalent options or values are listed in parentheses
for easy migration.
</p>
<p>To reduce the duplication of documentation, only the private options
and some others requiring special attention are documented here. For
the documentation of the undocumented generic options, see
<a href="#codec_002doptions">the Codec Options chapter</a>.
</p>
<p>To get a more accurate and extensive documentation of the libx264
options, invoke the command <code>x264 --fullhelp</code> or consult
the libx264 documentation.
</p>
<dl compact="compact">
<dt><span><samp>b (<em>bitrate</em>)</samp></span></dt>
<dd><p>Set bitrate in bits/s. Note that FFmpeg&rsquo;s <samp>b</samp> option is
expressed in bits/s, while <code>x264</code>&rsquo;s <samp>bitrate</samp> is in
kilobits/s.
</p>
</dd>
<dt><span><samp>bf (<em>bframes</em>)</samp></span></dt>
<dt><span><samp>g (<em>keyint</em>)</samp></span></dt>
<dt><span><samp>qmin (<em>qpmin</em>)</samp></span></dt>
<dd><p>Minimum quantizer scale.
</p>
</dd>
<dt><span><samp>qmax (<em>qpmax</em>)</samp></span></dt>
<dd><p>Maximum quantizer scale.
</p>
</dd>
<dt><span><samp>qdiff (<em>qpstep</em>)</samp></span></dt>
<dd><p>Maximum difference between quantizer scales.
</p>
</dd>
<dt><span><samp>qblur (<em>qblur</em>)</samp></span></dt>
<dd><p>Quantizer curve blur
</p>
</dd>
<dt><span><samp>qcomp (<em>qcomp</em>)</samp></span></dt>
<dd><p>Quantizer curve compression factor
</p>
</dd>
<dt><span><samp>refs (<em>ref</em>)</samp></span></dt>
<dd><p>Number of reference frames each P-frame can use. The range is from <var>0-16</var>.
</p>
</dd>
<dt><span><samp>sc_threshold (<em>scenecut</em>)</samp></span></dt>
<dd><p>Sets the threshold for the scene change detection.
</p>
</dd>
<dt><span><samp>trellis (<em>trellis</em>)</samp></span></dt>
<dd><p>Performs Trellis quantization to increase efficiency. Enabled by default.
</p>
</dd>
<dt><span><samp>nr  (<em>nr</em>)</samp></span></dt>
<dt><span><samp>me_range (<em>merange</em>)</samp></span></dt>
<dd><p>Maximum range of the motion search in pixels.
</p>
</dd>
<dt><span><samp>me_method (<em>me</em>)</samp></span></dt>
<dd><p>Set motion estimation method. Possible values in the decreasing order
of speed:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>dia (<em>dia</em>)</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>epzs (<em>dia</em>)</samp>&rsquo;</span></dt>
<dd><p>Diamond search with radius 1 (fastest). &lsquo;<samp>epzs</samp>&rsquo; is an alias for
&lsquo;<samp>dia</samp>&rsquo;.
</p></dd>
<dt><span>&lsquo;<samp>hex (<em>hex</em>)</samp>&rsquo;</span></dt>
<dd><p>Hexagonal search with radius 2.
</p></dd>
<dt><span>&lsquo;<samp>umh (<em>umh</em>)</samp>&rsquo;</span></dt>
<dd><p>Uneven multi-hexagon search.
</p></dd>
<dt><span>&lsquo;<samp>esa (<em>esa</em>)</samp>&rsquo;</span></dt>
<dd><p>Exhaustive search.
</p></dd>
<dt><span>&lsquo;<samp>tesa (<em>tesa</em>)</samp>&rsquo;</span></dt>
<dd><p>Hadamard exhaustive search (slowest).
</p></dd>
</dl>

</dd>
<dt><span><samp>forced-idr</samp></span></dt>
<dd><p>Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.
</p>
</dd>
<dt><span><samp>subq (<em>subme</em>)</samp></span></dt>
<dd><p>Sub-pixel motion estimation method.
</p>
</dd>
<dt><span><samp>b_strategy (<em>b-adapt</em>)</samp></span></dt>
<dd><p>Adaptive B-frame placement decision algorithm. Use only on first-pass.
</p>
</dd>
<dt><span><samp>keyint_min (<em>min-keyint</em>)</samp></span></dt>
<dd><p>Minimum GOP size.
</p>
</dd>
<dt><span><samp>coder</samp></span></dt>
<dd><p>Set entropy encoder. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>ac</samp>&rsquo;</span></dt>
<dd><p>Enable CABAC.
</p>
</dd>
<dt><span>&lsquo;<samp>vlc</samp>&rsquo;</span></dt>
<dd><p>Enable CAVLC and disable CABAC. It generates the same effect as
<code>x264</code>&rsquo;s <samp>--no-cabac</samp> option.
</p></dd>
</dl>

</dd>
<dt><span><samp>cmp</samp></span></dt>
<dd><p>Set full pixel motion estimation comparison algorithm. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>chroma</samp>&rsquo;</span></dt>
<dd><p>Enable chroma in motion estimation.
</p>
</dd>
<dt><span>&lsquo;<samp>sad</samp>&rsquo;</span></dt>
<dd><p>Ignore chroma in motion estimation. It generates the same effect as
<code>x264</code>&rsquo;s <samp>--no-chroma-me</samp> option.
</p></dd>
</dl>

</dd>
<dt><span><samp>threads (<em>threads</em>)</samp></span></dt>
<dd><p>Number of encoding threads.
</p>
</dd>
<dt><span><samp>thread_type</samp></span></dt>
<dd><p>Set multithreading technique. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>slice</samp>&rsquo;</span></dt>
<dd><p>Slice-based multithreading. It generates the same effect as
<code>x264</code>&rsquo;s <samp>--sliced-threads</samp> option.
</p></dd>
<dt><span>&lsquo;<samp>frame</samp>&rsquo;</span></dt>
<dd><p>Frame-based multithreading.
</p></dd>
</dl>

</dd>
<dt><span><samp>flags</samp></span></dt>
<dd><p>Set encoding flags. It can be used to disable closed GOP and enable
open GOP by setting it to <code>-cgop</code>. The result is similar to
the behavior of <code>x264</code>&rsquo;s <samp>--open-gop</samp> option.
</p>
</dd>
<dt><span><samp>rc_init_occupancy (<em>vbv-init</em>)</samp></span></dt>
<dt><span><samp>preset (<em>preset</em>)</samp></span></dt>
<dd><p>Set the encoding preset.
</p>
</dd>
<dt><span><samp>tune (<em>tune</em>)</samp></span></dt>
<dd><p>Set tuning of the encoding params.
</p>
</dd>
<dt><span><samp>profile (<em>profile</em>)</samp></span></dt>
<dd><p>Set profile restrictions.
</p>
</dd>
<dt><span><samp>fastfirstpass</samp></span></dt>
<dd><p>Enable fast settings when encoding first pass, when set to 1. When set
to 0, it has the same effect of <code>x264</code>&rsquo;s
<samp>--slow-firstpass</samp> option.
</p>
</dd>
<dt><span><samp>crf (<em>crf</em>)</samp></span></dt>
<dd><p>Set the quality for constant quality mode.
</p>
</dd>
<dt><span><samp>crf_max (<em>crf-max</em>)</samp></span></dt>
<dd><p>In CRF mode, prevents VBV from lowering quality beyond this point.
</p>
</dd>
<dt><span><samp>qp (<em>qp</em>)</samp></span></dt>
<dd><p>Set constant quantization rate control method parameter.
</p>
</dd>
<dt><span><samp>aq-mode (<em>aq-mode</em>)</samp></span></dt>
<dd><p>Set AQ method. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>0</em>)</samp>&rsquo;</span></dt>
<dd><p>Disabled.
</p>
</dd>
<dt><span>&lsquo;<samp>variance (<em>1</em>)</samp>&rsquo;</span></dt>
<dd><p>Variance AQ (complexity mask).
</p>
</dd>
<dt><span>&lsquo;<samp>autovariance (<em>2</em>)</samp>&rsquo;</span></dt>
<dd><p>Auto-variance AQ (experimental).
</p></dd>
</dl>

</dd>
<dt><span><samp>aq-strength (<em>aq-strength</em>)</samp></span></dt>
<dd><p>Set AQ strength, reduce blocking and blurring in flat and textured areas.
</p>
</dd>
<dt><span><samp>psy</samp></span></dt>
<dd><p>Use psychovisual optimizations when set to 1. When set to 0, it has the
same effect as <code>x264</code>&rsquo;s <samp>--no-psy</samp> option.
</p>
</dd>
<dt><span><samp>psy-rd  (<em>psy-rd</em>)</samp></span></dt>
<dd><p>Set strength of psychovisual optimization, in
<var>psy-rd</var>:<var>psy-trellis</var> format.
</p>
</dd>
<dt><span><samp>rc-lookahead (<em>rc-lookahead</em>)</samp></span></dt>
<dd><p>Set number of frames to look ahead for frametype and ratecontrol.
</p>
</dd>
<dt><span><samp>weightb</samp></span></dt>
<dd><p>Enable weighted prediction for B-frames when set to 1. When set to 0,
it has the same effect as <code>x264</code>&rsquo;s <samp>--no-weightb</samp> option.
</p>
</dd>
<dt><span><samp>weightp (<em>weightp</em>)</samp></span></dt>
<dd><p>Set weighted prediction method for P-frames. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>0</em>)</samp>&rsquo;</span></dt>
<dd><p>Disabled
</p></dd>
<dt><span>&lsquo;<samp>simple (<em>1</em>)</samp>&rsquo;</span></dt>
<dd><p>Enable only weighted refs
</p></dd>
<dt><span>&lsquo;<samp>smart (<em>2</em>)</samp>&rsquo;</span></dt>
<dd><p>Enable both weighted refs and duplicates
</p></dd>
</dl>

</dd>
<dt><span><samp>ssim (<em>ssim</em>)</samp></span></dt>
<dd><p>Enable calculation and printing SSIM stats after the encoding.
</p>
</dd>
<dt><span><samp>intra-refresh (<em>intra-refresh</em>)</samp></span></dt>
<dd><p>Enable the use of Periodic Intra Refresh instead of IDR frames when set
to 1.
</p>
</dd>
<dt><span><samp>avcintra-class (<em>class</em>)</samp></span></dt>
<dd><p>Configure the encoder to generate AVC-Intra.
Valid values are 50,100 and 200
</p>
</dd>
<dt><span><samp>bluray-compat (<em>bluray-compat</em>)</samp></span></dt>
<dd><p>Configure the encoder to be compatible with the bluray standard.
It is a shorthand for setting &quot;bluray-compat=1 force-cfr=1&quot;.
</p>
</dd>
<dt><span><samp>b-bias (<em>b-bias</em>)</samp></span></dt>
<dd><p>Set the influence on how often B-frames are used.
</p>
</dd>
<dt><span><samp>b-pyramid (<em>b-pyramid</em>)</samp></span></dt>
<dd><p>Set method for keeping of some B-frames as references. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>none</em>)</samp>&rsquo;</span></dt>
<dd><p>Disabled.
</p></dd>
<dt><span>&lsquo;<samp>strict (<em>strict</em>)</samp>&rsquo;</span></dt>
<dd><p>Strictly hierarchical pyramid.
</p></dd>
<dt><span>&lsquo;<samp>normal (<em>normal</em>)</samp>&rsquo;</span></dt>
<dd><p>Non-strict (not Blu-ray compatible).
</p></dd>
</dl>

</dd>
<dt><span><samp>mixed-refs</samp></span></dt>
<dd><p>Enable the use of one reference per partition, as opposed to one
reference per macroblock when set to 1. When set to 0, it has the
same effect as <code>x264</code>&rsquo;s <samp>--no-mixed-refs</samp> option.
</p>
</dd>
<dt><span><samp>8x8dct</samp></span></dt>
<dd><p>Enable adaptive spatial transform (high profile 8x8 transform)
when set to 1. When set to 0, it has the same effect as
<code>x264</code>&rsquo;s <samp>--no-8x8dct</samp> option.
</p>
</dd>
<dt><span><samp>fast-pskip</samp></span></dt>
<dd><p>Enable early SKIP detection on P-frames when set to 1. When set
to 0, it has the same effect as <code>x264</code>&rsquo;s
<samp>--no-fast-pskip</samp> option.
</p>
</dd>
<dt><span><samp>aud (<em>aud</em>)</samp></span></dt>
<dd><p>Enable use of access unit delimiters when set to 1.
</p>
</dd>
<dt><span><samp>mbtree</samp></span></dt>
<dd><p>Enable use macroblock tree ratecontrol when set to 1. When set
to 0, it has the same effect as <code>x264</code>&rsquo;s
<samp>--no-mbtree</samp> option.
</p>
</dd>
<dt><span><samp>deblock (<em>deblock</em>)</samp></span></dt>
<dd><p>Set loop filter parameters, in <var>alpha</var>:<var>beta</var> form.
</p>
</dd>
<dt><span><samp>cplxblur (<em>cplxblur</em>)</samp></span></dt>
<dd><p>Set fluctuations reduction in QP (before curve compression).
</p>
</dd>
<dt><span><samp>partitions (<em>partitions</em>)</samp></span></dt>
<dd><p>Set partitions to consider as a comma-separated list of. Possible
values in the list:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>p8x8</samp>&rsquo;</span></dt>
<dd><p>8x8 P-frame partition.
</p></dd>
<dt><span>&lsquo;<samp>p4x4</samp>&rsquo;</span></dt>
<dd><p>4x4 P-frame partition.
</p></dd>
<dt><span>&lsquo;<samp>b8x8</samp>&rsquo;</span></dt>
<dd><p>4x4 B-frame partition.
</p></dd>
<dt><span>&lsquo;<samp>i8x8</samp>&rsquo;</span></dt>
<dd><p>8x8 I-frame partition.
</p></dd>
<dt><span>&lsquo;<samp>i4x4</samp>&rsquo;</span></dt>
<dd><p>4x4 I-frame partition.
(Enabling &lsquo;<samp>p4x4</samp>&rsquo; requires &lsquo;<samp>p8x8</samp>&rsquo; to be enabled. Enabling
&lsquo;<samp>i8x8</samp>&rsquo; requires adaptive spatial transform (<samp>8x8dct</samp>
option) to be enabled.)
</p></dd>
<dt><span>&lsquo;<samp>none (<em>none</em>)</samp>&rsquo;</span></dt>
<dd><p>Do not consider any partitions.
</p></dd>
<dt><span>&lsquo;<samp>all (<em>all</em>)</samp>&rsquo;</span></dt>
<dd><p>Consider every partition.
</p></dd>
</dl>

</dd>
<dt><span><samp>direct-pred (<em>direct</em>)</samp></span></dt>
<dd><p>Set direct MV prediction mode. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>none</em>)</samp>&rsquo;</span></dt>
<dd><p>Disable MV prediction.
</p></dd>
<dt><span>&lsquo;<samp>spatial (<em>spatial</em>)</samp>&rsquo;</span></dt>
<dd><p>Enable spatial predicting.
</p></dd>
<dt><span>&lsquo;<samp>temporal (<em>temporal</em>)</samp>&rsquo;</span></dt>
<dd><p>Enable temporal predicting.
</p></dd>
<dt><span>&lsquo;<samp>auto (<em>auto</em>)</samp>&rsquo;</span></dt>
<dd><p>Automatically decided.
</p></dd>
</dl>

</dd>
<dt><span><samp>slice-max-size (<em>slice-max-size</em>)</samp></span></dt>
<dd><p>Set the limit of the size of each slice in bytes. If not specified
but RTP payload size (<samp>ps</samp>) is specified, that is used.
</p>
</dd>
<dt><span><samp>stats (<em>stats</em>)</samp></span></dt>
<dd><p>Set the file name for multi-pass stats.
</p>
</dd>
<dt><span><samp>nal-hrd (<em>nal-hrd</em>)</samp></span></dt>
<dd><p>Set signal HRD information (requires <samp>vbv-bufsize</samp> to be set).
Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>none (<em>none</em>)</samp>&rsquo;</span></dt>
<dd><p>Disable HRD information signaling.
</p></dd>
<dt><span>&lsquo;<samp>vbr (<em>vbr</em>)</samp>&rsquo;</span></dt>
<dd><p>Variable bit rate.
</p></dd>
<dt><span>&lsquo;<samp>cbr (<em>cbr</em>)</samp>&rsquo;</span></dt>
<dd><p>Constant bit rate (not allowed in MP4 container).
</p></dd>
</dl>

</dd>
<dt><span><samp>x264opts (N.A.)</samp></span></dt>
<dd><p>Set any x264 option, see <code>x264 --fullhelp</code> for a list.
</p>
<p>Argument is a list of <var>key</var>=<var>value</var> couples separated by
&quot;:&quot;. In <var>filter</var> and <var>psy-rd</var> options that use &quot;:&quot; as a separator
themselves, use &quot;,&quot; instead. They accept it as well since long ago but this
is kept undocumented for some reason.
</p>
<p>For example to specify libx264 encoding options with <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -i foo.mpg -c:v libx264 -x264opts keyint=123:min-keyint=20 -an out.mkv
</pre></div>

</dd>
<dt><span><samp>a53cc <var>boolean</var></samp></span></dt>
<dd><p>Import closed captions (which must be ATSC compatible format) into output.
Only the mpeg2 and h264 decoders provide these. Default is 1 (on).
</p>
</dd>
<dt><span><samp>udu_sei <var>boolean</var></samp></span></dt>
<dd><p>Import user data unregistered SEI if available into output. Default is 0 (off).
</p>
</dd>
<dt><span><samp>x264-params (N.A.)</samp></span></dt>
<dd><p>Override the x264 configuration using a :-separated list of key=value
parameters.
</p>
<p>This option is functionally the same as the <samp>x264opts</samp>, but is
duplicated for compatibility with the Libav fork.
</p>
<p>For example to specify libx264 encoding options with <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:v libx264 -x264-params level=30:bframes=0:weightp=0:\
cabac=0:ref=1:vbv-maxrate=768:vbv-bufsize=2000:analyse=all:me=umh:\
no-fast-pskip=1:subq=6:8x8dct=0:trellis=0 OUTPUT
</pre></div>
</dd>
</dl>

<p>Encoding ffpresets for common usages are provided so they can be used with the
general presets system (e.g. passing the <samp>pre</samp> option).
</p>
<a name="libx265"></a>
<h3 class="section">9.16 libx265<span class="pull-right"><a class="anchor hidden-xs" href="#libx265" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libx265" aria-hidden="true">TOC</a></span></h3>

<p>x265 H.265/HEVC encoder wrapper.
</p>
<p>This encoder requires the presence of the libx265 headers and library
during configuration. You need to explicitly configure the build with
<samp>--enable-libx265</samp>.
</p>
<a name="Options-35"></a>
<h4 class="subsection">9.16.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-35" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-35" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Sets target video bitrate.
</p>
</dd>
<dt><span><samp>bf</samp></span></dt>
<dt><span><samp>g</samp></span></dt>
<dd><p>Set the GOP size.
</p>
</dd>
<dt><span><samp>keyint_min</samp></span></dt>
<dd><p>Minimum GOP size.
</p>
</dd>
<dt><span><samp>refs</samp></span></dt>
<dd><p>Number of reference frames each P-frame can use. The range is from <var>1-16</var>.
</p>
</dd>
<dt><span><samp>preset</samp></span></dt>
<dd><p>Set the x265 preset.
</p>
</dd>
<dt><span><samp>tune</samp></span></dt>
<dd><p>Set the x265 tune parameter.
</p>
</dd>
<dt><span><samp>profile</samp></span></dt>
<dd><p>Set profile restrictions.
</p>
</dd>
<dt><span><samp>crf</samp></span></dt>
<dd><p>Set the quality for constant quality mode.
</p>
</dd>
<dt><span><samp>qp</samp></span></dt>
<dd><p>Set constant quantization rate control method parameter.
</p>
</dd>
<dt><span><samp>qmin</samp></span></dt>
<dd><p>Minimum quantizer scale.
</p>
</dd>
<dt><span><samp>qmax</samp></span></dt>
<dd><p>Maximum quantizer scale.
</p>
</dd>
<dt><span><samp>qdiff</samp></span></dt>
<dd><p>Maximum difference between quantizer scales.
</p>
</dd>
<dt><span><samp>qblur</samp></span></dt>
<dd><p>Quantizer curve blur
</p>
</dd>
<dt><span><samp>qcomp</samp></span></dt>
<dd><p>Quantizer curve compression factor
</p>
</dd>
<dt><span><samp>i_qfactor</samp></span></dt>
<dt><span><samp>b_qfactor</samp></span></dt>
<dt><span><samp>forced-idr</samp></span></dt>
<dd><p>Normally, when forcing a I-frame type, the encoder can select any type
of I-frame. This option forces it to choose an IDR-frame.
</p>
</dd>
<dt><span><samp>udu_sei <var>boolean</var></samp></span></dt>
<dd><p>Import user data unregistered SEI if available into output. Default is 0 (off).
</p>
</dd>
<dt><span><samp>x265-params</samp></span></dt>
<dd><p>Set x265 options using a list of <var>key</var>=<var>value</var> couples separated
by &quot;:&quot;. See <code>x265 --help</code> for a list of options.
</p>
<p>For example to specify libx265 encoding options with <samp>-x265-params</samp>:
</p>
<div class="example">
<pre class="example">ffmpeg -i input -c:v libx265 -x265-params crf=26:psy-rd=1 output.mp4
</pre></div>
</dd>
</dl>

<a name="libxavs2"></a>
<h3 class="section">9.17 libxavs2<span class="pull-right"><a class="anchor hidden-xs" href="#libxavs2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libxavs2" aria-hidden="true">TOC</a></span></h3>

<p>xavs2 AVS2-P2/IEEE1857.4 encoder wrapper.
</p>
<p>This encoder requires the presence of the libxavs2 headers and library
during configuration. You need to explicitly configure the build with
<samp>--enable-libxavs2</samp>.
</p>
<p>The following standard libavcodec options are used:
</p><ul>
<li> <samp>b</samp> / <samp>bit_rate</samp>
</li><li> <samp>g</samp> / <samp>gop_size</samp>
</li><li> <samp>bf</samp> / <samp>max_b_frames</samp>
</li></ul>

<p>The encoder also has its own specific options:
</p><a name="Options-36"></a>
<h4 class="subsection">9.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-36" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-36" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>lcu_row_threads</samp></span></dt>
<dd><p>Set the number of parallel threads for rows from 1 to 8 (default 5).
</p>
</dd>
<dt><span><samp>initial_qp</samp></span></dt>
<dd><p>Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the initial qp for the first frame.
</p>
</dd>
<dt><span><samp>qp</samp></span></dt>
<dd><p>Set the xavs2 quantization parameter from 1 to 63 (default 34). This is
used to set the qp value under constant-QP mode.
</p>
</dd>
<dt><span><samp>max_qp</samp></span></dt>
<dd><p>Set the max qp for rate control from 1 to 63 (default 55).
</p>
</dd>
<dt><span><samp>min_qp</samp></span></dt>
<dd><p>Set the min qp for rate control from 1 to 63 (default 20).
</p>
</dd>
<dt><span><samp>speed_level</samp></span></dt>
<dd><p>Set the Speed level from 0 to 9 (default 0). Higher is better but slower.
</p>
</dd>
<dt><span><samp>log_level</samp></span></dt>
<dd><p>Set the log level from -1 to 3 (default 0). -1: none, 0: error,
1: warning, 2: info, 3: debug.
</p>
</dd>
<dt><span><samp>xavs2-params</samp></span></dt>
<dd><p>Set xavs2 options using a list of <var>key</var>=<var>value</var> couples separated
by &quot;:&quot;.
</p>
<p>For example to specify libxavs2 encoding options with <samp>-xavs2-params</samp>:
</p>
<div class="example">
<pre class="example">ffmpeg -i input -c:v libxavs2 -xavs2-params RdoqLevel=0 output.avs2
</pre></div>
</dd>
</dl>

<a name="libxvid"></a>
<h3 class="section">9.18 libxvid<span class="pull-right"><a class="anchor hidden-xs" href="#libxvid" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libxvid" aria-hidden="true">TOC</a></span></h3>

<p>Xvid MPEG-4 Part 2 encoder wrapper.
</p>
<p>This encoder requires the presence of the libxvidcore headers and library
during configuration. You need to explicitly configure the build with
<code>--enable-libxvid --enable-gpl</code>.
</p>
<p>The native <code>mpeg4</code> encoder supports the MPEG-4 Part 2 format, so
users can encode to this format without this library.
</p>
<a name="Options-37"></a>
<h4 class="subsection">9.18.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-37" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-37" aria-hidden="true">TOC</a></span></h4>

<p>The following options are supported by the libxvid wrapper. Some of
the following options are listed but are not documented, and
correspond to shared codec options. See <a href="#codec_002doptions">the Codec
Options chapter</a> for their documentation. The other shared options
which are not listed have no effect for the libxvid encoder.
</p>
<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dt><span><samp>g</samp></span></dt>
<dt><span><samp>qmin</samp></span></dt>
<dt><span><samp>qmax</samp></span></dt>
<dt><span><samp>mpeg_quant</samp></span></dt>
<dt><span><samp>threads</samp></span></dt>
<dt><span><samp>bf</samp></span></dt>
<dt><span><samp>b_qfactor</samp></span></dt>
<dt><span><samp>b_qoffset</samp></span></dt>
<dt><span><samp>flags</samp></span></dt>
<dd><p>Set specific encoding flags. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>mv4</samp>&rsquo;</span></dt>
<dd><p>Use four motion vector by macroblock.
</p>
</dd>
<dt><span>&lsquo;<samp>aic</samp>&rsquo;</span></dt>
<dd><p>Enable high quality AC prediction.
</p>
</dd>
<dt><span>&lsquo;<samp>gray</samp>&rsquo;</span></dt>
<dd><p>Only encode grayscale.
</p>
</dd>
<dt><span>&lsquo;<samp>gmc</samp>&rsquo;</span></dt>
<dd><p>Enable the use of global motion compensation (GMC).
</p>
</dd>
<dt><span>&lsquo;<samp>qpel</samp>&rsquo;</span></dt>
<dd><p>Enable quarter-pixel motion compensation.
</p>
</dd>
<dt><span>&lsquo;<samp>cgop</samp>&rsquo;</span></dt>
<dd><p>Enable closed GOP.
</p>
</dd>
<dt><span>&lsquo;<samp>global_header</samp>&rsquo;</span></dt>
<dd><p>Place global headers in extradata instead of every keyframe.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>trellis</samp></span></dt>
<dt><span><samp>me_method</samp></span></dt>
<dd><p>Set motion estimation method. Possible values in decreasing order of
speed and increasing order of quality:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>zero</samp>&rsquo;</span></dt>
<dd><p>Use no motion estimation (default).
</p>
</dd>
<dt><span>&lsquo;<samp>phods</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>x1</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>log</samp>&rsquo;</span></dt>
<dd><p>Enable advanced diamond zonal search for 16x16 blocks and half-pixel
refinement for 16x16 blocks. &lsquo;<samp>x1</samp>&rsquo; and &lsquo;<samp>log</samp>&rsquo; are aliases for
&lsquo;<samp>phods</samp>&rsquo;.
</p>
</dd>
<dt><span>&lsquo;<samp>epzs</samp>&rsquo;</span></dt>
<dd><p>Enable all of the things described above, plus advanced diamond zonal
search for 8x8 blocks, half-pixel refinement for 8x8 blocks, and motion
estimation on chroma planes.
</p>
</dd>
<dt><span>&lsquo;<samp>full</samp>&rsquo;</span></dt>
<dd><p>Enable all of the things described above, plus extended 16x16 and 8x8
blocks search.
</p></dd>
</dl>

</dd>
<dt><span><samp>mbd</samp></span></dt>
<dd><p>Set macroblock decision algorithm. Possible values in the increasing
order of quality:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>simple</samp>&rsquo;</span></dt>
<dd><p>Use macroblock comparing function algorithm (default).
</p>
</dd>
<dt><span>&lsquo;<samp>bits</samp>&rsquo;</span></dt>
<dd><p>Enable rate distortion-based half pixel and quarter pixel refinement for
16x16 blocks.
</p>
</dd>
<dt><span>&lsquo;<samp>rd</samp>&rsquo;</span></dt>
<dd><p>Enable all of the things described above, plus rate distortion-based
half pixel and quarter pixel refinement for 8x8 blocks, and rate
distortion-based search using square pattern.
</p></dd>
</dl>

</dd>
<dt><span><samp>lumi_aq</samp></span></dt>
<dd><p>Enable lumi masking adaptive quantization when set to 1. Default is 0
(disabled).
</p>
</dd>
<dt><span><samp>variance_aq</samp></span></dt>
<dd><p>Enable variance adaptive quantization when set to 1. Default is 0
(disabled).
</p>
<p>When combined with <samp>lumi_aq</samp>, the resulting quality will not
be better than any of the two specified individually. In other
words, the resulting quality will be the worse one of the two
effects.
</p>
</dd>
<dt><span><samp>ssim</samp></span></dt>
<dd><p>Set structural similarity (SSIM) displaying method. Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>off</samp>&rsquo;</span></dt>
<dd><p>Disable displaying of SSIM information.
</p>
</dd>
<dt><span>&lsquo;<samp>avg</samp>&rsquo;</span></dt>
<dd><p>Output average SSIM at the end of encoding to stdout. The format of
showing the average SSIM is:
</p>
<div class="example">
<pre class="example">Average SSIM: %f
</pre></div>

<p>For users who are not familiar with C, %f means a float number, or
a decimal (e.g. 0.939232).
</p>
</dd>
<dt><span>&lsquo;<samp>frame</samp>&rsquo;</span></dt>
<dd><p>Output both per-frame SSIM data during encoding and average SSIM at
the end of encoding to stdout. The format of per-frame information
is:
</p>
<div class="example">
<pre class="example">       SSIM: avg: %1.3f min: %1.3f max: %1.3f
</pre></div>

<p>For users who are not familiar with C, %1.3f means a float number
rounded to 3 digits after the dot (e.g. 0.932).
</p>
</dd>
</dl>

</dd>
<dt><span><samp>ssim_acc</samp></span></dt>
<dd><p>Set SSIM accuracy. Valid options are integers within the range of
0-4, while 0 gives the most accurate result and 4 computes the
fastest.
</p>
</dd>
</dl>

<a name="MediaFoundation"></a>
<h3 class="section">9.19 MediaFoundation<span class="pull-right"><a class="anchor hidden-xs" href="#MediaFoundation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-MediaFoundation" aria-hidden="true">TOC</a></span></h3>

<p>This provides wrappers to encoders (both audio and video) in the
MediaFoundation framework. It can access both SW and HW encoders.
Video encoders can take input in either of nv12 or yuv420p form
(some encoders support both, some support only either - in practice,
nv12 is the safer choice, especially among HW encoders).
</p>
<a name="mpeg2"></a>
<h3 class="section">9.20 mpeg2<span class="pull-right"><a class="anchor hidden-xs" href="#mpeg2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpeg2" aria-hidden="true">TOC</a></span></h3>

<p>MPEG-2 video encoder.
</p>
<a name="Options-38"></a>
<h4 class="subsection">9.20.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-38" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-38" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>profile</samp></span></dt>
<dd><p>Select the mpeg2 profile to encode:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>422</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>ss</samp>&rsquo;</span></dt>
<dd><p>Spatially Scalable
</p></dd>
<dt><span>&lsquo;<samp>snr</samp>&rsquo;</span></dt>
<dd><p>SNR Scalable
</p></dd>
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simple</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>level</samp></span></dt>
<dd><p>Select the mpeg2 level to encode:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>high1440</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>low</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>seq_disp_ext <var>integer</var></samp></span></dt>
<dd><p>Specifies if the encoder should write a sequence_display_extension to the
output.
</p><dl compact="compact">
<dt><span><samp>-1</samp></span></dt>
<dt><span><samp>auto</samp></span></dt>
<dd><p>Decide automatically to write it or not (this is the default) by checking if
the data to be written is different from the default or unspecified values.
</p></dd>
<dt><span><samp>0</samp></span></dt>
<dt><span><samp>never</samp></span></dt>
<dd><p>Never write it.
</p></dd>
<dt><span><samp>1</samp></span></dt>
<dt><span><samp>always</samp></span></dt>
<dd><p>Always write it.
</p></dd>
</dl>
</dd>
<dt><span><samp>video_format <var>integer</var></samp></span></dt>
<dd><p>Specifies the video_format written into the sequence display extension
indicating the source of the video pictures. The default is &lsquo;<samp>unspecified</samp>&rsquo;,
can be &lsquo;<samp>component</samp>&rsquo;, &lsquo;<samp>pal</samp>&rsquo;, &lsquo;<samp>ntsc</samp>&rsquo;, &lsquo;<samp>secam</samp>&rsquo; or &lsquo;<samp>mac</samp>&rsquo;.
For maximum compatibility, use &lsquo;<samp>component</samp>&rsquo;.
</p></dd>
<dt><span><samp>a53cc <var>boolean</var></samp></span></dt>
<dd><p>Import closed captions (which must be ATSC compatible format) into output.
Default is 1 (on).
</p></dd>
</dl>

<a name="png"></a>
<h3 class="section">9.21 png<span class="pull-right"><a class="anchor hidden-xs" href="#png" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-png" aria-hidden="true">TOC</a></span></h3>

<p>PNG image encoder.
</p>
<a name="Private-options-1"></a>
<h4 class="subsection">9.21.1 Private options<span class="pull-right"><a class="anchor hidden-xs" href="#Private-options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Private-options-1" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>dpi <var>integer</var></samp></span></dt>
<dd><p>Set physical density of pixels, in dots per inch, unset by default
</p></dd>
<dt><span><samp>dpm <var>integer</var></samp></span></dt>
<dd><p>Set physical density of pixels, in dots per meter, unset by default
</p></dd>
</dl>

<a name="ProRes"></a>
<h3 class="section">9.22 ProRes<span class="pull-right"><a class="anchor hidden-xs" href="#ProRes" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ProRes" aria-hidden="true">TOC</a></span></h3>

<p>Apple ProRes encoder.
</p>
<p>FFmpeg contains 2 ProRes encoders, the prores-aw and prores-ks encoder.
The used encoder can be chosen with the <code>-vcodec</code> option.
</p>
<a name="Private-Options-for-prores_002dks"></a>
<h4 class="subsection">9.22.1 Private Options for prores-ks<span class="pull-right"><a class="anchor hidden-xs" href="#Private-Options-for-prores_002dks" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Private-Options-for-prores_002dks" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>profile <var>integer</var></samp></span></dt>
<dd><p>Select the ProRes profile to encode
</p><dl compact="compact">
<dt><span>&lsquo;<samp>proxy</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>lt</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>standard</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hq</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>4444</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>4444xq</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>quant_mat <var>integer</var></samp></span></dt>
<dd><p>Select quantization matrix.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>default</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>proxy</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>lt</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>standard</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hq</samp>&rsquo;</span></dt>
</dl>
<p>If set to <var>auto</var>, the matrix matching the profile will be picked.
If not set, the matrix providing the highest quality, <var>default</var>, will be
picked.
</p>
</dd>
<dt><span><samp>bits_per_mb <var>integer</var></samp></span></dt>
<dd><p>How many bits to allot for coding one macroblock. Different profiles use
between 200 and 2400 bits per macroblock, the maximum is 8000.
</p>
</dd>
<dt><span><samp>mbs_per_slice <var>integer</var></samp></span></dt>
<dd><p>Number of macroblocks in each slice (1-8); the default value (8)
should be good in almost all situations.
</p>
</dd>
<dt><span><samp>vendor <var>string</var></samp></span></dt>
<dd><p>Override the 4-byte vendor ID.
A custom vendor ID like <var>apl0</var> would claim the stream was produced by
the Apple encoder.
</p>
</dd>
<dt><span><samp>alpha_bits <var>integer</var></samp></span></dt>
<dd><p>Specify number of bits for alpha component.
Possible values are <var>0</var>, <var>8</var> and <var>16</var>.
Use <var>0</var> to disable alpha plane coding.
</p>
</dd>
</dl>

<a name="Speed-considerations"></a>
<h4 class="subsection">9.22.2 Speed considerations<span class="pull-right"><a class="anchor hidden-xs" href="#Speed-considerations" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Speed-considerations" aria-hidden="true">TOC</a></span></h4>

<p>In the default mode of operation the encoder has to honor frame constraints
(i.e. not produce frames with size bigger than requested) while still making
output picture as good as possible.
A frame containing a lot of small details is harder to compress and the encoder
would spend more time searching for appropriate quantizers for each slice.
</p>
<p>Setting a higher <samp>bits_per_mb</samp> limit will improve the speed.
</p>
<p>For the fastest encoding speed set the <samp>qscale</samp> parameter (4 is the
recommended value) and do not set a size constraint.
</p>
<a name="QSV-Encoders"></a>
<h3 class="section">9.23 QSV Encoders<span class="pull-right"><a class="anchor hidden-xs" href="#QSV-Encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-QSV-Encoders" aria-hidden="true">TOC</a></span></h3>

<p>The family of Intel QuickSync Video encoders (MPEG-2, H.264, HEVC, JPEG/MJPEG
and VP9)
</p>
<a name="Ratecontrol-Method"></a>
<h4 class="subsection">9.23.1 Ratecontrol Method<span class="pull-right"><a class="anchor hidden-xs" href="#Ratecontrol-Method" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Ratecontrol-Method" aria-hidden="true">TOC</a></span></h4>
<p>The ratecontrol method is selected as follows:
</p><ul>
<li> When <samp>global_quality</samp> is specified, a quality-based mode is used.
Specifically this means either
<ul class="no-bullet">
<li>- <var>CQP</var> - constant quantizer scale, when the <samp>qscale</samp> codec flag is
also set (the <samp>-qscale</samp> ffmpeg option).

</li><li>- <var>LA_ICQ</var> - intelligent constant quality with lookahead, when the
<samp>look_ahead</samp> option is also set.

</li><li>- <var>ICQ</var> &ndash; intelligent constant quality otherwise. For the ICQ modes, global
quality range is 1 to 51, with 1 being the best quality.
</li></ul>

</li><li> Otherwise, a bitrate-based mode is used. For all of those, you should specify at
least the desired average bitrate with the <samp>b</samp> option.
<ul class="no-bullet">
<li>- <var>LA</var> - VBR with lookahead, when the <samp>look_ahead</samp> option is specified.

</li><li>- <var>VCM</var> - video conferencing mode, when the <samp>vcm</samp> option is set.

</li><li>- <var>CBR</var> - constant bitrate, when <samp>maxrate</samp> is specified and equal to
the average bitrate.

</li><li>- <var>VBR</var> - variable bitrate, when <samp>maxrate</samp> is specified, but is higher
than the average bitrate.

</li><li>- <var>AVBR</var> - average VBR mode, when <samp>maxrate</samp> is not specified. This mode
is further configured by the <samp>avbr_accuracy</samp> and
<samp>avbr_convergence</samp> options.
</li></ul>
</li></ul>

<p>Note that depending on your system, a different mode than the one you specified
may be selected by the encoder. Set the verbosity level to <var>verbose</var> or
higher to see the actual settings used by the QSV runtime.
</p>
<a name="Global-Options-_002d_003e-MSDK-Options"></a>
<h4 class="subsection">9.23.2 Global Options -&gt; MSDK Options<span class="pull-right"><a class="anchor hidden-xs" href="#Global-Options-_002d_003e-MSDK-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Global-Options-_002d_003e-MSDK-Options" aria-hidden="true">TOC</a></span></h4>
<p>Additional libavcodec global options are mapped to MSDK options as follows:
</p>
<ul>
<li> <samp>g/gop_size</samp> -&gt; <samp>GopPicSize</samp>

</li><li> <samp>bf/max_b_frames</samp>+1 -&gt; <samp>GopRefDist</samp>

</li><li> <samp>rc_init_occupancy/rc_initial_buffer_occupancy</samp> -&gt;
<samp>InitialDelayInKB</samp>

</li><li> <samp>slices</samp> -&gt; <samp>NumSlice</samp>

</li><li> <samp>refs</samp> -&gt; <samp>NumRefFrame</samp>

</li><li> <samp>b_strategy/b_frame_strategy</samp> -&gt; <samp>BRefType</samp>

</li><li> <samp>cgop/CLOSED_GOP</samp> codec flag -&gt; <samp>GopOptFlag</samp>

</li><li> For the <var>CQP</var> mode, the <samp>i_qfactor/i_qoffset</samp> and
<samp>b_qfactor/b_qoffset</samp> set the difference between <var>QPP</var> and <var>QPI</var>,
and <var>QPP</var> and <var>QPB</var> respectively.

</li><li> Setting the <samp>coder</samp> option to the value <var>vlc</var> will make the H.264
encoder use CAVLC instead of CABAC.

</li></ul>

<a name="Common-Options-1"></a>
<h4 class="subsection">9.23.3 Common Options<span class="pull-right"><a class="anchor hidden-xs" href="#Common-Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Common-Options-1" aria-hidden="true">TOC</a></span></h4>
<p>Following options are used by all qsv encoders.
</p>
<dl compact="compact">
<dt><span><samp><var>async_depth</var></samp></span></dt>
<dd><p>Specifies how many asynchronous operations an application performs
before the application explicitly synchronizes the result. If zero,
the value is not specified.
</p>
</dd>
<dt><span><samp><var>avbr_accuracy</var></samp></span></dt>
<dd><p>Accuracy of the AVBR ratecontrol (unit of tenth of percent).
</p>
</dd>
<dt><span><samp><var>avbr_convergence</var></samp></span></dt>
<dd><p>Convergence of the AVBR ratecontrol (unit of 100 frames)
</p>
<p>The parameters <var>avbr_accuracy</var> and <var>avbr_convergence</var> are for the
average variable bitrate control (AVBR) algorithm.
The algorithm focuses on overall encoding quality while meeting the specified
bitrate, <var>target_bitrate</var>, within the accuracy range <var>avbr_accuracy</var>,
after a <var>avbr_Convergence</var> period. This method does not follow HRD and the
instant bitrate is not capped or padded.
</p>
</dd>
<dt><span><samp><var>preset</var></samp></span></dt>
<dd><p>This option itemizes a range of choices from veryfast (best speed) to veryslow
(best quality).
</p><dl compact="compact">
<dt><span>&lsquo;<samp>veryfast</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>faster</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>fast</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>medium</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>slow</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>slower</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>veryslow</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>forced_idr</var></samp></span></dt>
<dd><p>Forcing I frames as IDR frames.
</p>
</dd>
<dt><span><samp><var>low_power</var></samp></span></dt>
<dd><p>For encoders set this flag to ON to reduce power consumption and GPU usage.
</p></dd>
</dl>

<a name="Runtime-Options"></a>
<h4 class="subsection">9.23.4 Runtime Options<span class="pull-right"><a class="anchor hidden-xs" href="#Runtime-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Runtime-Options" aria-hidden="true">TOC</a></span></h4>
<p>Following options can be used durning qsv encoding.
</p>
<dl compact="compact">
<dt><span><samp><var>qsv_config_qp</var></samp></span></dt>
<dd><p>Supported in h264_qsv and hevc_qsv.
This option can be set in per-frame metadata. QP parameter can be dynamically
changed when encoding in CQP mode.
</p></dd>
</dl>

<a name="H264-options"></a>
<h4 class="subsection">9.23.5 H264 options<span class="pull-right"><a class="anchor hidden-xs" href="#H264-options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-H264-options" aria-hidden="true">TOC</a></span></h4>
<p>These options are used by h264_qsv
</p>
<dl compact="compact">
<dt><span><samp><var>extbrc</var></samp></span></dt>
<dd><p>Extended bitrate control.
</p>
</dd>
<dt><span><samp><var>recovery_point_sei</var></samp></span></dt>
<dd><p>Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
</p>
</dd>
<dt><span><samp><var>rdo</var></samp></span></dt>
<dd><p>Enable rate distortion optimization.
</p>
</dd>
<dt><span><samp><var>max_frame_size</var></samp></span></dt>
<dd><p>Maximum encoded frame size in bytes.
</p>
</dd>
<dt><span><samp><var>max_frame_size_i</var></samp></span></dt>
<dd><p>Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><span><samp><var>max_frame_size_p</var></samp></span></dt>
<dd><p>Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><span><samp><var>max_slice_size</var></samp></span></dt>
<dd><p>Maximum encoded slice size in bytes.
</p>
</dd>
<dt><span><samp><var>bitrate_limit</var></samp></span></dt>
<dd><p>Toggle bitrate limitations.
Modifies bitrate to be in the range imposed by the QSV encoder. Setting this
flag off may lead to violation of HRD conformance. Mind that specifying bitrate
below the QSV encoder range might significantly affect quality. If on this
option takes effect in non CQP modes: if bitrate is not in the range imposed
by the QSV encoder, it will be changed to be in the range.
</p>
</dd>
<dt><span><samp><var>mbbrc</var></samp></span></dt>
<dd><p>Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
</p>
</dd>
<dt><span><samp><var>low_delay_brc</var></samp></span></dt>
<dd><p>Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on
</p>
</dd>
<dt><span><samp><var>adaptive_i</var></samp></span></dt>
<dd><p>This flag controls insertion of I frames by the QSV encoder. Turn ON this flag
to allow changing of frame type from P and B to I.
</p>
</dd>
<dt><span><samp><var>adaptive_b</var></samp></span></dt>
<dd><p>This flag controls changing of frame type from B to P.
</p>
</dd>
<dt><span><samp><var>p_strategy</var></samp></span></dt>
<dd><p>Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).
</p>
</dd>
<dt><span><samp><var>b_strategy</var></samp></span></dt>
<dd><p>This option controls usage of B frames as reference.
</p>
</dd>
<dt><span><samp><var>dblk_idc</var></samp></span></dt>
<dd><p>This option disable deblocking. It has value in range 0~2.
</p>
</dd>
<dt><span><samp><var>cavlc</var></samp></span></dt>
<dd><p>If set, CAVLC is used; if unset, CABAC is used for encoding.
</p>
</dd>
<dt><span><samp><var>vcm</var></samp></span></dt>
<dd><p>Video conferencing mode, please see ratecontrol method.
</p>
</dd>
<dt><span><samp><var>idr_interval</var></samp></span></dt>
<dd><p>Distance (in I-frames) between IDR frames.
</p>
</dd>
<dt><span><samp><var>pic_timing_sei</var></samp></span></dt>
<dd><p>Insert picture timing SEI with pic_struct_syntax element.
</p>
</dd>
<dt><span><samp><var>single_sei_nal_unit</var></samp></span></dt>
<dd><p>Put all the SEI messages into one NALU.
</p>
</dd>
<dt><span><samp><var>max_dec_frame_buffering</var></samp></span></dt>
<dd><p>Maximum number of frames buffered in the DPB.
</p>
</dd>
<dt><span><samp><var>look_ahead</var></samp></span></dt>
<dd><p>Use VBR algorithm with look ahead.
</p>
</dd>
<dt><span><samp><var>look_ahead_depth</var></samp></span></dt>
<dd><p>Depth of look ahead in number frames.
</p>
</dd>
<dt><span><samp><var>look_ahead_downsampling</var></samp></span></dt>
<dd><p>Downscaling factor for the frames saved for the lookahead analysis.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>off</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>2x</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>4x</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>int_ref_type</var></samp></span></dt>
<dd><p>Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. <var>none</var> means no refresh. <var>vertical</var> means
vertical refresh, by column of MBs. To enable intra refresh, B frame should be
set to 0.
</p>
</dd>
<dt><span><samp><var>int_ref_cycle_size</var></samp></span></dt>
<dd><p>Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
</p>
</dd>
<dt><span><samp><var>int_ref_qp_delta</var></samp></span></dt>
<dd><p>Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.
</p>
</dd>
<dt><span><samp><var>int_ref_cycle_dist</var></samp></span></dt>
<dd><p>Distance between the beginnings of the intra-refresh cycles in frames.
</p>
</dd>
<dt><span><samp><var>profile</var></samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>baseline</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>a53cc</var></samp></span></dt>
<dd><p>Use A53 Closed Captions (if available).
</p>
</dd>
<dt><span><samp><var>aud</var></samp></span></dt>
<dd><p>Insert the Access Unit Delimiter NAL.
</p>
</dd>
<dt><span><samp><var>mfmode</var></samp></span></dt>
<dd><p>Multi-Frame Mode.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>off</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>repeat_pps</var></samp></span></dt>
<dd><p>Repeat pps for every frame.
</p>
</dd>
<dt><span><samp><var>max_qp_i</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for I frame.
</p>
</dd>
<dt><span><samp><var>min_qp_i</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for I frame.
</p>
</dd>
<dt><span><samp><var>max_qp_p</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for P frame.
</p>
</dd>
<dt><span><samp><var>min_qp_p</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for P frame.
</p>
</dd>
<dt><span><samp><var>max_qp_b</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for B frame.
</p>
</dd>
<dt><span><samp><var>min_qp_b</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for B frame.
</p></dd>
</dl>

<a name="HEVC-Options-1"></a>
<h4 class="subsection">9.23.6 HEVC Options<span class="pull-right"><a class="anchor hidden-xs" href="#HEVC-Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-HEVC-Options-1" aria-hidden="true">TOC</a></span></h4>
<p>These options are used by hevc_qsv
</p>
<dl compact="compact">
<dt><span><samp><var>extbrc</var></samp></span></dt>
<dd><p>Extended bitrate control.
</p>
</dd>
<dt><span><samp><var>recovery_point_sei</var></samp></span></dt>
<dd><p>Set this flag to insert the recovery point SEI message at the beginning of every
intra refresh cycle.
</p>
</dd>
<dt><span><samp><var>rdo</var></samp></span></dt>
<dd><p>Enable rate distortion optimization.
</p>
</dd>
<dt><span><samp><var>max_frame_size</var></samp></span></dt>
<dd><p>Maximum encoded frame size in bytes.
</p>
</dd>
<dt><span><samp><var>max_frame_size_i</var></samp></span></dt>
<dd><p>Maximum encoded frame size for I frames in bytes. If this value is set as larger
than zero, then for I frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><span><samp><var>max_frame_size_p</var></samp></span></dt>
<dd><p>Maximum encoded frame size for P frames in bytes. If this value is set as larger
than zero, then for P frames the value set by max_frame_size is ignored.
</p>
</dd>
<dt><span><samp><var>max_slice_size</var></samp></span></dt>
<dd><p>Maximum encoded slice size in bytes.
</p>
</dd>
<dt><span><samp><var>mbbrc</var></samp></span></dt>
<dd><p>Setting this flag enables macroblock level bitrate control that generally
improves subjective visual quality. Enabling this flag may have negative impact
on performance and objective visual quality metric.
</p>
</dd>
<dt><span><samp><var>low_delay_brc</var></samp></span></dt>
<dd><p>Setting this flag turns on or off LowDelayBRC feautre in qsv plugin, which provides
more accurate bitrate control to minimize the variance of bitstream size frame
by frame. Value: -1-default 0-off 1-on
</p>
</dd>
<dt><span><samp><var>p_strategy</var></samp></span></dt>
<dd><p>Enable P-pyramid: 0-default 1-simple 2-pyramid(bf need to be set to 0).
</p>
</dd>
<dt><span><samp><var>b_strategy</var></samp></span></dt>
<dd><p>This option controls usage of B frames as reference.
</p>
</dd>
<dt><span><samp><var>dblk_idc</var></samp></span></dt>
<dd><p>This option disable deblocking. It has value in range 0~2.
</p>
</dd>
<dt><span><samp><var>idr_interval</var></samp></span></dt>
<dd><p>Distance (in I-frames) between IDR frames.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>begin_only</samp>&rsquo;</span></dt>
<dd><p>Output an IDR-frame only at the beginning of the stream.
</p></dd>
</dl>

</dd>
<dt><span><samp><var>load_plugin</var></samp></span></dt>
<dd><p>A user plugin to load in an internal session.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hevc_sw</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>hevc_hw</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>load_plugins</var></samp></span></dt>
<dd><p>A :-separate list of hexadecimal plugin UIDs to load in
an internal session.
</p>
</dd>
<dt><span><samp><var>look_ahead_depth</var></samp></span></dt>
<dd><p>Depth of look ahead in number frames, available when extbrc option is enabled.
</p>
</dd>
<dt><span><samp><var>profile</var></samp></span></dt>
<dd><p>Set the encoding profile (scc requires libmfx &gt;= 1.32).
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>main10</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>mainsp</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>rext</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>scc</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>gpb</var></samp></span></dt>
<dd><p>1: GPB (generalized P/B frame)
</p>
<p>0: regular P frame.
</p>
</dd>
<dt><span><samp><var>tile_cols</var></samp></span></dt>
<dd><p>Number of columns for tiled encoding.
</p>
</dd>
<dt><span><samp><var>tile_rows</var></samp></span></dt>
<dd><p>Number of rows for tiled encoding.
</p>
</dd>
<dt><span><samp><var>aud</var></samp></span></dt>
<dd><p>Insert the Access Unit Delimiter NAL.
</p>
</dd>
<dt><span><samp><var>pic_timing_sei</var></samp></span></dt>
<dd><p>Insert picture timing SEI with pic_struct_syntax element.
</p>
</dd>
<dt><span><samp><var>transform_skip</var></samp></span></dt>
<dd><p>Turn this option ON to enable transformskip. It is supported on platform equal
or newer than ICL.
</p>
</dd>
<dt><span><samp><var>int_ref_type</var></samp></span></dt>
<dd><p>Specifies intra refresh type. The major goal of intra refresh is improvement of
error resilience without significant impact on encoded bitstream size caused by
I frames. The SDK encoder achieves this by encoding part of each frame in
refresh cycle using intra MBs. <var>none</var> means no refresh. <var>vertical</var> means
vertical refresh, by column of MBs. To enable intra refresh, B frame should be
set to 0.
</p>
</dd>
<dt><span><samp><var>int_ref_cycle_size</var></samp></span></dt>
<dd><p>Specifies number of pictures within refresh cycle starting from 2. 0 and 1 are
invalid values.
</p>
</dd>
<dt><span><samp><var>int_ref_qp_delta</var></samp></span></dt>
<dd><p>Specifies QP difference for inserted intra MBs. This is signed value in
[-51, 51] range if target encoding bit-depth for luma samples is 8 and this
range is [-63, 63] for 10 bit-depth or [-75, 75] for 12 bit-depth respectively.
</p>
</dd>
<dt><span><samp><var>int_ref_cycle_dist</var></samp></span></dt>
<dd><p>Distance between the beginnings of the intra-refresh cycles in frames.
</p>
</dd>
<dt><span><samp><var>max_qp_i</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for I frame.
</p>
</dd>
<dt><span><samp><var>min_qp_i</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for I frame.
</p>
</dd>
<dt><span><samp><var>max_qp_p</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for P frame.
</p>
</dd>
<dt><span><samp><var>min_qp_p</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for P frame.
</p>
</dd>
<dt><span><samp><var>max_qp_b</var></samp></span></dt>
<dd><p>Maximum video quantizer scale for B frame.
</p>
</dd>
<dt><span><samp><var>min_qp_b</var></samp></span></dt>
<dd><p>Minimum video quantizer scale for B frame.
</p></dd>
</dl>

<a name="MPEG2-Options"></a>
<h4 class="subsection">9.23.7 MPEG2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#MPEG2-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-MPEG2-Options" aria-hidden="true">TOC</a></span></h4>
<p>These options are used by mpeg2_qsv
</p><dl compact="compact">
<dt><span><samp><var>profile</var></samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>simple</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>main</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>high</samp>&rsquo;</span></dt>
</dl>
</dd>
</dl>

<a name="VP9-Options"></a>
<h4 class="subsection">9.23.8 VP9 Options<span class="pull-right"><a class="anchor hidden-xs" href="#VP9-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-VP9-Options" aria-hidden="true">TOC</a></span></h4>
<p>These options are used by vp9_qsv
</p><dl compact="compact">
<dt><span><samp><var>profile</var></samp></span></dt>
<dd><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>profile0</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>profile1</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>profile2</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>profile3</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp><var>tile_cols</var></samp></span></dt>
<dd><p>Number of columns for tiled encoding (requires libmfx &gt;= 1.29).
</p>
</dd>
<dt><span><samp><var>tile_rows</var></samp></span></dt>
<dd><p>Number of rows for tiled encoding (requires libmfx  &gt;= 1.29).
</p></dd>
</dl>

<a name="snow"></a>
<h3 class="section">9.24 snow<span class="pull-right"><a class="anchor hidden-xs" href="#snow" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-snow" aria-hidden="true">TOC</a></span></h3>

<a name="Options-39"></a>
<h4 class="subsection">9.24.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-39" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-39" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>iterative_dia_size</samp></span></dt>
<dd><p>dia size for the iterative motion estimation
</p></dd>
</dl>

<a name="VAAPI-encoders"></a>
<h3 class="section">9.25 VAAPI encoders<span class="pull-right"><a class="anchor hidden-xs" href="#VAAPI-encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-VAAPI-encoders" aria-hidden="true">TOC</a></span></h3>

<p>Wrappers for hardware encoders accessible via VAAPI.
</p>
<p>These encoders only accept input in VAAPI hardware surfaces.  If you have input
in software frames, use the <samp>hwupload</samp> filter to upload them to the GPU.
</p>
<p>The following standard libavcodec options are used:
</p><ul>
<li> <samp>g</samp> / <samp>gop_size</samp>
</li><li> <samp>bf</samp> / <samp>max_b_frames</samp>
</li><li> <samp>profile</samp>

<p>If not set, this will be determined automatically from the format of the input
frames and the profiles supported by the driver.
</p></li><li> <samp>level</samp>
</li><li> <samp>b</samp> / <samp>bit_rate</samp>
</li><li> <samp>maxrate</samp> / <samp>rc_max_rate</samp>
</li><li> <samp>bufsize</samp> / <samp>rc_buffer_size</samp>
</li><li> <samp>rc_init_occupancy</samp> / <samp>rc_initial_buffer_occupancy</samp>
</li><li> <samp>compression_level</samp>

<p>Speed / quality tradeoff: higher values are faster / worse quality.
</p></li><li> <samp>q</samp> / <samp>global_quality</samp>

<p>Size / quality tradeoff: higher values are smaller / worse quality.
</p></li><li> <samp>qmin</samp>
</li><li> <samp>qmax</samp>
</li><li> <samp>i_qfactor</samp> / <samp>i_quant_factor</samp>
</li><li> <samp>i_qoffset</samp> / <samp>i_quant_offset</samp>
</li><li> <samp>b_qfactor</samp> / <samp>b_quant_factor</samp>
</li><li> <samp>b_qoffset</samp> / <samp>b_quant_offset</samp>
</li><li> <samp>slices</samp>
</li></ul>

<p>All encoders support the following options:
</p><dl compact="compact">
<dt><span><samp>low_power</samp></span></dt>
<dd><p>Some drivers/platforms offer a second encoder for some codecs intended to use
less power than the default encoder; setting this option will attempt to use
that encoder.  Note that it may support a reduced feature set, so some other
options may not be available in this mode.
</p>
</dd>
<dt><span><samp>idr_interval</samp></span></dt>
<dd><p>Set the number of normal intra frames between full-refresh (IDR) frames in
open-GOP mode.  The intra frames are still IRAPs, but will not include global
headers and may have non-decodable leading pictures.
</p>
</dd>
<dt><span><samp>b_depth</samp></span></dt>
<dd><p>Set the B-frame reference depth.  When set to one (the default), all B-frames
will refer only to P- or I-frames.  When set to greater values multiple layers
of B-frames will be present, frames in each layer only referring to frames in
higher layers.
</p>
</dd>
<dt><span><samp>async_depth</samp></span></dt>
<dd><p>Maximum processing parallelism. Increase this to improve single channel
performance. This option doesn&rsquo;t work if driver doesn&rsquo;t implement vaSyncBuffer
function. Please make sure there are enough hw_frames allocated if a large
number of async_depth is used.
</p>
</dd>
<dt><span><samp>max_frame_size</samp></span></dt>
<dd><p>Set the allowed max size in bytes for each frame. If the frame size exceeds
the limitation, encoder will adjust the QP value to control the frame size.
Invalid in CQP rate control mode.
</p>
</dd>
<dt><span><samp>rc_mode</samp></span></dt>
<dd><p>Set the rate control mode to use.  A given driver may only support a subset of
modes.
</p>
<p>Possible modes:
</p><dl compact="compact">
<dt><span><samp>auto</samp></span></dt>
<dd><p>Choose the mode automatically based on driver support and the other options.
This is the default.
</p></dd>
<dt><span><samp>CQP</samp></span></dt>
<dd><p>Constant-quality.
</p></dd>
<dt><span><samp>CBR</samp></span></dt>
<dd><p>Constant-bitrate.
</p></dd>
<dt><span><samp>VBR</samp></span></dt>
<dd><p>Variable-bitrate.
</p></dd>
<dt><span><samp>ICQ</samp></span></dt>
<dd><p>Intelligent constant-quality.
</p></dd>
<dt><span><samp>QVBR</samp></span></dt>
<dd><p>Quality-defined variable-bitrate.
</p></dd>
<dt><span><samp>AVBR</samp></span></dt>
<dd><p>Average variable bitrate.
</p></dd>
</dl>

</dd>
</dl>

<p>Each encoder also has its own specific options:
</p><dl compact="compact">
<dt><span><samp>h264_vaapi</samp></span></dt>
<dd><p><samp>profile</samp> sets the value of <em>profile_idc</em> and the <em>constraint_set*_flag</em>s.
<samp>level</samp> sets the value of <em>level_idc</em>.
</p>
<dl compact="compact">
<dt><span><samp>coder</samp></span></dt>
<dd><p>Set entropy encoder (default is <em>cabac</em>).  Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>ac</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>cabac</samp>&rsquo;</span></dt>
<dd><p>Use CABAC.
</p>
</dd>
<dt><span>&lsquo;<samp>vlc</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>cavlc</samp>&rsquo;</span></dt>
<dd><p>Use CAVLC.
</p></dd>
</dl>

</dd>
<dt><span><samp>aud</samp></span></dt>
<dd><p>Include access unit delimiters in the stream (not included by default).
</p>
</dd>
<dt><span><samp>sei</samp></span></dt>
<dd><p>Set SEI message types to include.
Some combination of the following values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>identifier</samp>&rsquo;</span></dt>
<dd><p>Include a <em>user_data_unregistered</em> message containing information about
the encoder.
</p></dd>
<dt><span>&lsquo;<samp>timing</samp>&rsquo;</span></dt>
<dd><p>Include picture timing parameters (<em>buffering_period</em> and
<em>pic_timing</em> messages).
</p></dd>
<dt><span>&lsquo;<samp>recovery_point</samp>&rsquo;</span></dt>
<dd><p>Include recovery points where appropriate (<em>recovery_point</em> messages).
</p></dd>
</dl>

</dd>
</dl>

</dd>
<dt><span><samp>hevc_vaapi</samp></span></dt>
<dd><p><samp>profile</samp> and <samp>level</samp> set the values of
<em>general_profile_idc</em> and <em>general_level_idc</em> respectively.
</p>
<dl compact="compact">
<dt><span><samp>aud</samp></span></dt>
<dd><p>Include access unit delimiters in the stream (not included by default).
</p>
</dd>
<dt><span><samp>tier</samp></span></dt>
<dd><p>Set <em>general_tier_flag</em>.  This may affect the level chosen for the stream
if it is not explicitly specified.
</p>
</dd>
<dt><span><samp>sei</samp></span></dt>
<dd><p>Set SEI message types to include.
Some combination of the following values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>hdr</samp>&rsquo;</span></dt>
<dd><p>Include HDR metadata if the input frames have it
(<em>mastering_display_colour_volume</em> and <em>content_light_level</em>
messages).
</p></dd>
</dl>

</dd>
<dt><span><samp>tiles</samp></span></dt>
<dd><p>Set the number of tiles to encode the input video with, as columns x rows.
Larger numbers allow greater parallelism in both encoding and decoding, but
may decrease coding efficiency.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>mjpeg_vaapi</samp></span></dt>
<dd><p>Only baseline DCT encoding is supported.  The encoder always uses the standard
quantisation and huffman tables - <samp>global_quality</samp> scales the standard
quantisation table (range 1-100).
</p>
<p>For YUV, 4:2:0, 4:2:2 and 4:4:4 subsampling modes are supported.  RGB is also
supported, and will create an RGB JPEG.
</p>
<dl compact="compact">
<dt><span><samp>jfif</samp></span></dt>
<dd><p>Include JFIF header in each frame (not included by default).
</p></dd>
<dt><span><samp>huffman</samp></span></dt>
<dd><p>Include standard huffman tables (on by default).  Turning this off will save
a few hundred bytes in each output frame, but may lose compatibility with some
JPEG decoders which don&rsquo;t fully handle MJPEG.
</p></dd>
</dl>

</dd>
<dt><span><samp>mpeg2_vaapi</samp></span></dt>
<dd><p><samp>profile</samp> and <samp>level</samp> set the value of <em>profile_and_level_indication</em>.
</p>
</dd>
<dt><span><samp>vp8_vaapi</samp></span></dt>
<dd><p>B-frames are not supported.
</p>
<p><samp>global_quality</samp> sets the <em>q_idx</em> used for non-key frames (range 0-127).
</p>
<dl compact="compact">
<dt><span><samp>loop_filter_level</samp></span></dt>
<dt><span><samp>loop_filter_sharpness</samp></span></dt>
<dd><p>Manually set the loop filter parameters.
</p></dd>
</dl>

</dd>
<dt><span><samp>vp9_vaapi</samp></span></dt>
<dd><p><samp>global_quality</samp> sets the <em>q_idx</em> used for P-frames (range 0-255).
</p>
<dl compact="compact">
<dt><span><samp>loop_filter_level</samp></span></dt>
<dt><span><samp>loop_filter_sharpness</samp></span></dt>
<dd><p>Manually set the loop filter parameters.
</p></dd>
</dl>

<p>B-frames are supported, but the output stream is always in encode order rather than display
order.  If B-frames are enabled, it may be necessary to use the <samp>vp9_raw_reorder</samp>
bitstream filter to modify the output stream to display frames in the correct order.
</p>
<p>Only normal frames are produced - the <samp>vp9_superframe</samp> bitstream filter may be
required to produce a stream usable with all decoders.
</p>
</dd>
</dl>

<a name="vbn"></a>
<h3 class="section">9.26 vbn<span class="pull-right"><a class="anchor hidden-xs" href="#vbn" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vbn" aria-hidden="true">TOC</a></span></h3>

<p>Vizrt Binary Image encoder.
</p>
<p>This format is used by the broadcast vendor Vizrt for quick texture streaming.
Advanced features of the format such as LZW compression of texture data or
generation of mipmaps are not supported.
</p>
<a name="Options-40"></a>
<h4 class="subsection">9.26.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-40" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-40" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>format <var>string</var></samp></span></dt>
<dd><p>Sets the texture compression used by the VBN file. Can be <var>dxt1</var>,
<var>dxt5</var> or <var>raw</var>. Default is <var>dxt5</var>.
</p></dd>
</dl>

<a name="vc2"></a>
<h3 class="section">9.27 vc2<span class="pull-right"><a class="anchor hidden-xs" href="#vc2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vc2" aria-hidden="true">TOC</a></span></h3>

<p>SMPTE VC-2 (previously BBC Dirac Pro). This codec was primarily aimed at
professional broadcasting but since it supports yuv420, yuv422 and yuv444 at
8 (limited range or full range), 10 or 12 bits, this makes it suitable for
other tasks which require low overhead and low compression (like screen
recording).
</p>
<a name="Options-41"></a>
<h4 class="subsection">9.27.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-41" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-41" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>b</samp></span></dt>
<dd><p>Sets target video bitrate. Usually that&rsquo;s around 1:6 of the uncompressed
video bitrate (e.g. for 1920x1080 50fps yuv422p10 that&rsquo;s around 400Mbps). Higher
values (close to the uncompressed bitrate) turn on lossless compression mode.
</p>
</dd>
<dt><span><samp>field_order</samp></span></dt>
<dd><p>Enables field coding when set (e.g. to tt - top field first) for interlaced
inputs. Should increase compression with interlaced content as it splits the
fields and encodes each separately.
</p>
</dd>
<dt><span><samp>wavelet_depth</samp></span></dt>
<dd><p>Sets the total amount of wavelet transforms to apply, between 1 and 5 (default).
Lower values reduce compression and quality. Less capable decoders may not be
able to handle values of <samp>wavelet_depth</samp> over 3.
</p>
</dd>
<dt><span><samp>wavelet_type</samp></span></dt>
<dd><p>Sets the transform type. Currently only <var>5_3</var> (LeGall) and <var>9_7</var>
(Deslauriers-Dubuc)
are implemented, with 9_7 being the one with better compression and thus
is the default.
</p>
</dd>
<dt><span><samp>slice_width</samp></span></dt>
<dt><span><samp>slice_height</samp></span></dt>
<dd><p>Sets the slice size for each slice. Larger values result in better compression.
For compatibility with other more limited decoders use <samp>slice_width</samp> of
32 and <samp>slice_height</samp> of 8.
</p>
</dd>
<dt><span><samp>tolerance</samp></span></dt>
<dd><p>Sets the undershoot tolerance of the rate control system in percent. This is
to prevent an expensive search from being run.
</p>
</dd>
<dt><span><samp>qm</samp></span></dt>
<dd><p>Sets the quantization matrix preset to use by default or when <samp>wavelet_depth</samp>
is set to 5
</p><ul class="no-bullet">
<li>- <var>default</var>
Uses the default quantization matrix from the specifications, extended with
values for the fifth level. This provides a good balance between keeping detail
and omitting artifacts.

</li><li>- <var>flat</var>
Use a completely zeroed out quantization matrix. This increases PSNR but might
reduce perception. Use in bogus benchmarks.

</li><li>- <var>color</var>
Reduces detail but attempts to preserve color at extremely low bitrates.
</li></ul>

</dd>
</dl>


<a name="Subtitles-Encoders"></a>
<h2 class="chapter">10 Subtitles Encoders<span class="pull-right"><a class="anchor hidden-xs" href="#Subtitles-Encoders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Subtitles-Encoders" aria-hidden="true">TOC</a></span></h2>

<a name="dvdsub-1"></a>
<h3 class="section">10.1 dvdsub<span class="pull-right"><a class="anchor hidden-xs" href="#dvdsub-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dvdsub-1" aria-hidden="true">TOC</a></span></h3>

<p>This codec encodes the bitmap subtitle format that is used in DVDs.
Typically they are stored in VOBSUB file pairs (*.idx + *.sub),
and they can also be used in Matroska files.
</p>
<a name="Options-42"></a>
<h4 class="subsection">10.1.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-42" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-42" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>palette</samp></span></dt>
<dd><p>Specify the global palette used by the bitmaps.
</p>
<p>The format for this option is a string containing 16 24-bits hexadecimal
numbers (without 0x prefix) separated by commas, for example <code>0d00ee,
ee450d, 101010, eaeaea, 0ce60b, ec14ed, ebff0b, 0d617a, 7b7b7b, d1d1d1,
7b2a0e, 0d950c, 0f007b, cf0dec, cfa80c, 7c127b</code>.
</p>
</dd>
<dt><span><samp>even_rows_fix</samp></span></dt>
<dd><p>When set to 1, enable a work-around that makes the number of pixel rows
even in all subtitles.  This fixes a problem with some players that
cut off the bottom row if the number is odd.  The work-around just adds
a fully transparent row if needed.  The overhead is low, typically
one byte per subtitle on average.
</p>
<p>By default, this work-around is disabled.
</p></dd>
</dl>


<a name="See-Also"></a>
<h2 class="chapter">11 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>,
<a href="libavcodec.html">libavcodec</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">12 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a href="https://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
