﻿/********************************************************
 * 创建者：ljf
 * 创建日期：2020-07-13
 * 描述：全局消息
 *
********************************************************/

#ifndef GLOBALSIGNAL_H
#define GLOBALSIGNAL_H

#include <QObject>
#include "./singleton.h"

class GlobalSignal : public QObject, public Singleton<GlobalSignal>
{
    Q_OBJECT
public:

signals:
    /*************************************************
     * Function:    exitApp
     * Description:	全局信号，应用程序退出时由ui发出，各模块按需连接到此信号以做清理工作
     * Input:       无
     * Output:      无
     * Return:      无
     * Others:      无
    *************************************************/
    void exitApp();

    /*************************************************
     * Function:    ukey拔出信号
     * Description:	全局信号，Ukey被拔出时发送该信号
     * Input:       Ukey被拔出后检查到的次数
     * Output:      无
     * Return:      无
     * Others:      无
    *************************************************/
    void ukeyPullOut(int);

	/*************************************************
    * Function:    rebuildSystray
    * Description: 全局信号，explorer.exe崩溃后发出此信号
	* Input:       无
	* Output:      无
	* Return:      无
	* Others:      无
	*************************************************/
    void rebuildSystray();

    void ClearState();
    /*************************************************
    * Function:    changeState
    * Description: 改变主窗口层叠窗口的显示
    * Input:       nMainState=主状态，nSubState = 子状态
    * Output:      无
    * Return:      无
    * Others:      无
    *************************************************/
    void changeState(int nMainState,int nSubState=0);
    /*************************************************
    * Function:    changeMainBK
    * Description: 改变主窗口层叠窗口的显示
    * Input:       nWidth = 宽度，nHeight = 高度
    * Output:      无
    * Return:      无
    * Others:      无
    *************************************************/
    void changeMainBK(int nWidth,int nHeight);
    void aboutApp();
    void closeLoadWidget();
    void updateProcessInfo(int nScan, int nLine, int nLoop, int nTotal);
protected:
    friend class Singleton<GlobalSignal>;
    explicit GlobalSignal(QObject *parent = 0) : QObject(parent) {}
};

#endif // GLOBALSIGNAL_H
