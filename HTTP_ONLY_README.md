# HTTP检测模式修改说明

## 修改概述
根据用户需求，已将摄像头扫描器修改为仅支持HTTP检测模式，移除了RTSP检测功能。同时简化了FFmpeg相关的代码结构。

## 主要修改内容

### 1. 扫描逻辑修改 (camerascanner.cpp)
- 移除了RTSP连接检测逻辑
- 只保留HTTP图像检测功能
- 简化了检测结果判断逻辑

### 2. 配置管理修改 (configmanager.cpp/h)
- 移除了RTSP超时设置 (`m_rtspTimeout`)
- 移除了HTTP检测开关设置 (`m_useHttpDetection`)
- 只保留HTTP超时设置 (`m_httpTimeout`)
- 移除了相关的getter/setter方法

### 3. 配置界面修改 (configdialog.cpp/ui)
- 移除了RTSP超时设置控件
- 移除了HTTP检测开关复选框
- 只保留HTTP超时设置控件

### 4. 配置文件修改 (config.ini)
- 移除了 `RtspTimeout` 和 `UseHttpDetection` 配置项
- 只保留 `HttpTimeout` 配置项

### 5. 线程类修改 (mythread.h/cpp)
- 移除了 `testRtspConnection` 方法的声明和实现
- 保留了 `testHttpImage` 和 `buildHttpImageUrl` 方法
- **移除了 `USE_FFMPEG` 条件编译宏**，FFmpeg相关代码现在始终编译

### 6. 项目配置修改 (NetVideoClient.pro)
- 移除了 `CONFIG += use_ffmpeg` 配置
- 移除了 `USE_FFMPEG` 宏定义
- FFmpeg库现在始终链接到项目中

### 7. 主程序修改 (main.cpp)
- 移除了 `USE_FFMPEG` 条件编译
- FFmpeg DLL检查现在始终执行

## 功能说明
修改后的应用程序将：
1. 只通过HTTP方式检测摄像头
2. 尝试访问 `http://[IP]/image.jpg` 来验证摄像头是否存在
3. 支持用户名/密码认证
4. 使用可配置的HTTP超时时间（默认2000ms）
5. **保留FFmpeg功能用于录像等高级功能**

## 代码结构简化
- **移除了条件编译复杂性**：不再需要 `#ifdef USE_FFMPEG` 条件编译
- **简化了代码维护**：所有FFmpeg相关代码现在都是可见的
- **保持了功能完整性**：录像等功能仍然可用

## 注意事项
- RTSP相关的代码仍然保留在MyThread类中，以便在其他功能中使用
- 扫描器现在只依赖HTTP检测，扫描速度可能会有所变化
- 建议根据网络环境调整HTTP超时时间以获得最佳检测效果
- **FFmpeg功能仍然完整保留**，用于录像等高级功能 