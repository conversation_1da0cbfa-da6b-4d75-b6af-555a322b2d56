﻿/********************************************************
 * 创建者：ljf
 * 描述：单例模板类
 *
********************************************************/

#ifndef SINGLETON_H
#define SINGLETON_H

#include <QMutex>
#include <QMutexLocker>
#include <QScopedPointer>

template<typename T>
class Singleton
{
public:
	virtual ~Singleton() {
		
	}

    static T& getInstance(){

		if (!instance)
		{
			static QMutex mutex; //实例互斥锁

			QMutexLocker locker(&mutex); //加互斥锁

			if (!instance) { //线程不安全，这里需要同步
				instance = new T;
			}
		}
        
        return *instance;
    }
    static void destroyInstance()
	{
		if (instance) {
			delete instance;
            instance = 0;
		}
	}
protected:
    Singleton(){}
    Singleton(const Singleton<T>&);
    Singleton<T>& operator=(const Singleton<T>);
private:

    static T* instance;
};

template<typename T>
T* Singleton<T>::instance;


#endif // SINGLETON_H
