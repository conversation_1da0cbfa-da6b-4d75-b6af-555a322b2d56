#include "configmanager.h"
#include <QCoreApplication>
#include <QDebug>
#include <QFile>
#include <QDir>
#include <QSqlQuery>
#include <QSqlError>
#include <QVariant>
#include <QTextCodec>

// 临时禁用日志宏，直接使用qDebug
#define CONFIG_LOG(msg) qDebug() << "ConfigManager: " << msg

ConfigManager::ConfigManager(QObject *parent) : QObject(parent), m_firstScan(true)
{
    // 创建配置文件对象，使用INI格式
    QString configPath = QCoreApplication::applicationDirPath() + "/config.ini";

    // 检查配置文件是否存在，如果不存在则创建一个默认配置
    QFile configFile(configPath);
    bool needDefaultConfig = !configFile.exists();

    // 创建配置对象，指定使用UTF-8编码
    m_settings = new QSettings(configPath, QSettings::IniFormat);
    m_settings->setIniCodec(QTextCodec::codecForName("UTF-8"));

    // 如果需要创建默认配置，设置默认值并保存
    if (needDefaultConfig)
    {
        CONFIG_LOG("create default config file:" << configPath);

        // 设置默认值
        m_ipPrefix = "192.168.0";
        m_startIp = 170;
        m_endIp = 180;
        m_username = DEFAULT_USERNAME;
        m_password = DEFAULT_PASSWORD;
        m_streamPath = DEFAULT_STREAM_PATH;
        m_httpTimeout = 2000;
        m_maxRecordingDuration = 60; // 默认最大录制时长为60秒（1分钟）
        m_autoDisplay = false;
        m_firstScan = true;
        m_appName = "高清针孔摄像头窃视演示";
        m_appVersion = "1.0.0";
        m_logLevel = 3;           // 默认日志级别为error
        m_defaultDisplayMode = 4; // 默认分屏数为4

        // 保存默认配置
        saveSettings();
    }
    else
    {
        // 加载现有设置
        loadSettings();
    }

    // 初始化数据库
    initDatabase();

    CONFIG_LOG("config loaded, file path:" << configPath);
}

ConfigManager::~ConfigManager()
{
    // 保存设置
    saveSettings();

    // 关闭数据库连接
    if (m_database.isOpen())
    {
        m_database.close();
    }

    // 释放资源
    delete m_settings;
}

ConfigManager &ConfigManager::instance()
{
    static ConfigManager instance;
    return instance;
}

void ConfigManager::loadSettings()
{
    // 扫描设置
    m_ipPrefix = m_settings->value("Scan/IpPrefix", "192.168.0").toString();
    m_startIp = m_settings->value("Scan/StartIp", 1).toInt();
    m_endIp = m_settings->value("Scan/EndIp", 254).toInt();

    // 认证设置 - 使用宏定义的默认值
    m_username = DEFAULT_USERNAME;
    m_password = DEFAULT_PASSWORD;
    m_streamPath = DEFAULT_STREAM_PATH;

    // 超时设置
    m_httpTimeout = m_settings->value("Timeout/HttpTimeout", 2000).toInt();

    // 录制设置
    m_maxRecordingDuration = m_settings->value("Recording/MaxDuration", 60).toInt();

    // 自动显示设置
    m_autoDisplay = m_settings->value("Display/AutoDisplay", false).toBool();

    // 默认分屏设置
    m_defaultDisplayMode = m_settings->value("Display/DefaultMode", 4).toInt();

    // 应用程序信息
    m_appName = m_settings->value("Application/AppName", "针孔摄像头演示系统").toString();
    m_appVersion = m_settings->value("Application/AppVersion", "1.0.0").toString();

    // 日志设置
    m_logLevel = m_settings->value("Log/LogLevel", 0).toInt();

    // 扫描状态
    m_firstScan = m_settings->value("Database/FirstScan", true).toBool();
}

void ConfigManager::saveSettings()
{
    // 扫描设置
    m_settings->setValue("Scan/IpPrefix", m_ipPrefix);
    m_settings->setValue("Scan/StartIp", m_startIp);
    m_settings->setValue("Scan/EndIp", m_endIp);

    // 认证设置 - 不再保存到配置文件中
    // 移除这些行
    // m_settings->setValue("Auth/Username", m_username);
    // m_settings->setValue("Auth/Password", m_password);
    // m_settings->setValue("Auth/StreamPath", m_streamPath);

    // 超时设置
    m_settings->setValue("Timeout/HttpTimeout", m_httpTimeout);

    // 录制设置
    m_settings->setValue("Recording/MaxDuration", m_maxRecordingDuration);

    // 自动显示设置
    m_settings->setValue("Display/AutoDisplay", m_autoDisplay);

    // 默认分屏设置
    m_settings->setValue("Display/DefaultMode", m_defaultDisplayMode);

    // 应用程序信息
    m_settings->setValue("Application/AppName", m_appName);
    m_settings->setValue("Application/AppVersion", m_appVersion);

    // 日志设置
    m_settings->setValue("Log/LogLevel", m_logLevel);

    // 扫描状态
    m_settings->setValue("Database/FirstScan", m_firstScan);

    // 确保写入到文件
    m_settings->sync();

    CONFIG_LOG("config saved");
}

// 扫描设置
QString ConfigManager::getIpPrefix() const
{
    return m_ipPrefix;
}

void ConfigManager::setIpPrefix(const QString &prefix)
{
    m_ipPrefix = prefix;
}

int ConfigManager::getStartIp() const
{
    return m_startIp;
}

void ConfigManager::setStartIp(int ip)
{
    m_startIp = ip;
}

int ConfigManager::getEndIp() const
{
    return m_endIp;
}

void ConfigManager::setEndIp(int ip)
{
    m_endIp = ip;
}

// 认证设置
QString ConfigManager::getUsername() const
{
    return m_username;
}

void ConfigManager::setUsername(const QString &username)
{
    m_username = username;
}

QString ConfigManager::getPassword() const
{
    return m_password;
}

void ConfigManager::setPassword(const QString &password)
{
    m_password = password;
}

QString ConfigManager::getStreamPath() const
{
    return m_streamPath;
}

void ConfigManager::setStreamPath(const QString &path)
{
    m_streamPath = path;
}

int ConfigManager::getHttpTimeout() const
{
    return m_httpTimeout;
}

void ConfigManager::setHttpTimeout(int timeout)
{
    m_httpTimeout = timeout;
}

// 自动显示设置
bool ConfigManager::getAutoDisplay() const
{
    return m_autoDisplay;
}

void ConfigManager::setAutoDisplay(bool autoDisplay)
{
    m_autoDisplay = autoDisplay;
}

// 默认分屏设置
int ConfigManager::getDefaultDisplayMode() const
{
    return m_defaultDisplayMode;
}

void ConfigManager::setDefaultDisplayMode(int mode)
{
    m_defaultDisplayMode = mode;
}

// 应用程序信息
QString ConfigManager::getAppName() const
{
    return m_appName;
}

void ConfigManager::setAppName(const QString &appName)
{
    m_appName = appName;
}

QString ConfigManager::getAppVersion() const
{
    return m_appVersion;
}

void ConfigManager::setAppVersion(const QString &appVersion)
{
    m_appVersion = appVersion;
}

// 录制设置
int ConfigManager::getMaxRecordingDuration() const
{
    return m_maxRecordingDuration;
}

void ConfigManager::setMaxRecordingDuration(int seconds)
{
    m_maxRecordingDuration = seconds;
}

// 数据库相关方法
bool ConfigManager::isFirstScan() const
{
    return m_firstScan;
}

void ConfigManager::setFirstScanCompleted()
{
    m_firstScan = false;
    saveSettings();
}

bool ConfigManager::initDatabase()
{
    // 设置数据库文件路径
    QString dbPath = QCoreApplication::applicationDirPath() + "/cameras.db";

    // 检查SQLite驱动是否可用
    if (!QSqlDatabase::isDriverAvailable("QSQLITE"))
    {
        CONFIG_LOG("SQLite driver not available");
        return false;
    }

    // 创建数据库连接
    m_database = QSqlDatabase::addDatabase("QSQLITE");
    m_database.setDatabaseName(dbPath);

    // 打开数据库
    if (!m_database.open())
    {
        CONFIG_LOG("failed to open database:" << m_database.lastError().text());
        return false;
    }

    // 创建摄像头表
    QSqlQuery query;
    if (!query.exec("CREATE TABLE IF NOT EXISTS cameras ("
                    "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                    "name TEXT, "
                    "rtsp_url TEXT UNIQUE, "
                    "ip_address TEXT, "
                    "is_valid INTEGER, "
                    "has_http_image INTEGER, "
                    "is_checked INTEGER DEFAULT 0)"))
    {
        CONFIG_LOG("create table failed:" << query.lastError().text());
        return false;
    }

    // 检查是否需要添加is_checked字段（兼容旧版本数据库）
    QSqlQuery checkColumnQuery("PRAGMA table_info(cameras)");
    bool hasCheckedColumn = false;
    while (checkColumnQuery.next())
    {
        if (checkColumnQuery.value(1).toString() == "is_checked")
        {
            hasCheckedColumn = true;
            break;
        }
    }

    // 如果没有is_checked字段，添加它
    if (!hasCheckedColumn)
    {
        QSqlQuery alterQuery;
        if (!alterQuery.exec("ALTER TABLE cameras ADD COLUMN is_checked INTEGER DEFAULT 0"))
        {
            CONFIG_LOG("alter table failed:" << alterQuery.lastError().text());
            // 不返回false，允许程序继续运行
        }
        else
        {
            CONFIG_LOG("added is_checked column to cameras table");
        }
    }

    CONFIG_LOG("database initialized successfully:" << dbPath);
    return true;
}

bool ConfigManager::saveCameraToDatabase(const CameraInfo &camera)
{
    if (!m_database.isOpen())
    {
        if (!initDatabase())
        {
            return false;
        }
    }

    // 先检查是否已存在相同IP的摄像头，以保留其名称和勾选状态
    QSqlQuery checkQuery;
    checkQuery.prepare("SELECT name, is_checked FROM cameras WHERE ip_address = :ip_address");
    checkQuery.bindValue(":ip_address", camera.ipAddress);

    QString existingName;
    bool isChecked = camera.isChecked; // 始终使用传入的勾选状态
    if (checkQuery.exec() && checkQuery.next())
    {
        existingName = checkQuery.value(0).toString();
        // 移除对勾选状态的特殊处理，始终使用传入的值
    }

    QSqlQuery query;
    query.prepare("INSERT OR REPLACE INTO cameras (name, rtsp_url, ip_address, is_valid, has_http_image, is_checked) "
                  "VALUES (:name, :rtsp_url, :ip_address, :is_valid, :has_http_image, :is_checked)");

    // 使用摄像头的name属性，如果为空则生成默认名称
    QString name = camera.name;

    // 如果传入的name为空，但数据库中已有名称，则使用数据库中的名称
    if (name.isEmpty() && !existingName.isEmpty())
    {
        name = existingName;
        CONFIG_LOG("use exist name: IP=" << camera.ipAddress << ", name=" << name);
    }
    else if (name.isEmpty())
    {
        // 获取当前摄像头数量，用于生成序号
        QSqlQuery countQuery("SELECT COUNT(*) FROM cameras");
        int cameraCount = 0;
        if (countQuery.next())
        {
            cameraCount = countQuery.value(0).toInt() + 1;
        }
        else
        {
            cameraCount = 1;
        }
        name = QString("未命名摄像头%1").arg(cameraCount);
    }

    query.bindValue(":name", name);
    query.bindValue(":rtsp_url", camera.rtspUrl);
    query.bindValue(":ip_address", camera.ipAddress);
    query.bindValue(":is_valid", camera.isValid ? 1 : 0);
    query.bindValue(":has_http_image", camera.hasHttpImage ? 1 : 0);
    query.bindValue(":is_checked", isChecked ? 1 : 0); // 使用传入的勾选状态

    if (!query.exec())
    {
        CONFIG_LOG("save camera info failed:" << query.lastError().text());
        return false;
    }

    return true;
}

QList<CameraInfo> ConfigManager::loadCamerasFromDatabase()
{
    QList<CameraInfo> cameras;

    if (!m_database.isOpen())
    {
        if (!initDatabase())
        {
            return cameras;
        }
    }

    QSqlQuery query("SELECT name, rtsp_url, ip_address, is_valid, has_http_image, is_checked FROM cameras");

    while (query.next())
    {
        QString name = query.value(0).toString();
        QString rtspUrl = query.value(1).toString();
        QString ipAddress = query.value(2).toString();
        bool isValid = query.value(3).toBool();
        bool hasHttpImage = query.value(4).toBool();
        bool isChecked = query.value(5).toBool();

        CameraInfo camera(rtspUrl, ipAddress, isValid, hasHttpImage);
        camera.name = name;
        camera.isChecked = isChecked;
        cameras.append(camera);
    }

    return cameras;
}

bool ConfigManager::updateCameraName(const QString &rtspUrl, const QString &newName)
{
    if (!m_database.isOpen())
    {
        if (!initDatabase())
        {
            return false;
        }
    }

    QSqlQuery query;
    query.prepare("UPDATE cameras SET name = :name WHERE rtsp_url = :rtsp_url");
    query.bindValue(":name", newName);
    query.bindValue(":rtsp_url", rtspUrl);

    if (!query.exec())
    {
        CONFIG_LOG("update camera name failed:" << query.lastError().text());
        return false;
    }

    return query.numRowsAffected() > 0;
}

CameraInfo ConfigManager::findCameraByIp(const QString &ipAddress)
{
    CameraInfo camera;

    if (!m_database.isOpen())
    {
        if (!initDatabase())
        {
            return camera;
        }
    }

    QSqlQuery query;
    query.prepare("SELECT name, rtsp_url, ip_address, is_valid, has_http_image FROM cameras WHERE ip_address = :ip_address");
    query.bindValue(":ip_address", ipAddress);

    if (query.exec() && query.next())
    {
        QString name = query.value(0).toString();
        QString rtspUrl = query.value(1).toString();
        QString ip = query.value(2).toString();
        bool isValid = query.value(3).toBool();
        bool hasHttpImage = query.value(4).toBool();

        camera = CameraInfo(rtspUrl, ip, isValid, hasHttpImage);
        camera.name = name;

        CONFIG_LOG("found exist camera: IP=" << ipAddress << ", name=" << name);
    }

    return camera;
}

bool ConfigManager::deleteCameraByIp(const QString &ipAddress)
{
    if (!m_database.isOpen())
    {
        if (!initDatabase())
        {
            return false;
        }
    }

    QSqlQuery query;
    query.prepare("DELETE FROM cameras WHERE ip_address = :ip_address");
    query.bindValue(":ip_address", ipAddress);

    if (!query.exec())
    {
        CONFIG_LOG("delete camera failed:" << query.lastError().text());
        return false;
    }

    CONFIG_LOG("delete camera success: IP=" << ipAddress);
    return query.numRowsAffected() > 0;
}

bool ConfigManager::deleteInactiveCameras(const QList<QString> &activeIpAddresses)
{
    if (!m_database.isOpen() && !m_database.open())
    {
        CONFIG_LOG("delete inactive cameras error: database not open");
        return false;
    }

    QSqlQuery query(m_database);
    query.prepare("DELETE FROM cameras WHERE ip_address NOT IN ('" + activeIpAddresses.join("','") + "')");

    if (!query.exec())
    {
        CONFIG_LOG("delete inactive cameras error:" << query.lastError().text());
        return false;
    }

    return true;
}

bool ConfigManager::deleteInactiveCameras(const QList<QString> &activeIpAddresses, const QList<QString> &scanRangeIps)
{
    if (!m_database.isOpen() && !m_database.open())
    {
        CONFIG_LOG("delete inactive cameras in scan range error: database not open");
        return false;
    }

    // 先构建扫描范围内的IP地址字符串列表，用于SQL语句
    if (scanRangeIps.isEmpty())
    {
        CONFIG_LOG("scan range ips is empty, nothing to delete");
        return true;
    }

    QString scanRangeIpsStr = "'" + scanRangeIps.join("','") + "'";

    // 构建活跃IP地址字符串列表
    QString activeIpsStr = activeIpAddresses.isEmpty() ? "''" : // 如果没有活跃IP，使用一个不存在的值确保条件成立
                               "'" + activeIpAddresses.join("','") + "'";

    // 删除在扫描范围内但不在活跃列表中的摄像头
    QSqlQuery query(m_database);
    QString queryStr = QString("DELETE FROM cameras WHERE ip_address IN (%1) AND ip_address NOT IN (%2)")
                           .arg(scanRangeIpsStr)
                           .arg(activeIpsStr);

    query.prepare(queryStr);

    if (!query.exec())
    {
        CONFIG_LOG("delete inactive cameras in scan range error:" << query.lastError().text());
        return false;
    }

    CONFIG_LOG("deleted cameras in scan range:" << query.numRowsAffected());
    return true;
}

QSettings *ConfigManager::getSettings() const
{
    return m_settings;
}

int ConfigManager::getLogLevel() const
{
    return m_logLevel;
}

void ConfigManager::setLogLevel(int level)
{
    m_logLevel = level;
    m_settings->setValue("Log/LogLevel", level);
    m_settings->sync();
    CONFIG_LOG("日志级别已设置为: " << level);
}

QString ConfigManager::getLogFileName() const
{
    return m_settings->value("Log/LogFileName", "log_%date%.txt").toString();
}

void ConfigManager::setLogFileName(const QString &fileName)
{
    m_settings->setValue("Log/LogFileName", fileName);
}