/****************************************************************************
** Meta object code from reading C++ file 'camerascanwidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../camerascanwidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'camerascanwidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CameraScanWidget_t {
    QByteArrayData data[23];
    char stringdata0[285];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CameraScanWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CameraScanWidget_t qt_meta_stringdata_CameraScanWidget = {
    {
QT_MOC_LITERAL(0, 0, 16), // "CameraScanWidget"
QT_MOC_LITERAL(1, 17, 14), // "cameraSelected"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 7), // "rtspUrl"
QT_MOC_LITERAL(4, 41, 16), // "cameraDeselected"
QT_MOC_LITERAL(5, 58, 17), // "cameraNameUpdated"
QT_MOC_LITERAL(6, 76, 7), // "newName"
QT_MOC_LITERAL(7, 84, 14), // "onScanProgress"
QT_MOC_LITERAL(8, 99, 7), // "current"
QT_MOC_LITERAL(9, 107, 5), // "total"
QT_MOC_LITERAL(10, 113, 13), // "onCameraFound"
QT_MOC_LITERAL(11, 127, 10), // "CameraInfo"
QT_MOC_LITERAL(12, 138, 6), // "camera"
QT_MOC_LITERAL(13, 145, 15), // "onScanCompleted"
QT_MOC_LITERAL(14, 161, 28), // "onCameraListSelectionChanged"
QT_MOC_LITERAL(15, 190, 15), // "showContextMenu"
QT_MOC_LITERAL(16, 206, 3), // "pos"
QT_MOC_LITERAL(17, 210, 6), // "rescan"
QT_MOC_LITERAL(18, 217, 24), // "onOperationButtonClicked"
QT_MOC_LITERAL(19, 242, 5), // "state"
QT_MOC_LITERAL(20, 248, 13), // "onItemChanged"
QT_MOC_LITERAL(21, 262, 17), // "QTableWidgetItem*"
QT_MOC_LITERAL(22, 280, 4) // "item"

    },
    "CameraScanWidget\0cameraSelected\0\0"
    "rtspUrl\0cameraDeselected\0cameraNameUpdated\0"
    "newName\0onScanProgress\0current\0total\0"
    "onCameraFound\0CameraInfo\0camera\0"
    "onScanCompleted\0onCameraListSelectionChanged\0"
    "showContextMenu\0pos\0rescan\0"
    "onOperationButtonClicked\0state\0"
    "onItemChanged\0QTableWidgetItem*\0item"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CameraScanWidget[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   69,    2, 0x06 /* Public */,
       4,    1,   72,    2, 0x06 /* Public */,
       5,    2,   75,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    2,   80,    2, 0x08 /* Private */,
      10,    1,   85,    2, 0x08 /* Private */,
      13,    0,   88,    2, 0x08 /* Private */,
      14,    0,   89,    2, 0x08 /* Private */,
      15,    1,   90,    2, 0x08 /* Private */,
      17,    0,   93,    2, 0x08 /* Private */,
      18,    1,   94,    2, 0x08 /* Private */,
      20,    1,   97,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    6,

 // slots: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    8,    9,
    QMetaType::Void, 0x80000000 | 11,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   16,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   19,
    QMetaType::Void, 0x80000000 | 21,   22,

       0        // eod
};

void CameraScanWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        CameraScanWidget *_t = static_cast<CameraScanWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->cameraSelected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->cameraDeselected((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->cameraNameUpdated((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->onScanProgress((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->onCameraFound((*reinterpret_cast< const CameraInfo(*)>(_a[1]))); break;
        case 5: _t->onScanCompleted(); break;
        case 6: _t->onCameraListSelectionChanged(); break;
        case 7: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 8: _t->rescan(); break;
        case 9: _t->onOperationButtonClicked((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 10: _t->onItemChanged((*reinterpret_cast< QTableWidgetItem*(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (CameraScanWidget::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanWidget::cameraSelected)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (CameraScanWidget::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanWidget::cameraDeselected)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (CameraScanWidget::*_t)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanWidget::cameraNameUpdated)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject CameraScanWidget::staticMetaObject = {
    { &QWidget::staticMetaObject, qt_meta_stringdata_CameraScanWidget.data,
      qt_meta_data_CameraScanWidget,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *CameraScanWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CameraScanWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CameraScanWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int CameraScanWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}

// SIGNAL 0
void CameraScanWidget::cameraSelected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CameraScanWidget::cameraDeselected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CameraScanWidget::cameraNameUpdated(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
