﻿#include "mythread.h"
#include <QByteArray>
#include <QImage>
#include <QNetworkRequest>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QEventLoop>
#include <QTimer>
#include <QUrl>
#include <QDebug>
#include <QRegExp>
#include <QProcess>
#include <QTemporaryFile>
#include <QDir>
#include <QCoreApplication>
#include <QTime>
#include <QElapsedTimer>
#include <QMutexLocker>
#include "logmanager.h"
// FFmpeg头文件
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/opt.h>

// 定义LOG_DEBUG_ONCE宏，只打印一次日志
#define LOG_DEBUG_ONCE(msg)         \
	do                              \
	{                               \
		static bool logged = false; \
		if (!logged)                \
		{                           \
			LOG_DEBUG(msg);         \
			logged = true;          \
		}                           \
	} while (0)

// RTSP 构造函数 - 唯一的构造函数
MyThread::MyThread(QString rtspUrl, QObject *parent) : QThread(parent),
													   rtspUrl(rtspUrl),
													   formatContext(nullptr),
													   codecContext(nullptr),
													   audioCodecContext(nullptr),
													   frame(nullptr),
													   audioFrame(nullptr),
													   packet(nullptr),
													   swsContext(nullptr),
													   videoStreamIndex(-1),
													   audioStreamIndex(-1),
													   isRunning(false),
													   isMuted(true),
													   m_threadStopped(false),
													   recordProcess(nullptr),
													   audioOutput(nullptr),
													   audioDevice(nullptr)
{
	// 构造函数体为空，所有初始化都在初始化列表中完成
}

MyThread::~MyThread()
{
	// 停止所有活动
	isRunning = false;
	m_threadStopped = false;

	// 尝试正常停止线程
	if (QThread::isRunning())
	{
		// 给线程100毫秒的时间正常结束
		if (!wait(100))
		{
			// 如果100毫秒后线程还没结束，强制终止
			LOG_WARNING("Force terminating thread in destructor");
			terminate();
			wait(500); // 等待线程真正结束
		}
	}

	// 清理资源
	mutex.lock();
	cleanupFFmpeg();
	mutex.unlock();

	LOG_DEBUG("MyThread destructor completed");
}

// 新增: 安全停止线程方法
bool MyThread::safeStop(int timeoutMs)
{
	if (!isRunning)
	{
		return true; // 线程已经停止
	}

	// 设置停止标志
	isRunning = false;
	m_threadStopped = false;

	// 等待线程完成
	if (QThread::isRunning())
	{
		// 等待指定时间
		return wait(timeoutMs);
	}

	return true;
}

void MyThread::setRunning(bool running)
{
	if (!running && isRunning)
	{
		// 如果要停止线程，使用安全停止方法
		safeStop();
	}
	else
	{
		isRunning = running;
	}
}

QImage MyThread::getLastFrame() const
{
	QMutexLocker locker(&mutex);
	return lastFrame;
}

void MyThread::setMuted(bool muted)
{
	isMuted = muted;

	// 控制音频输出音量
	if (audioOutput)
	{
		audioOutput->setVolume(muted ? 0.0 : 1.0);
		LOG_DEBUG("音频" << (muted ? "静音" : "取消静音"));
	}
}

void MyThread::run()
{
	LOG_DEBUG("MyThread start running in thread:" << QThread::currentThread());

	// 在线程开始时设置运行标志
	isRunning = true;
	m_threadStopped = false;

	// 使用FFmpeg处理RTSP流
	handleRtspWithFFmpeg();

	// 线程即将结束，标记线程已停止
	m_threadStopped = true;

	LOG_DEBUG("MyThread end running in thread:" << QThread::currentThread());
}

// 从IP构建HTTP图像URL
QString MyThread::buildHttpImageUrl(const QString &ip, const QString &username, const QString &password)
{
	// 使用QUrl类来构建URL，以确保正确处理特殊字符
	QUrl url;
	url.setScheme("http");
	url.setHost(ip);
	url.setPath("/image.jpg");

	// 添加认证信息（如果有）
	if (!username.isEmpty() && !password.isEmpty())
	{
		url.setUserName(username);
		url.setPassword(password);
	}

	// 返回URL字符串
	return url.toString();
}

// 测试HTTP图像是否有效
bool MyThread::testHttpImage(const QString &ip, const QString &username, const QString &password, int timeoutMs)
{
	// LOG_DEBUG("111111111111111Testing HTTP image: " << ip << " username: " << username << " password: " << password);
	//  构建HTTP图像URL
	QString url = buildHttpImageUrl(ip, username, password);

	LOG_DEBUG("Testing HTTP image: " << url);

	// 使用严格超时控制
	QElapsedTimer timeoutTimer;
	timeoutTimer.start();

	// 检查URL是否有效
	QUrl qurl(url);
	if (!qurl.isValid())
	{
		LOG_DEBUG("HTTP image request error: Invalid URL format");
		return false;
	}

	// 创建网络请求管理器
	QScopedPointer<QNetworkAccessManager> manager(new QNetworkAccessManager());
	QNetworkRequest request;
	request.setUrl(qurl);

	// 添加基本认证头（另一种认证方式）
	if (!username.isEmpty() && !password.isEmpty())
	{
		// 直接使用原始用户名和密码，不进行URL编码
		QString credentials = username + ":" + password;
		QByteArray auth = "Basic " + credentials.toUtf8().toBase64();
		request.setRawHeader("Authorization", auth);
		LOG_DEBUG("Using HTTP basic auth with credentials: " << username << ":******");
	}

	// 设置较短的超时
	timeoutMs = qMin(timeoutMs, 2000); // 最多2秒

	// 发送请求并等待响应
	QEventLoop loop;
	QTimer timer;
	timer.setSingleShot(true);

	bool requestError = false;
	QString errorMessage;
	QByteArray imageData;

	// 连接定时器超时信号
	QObject::connect(&timer, &QTimer::timeout, [&]()
					 {
		LOG_DEBUG("HTTP image request timeout");
		requestError = true;
		errorMessage = "Request timeout";
		loop.quit(); });

	// 发送请求
	QNetworkReply *reply = manager->get(request);

	// 连接请求完成信号
	QObject::connect(reply, &QNetworkReply::finished, [&]()
					 {
		if (reply->error() == QNetworkReply::NoError) {
			// 获取图像数据
			imageData = reply->readAll();
			
			// 检查是否是有效的图像
			QImage image = QImage::fromData(imageData);
			if (image.isNull()) {
				requestError = true;
				errorMessage = "Invalid image data";
			}
		} else {
			requestError = true;
			errorMessage = reply->errorString();
		}
		
		loop.quit(); });

	// 启动超时计时器
	timer.start(timeoutMs);

	// 等待请求完成或超时
	loop.exec();

	// 释放资源
	reply->deleteLater();

	// 断开连接
	QObject::disconnect(reply, nullptr, nullptr, nullptr);

	// 检查是否成功获取图像
	bool success = !requestError && !imageData.isEmpty();

	if (requestError)
	{
		LOG_DEBUG("HTTP image request error: " << errorMessage);
	}
	else if (imageData.isEmpty())
	{
		LOG_DEBUG("HTTP image data is empty");
		success = false;
	}

	LOG_DEBUG("HTTP image test result: " << (success ? "Success" : "Failed") << " Time: " << timeoutTimer.elapsed() << "ms");

	return success;
}

// FFmpeg初始化方法
bool MyThread::initFFmpeg()
{
	LOG_DEBUG("初始化FFmpeg，RTSP URL: " << rtspUrl);

	try
	{
		// 初始化FFmpeg网络功能
		avformat_network_init();

		// 分配格式上下文
		formatContext = avformat_alloc_context();
		if (!formatContext)
		{
			LOG_ERROR("无法分配FFmpeg格式上下文");
			avformat_network_deinit();
			return false;
		}

		// 设置选项，优化实时性
		AVDictionary *options = nullptr;
		av_dict_set(&options, "rtsp_transport", "tcp", 0); // 使用TCP传输RTSP
		// av_dict_set(&options, "rtsp_transport", "udp", 0);	  // 使用UDP传输RTSP
		av_dict_set(&options, "stimeout", "5000000", 0); // 5秒超时，单位是微秒

		// 稳定性优先的设置，减少花屏
		av_dict_set(&options, "buffer_size", "262144", 0);		// 增加缓冲区到256KB，提高稳定性
		av_dict_set(&options, "max_delay", "500000", 0);		// 增加最大延迟到0.5秒，减少丢包
		av_dict_set(&options, "rtbufsize", "262144", 0);		// 实时缓冲区大小256KB
		av_dict_set(&options, "fflags", "nobuffer", 0);			// 禁用额外缓冲
		av_dict_set(&options, "flags", "low_delay", 0);			// 低延迟标志
		av_dict_set(&options, "probesize", "262144", 0);		// 增加探测大小，确保正确识别流
		av_dict_set(&options, "analyzeduration", "1000000", 0); // 增加分析时间到1秒，确保流信息准确

		av_dict_set(&options, "reconnect", "1", 0);			  // 断线重连
		av_dict_set(&options, "reconnect_streamed", "1", 0);  // 流媒体重连
		av_dict_set(&options, "reconnect_delay_max", "2", 0); // 减小最大重连延迟到2秒

		// 设置中断回调，用于处理超时
		formatContext->interrupt_callback.callback = [](void *ctx) -> int
		{
			// 超时中断处理
			return 0; // 返回1表示中断
		};
		formatContext->interrupt_callback.opaque = this;

		// 打开输入流
		int ret = avformat_open_input(&formatContext, rtspUrl.toUtf8().constData(), nullptr, &options);

		// 释放选项字典（无论成功与否都要释放）
		av_dict_free(&options);

		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开RTSP流: " << rtspUrl << ", 错误: " << errBuf);
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 查找流信息
		ret = avformat_find_stream_info(formatContext, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法找到流信息, 错误: " << errBuf);
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 查找视频流和音频流
		videoStreamIndex = -1;
		audioStreamIndex = -1;
		for (unsigned int i = 0; i < formatContext->nb_streams; i++)
		{
			if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO && videoStreamIndex == -1)
			{
				videoStreamIndex = i;
				LOG_DEBUG("找到视频流，索引: " << i);
			}
			else if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && audioStreamIndex == -1)
			{
				audioStreamIndex = i;
				LOG_DEBUG("找到音频流，索引: " << i);
			}
		}

		if (videoStreamIndex == -1)
		{
			LOG_ERROR("未找到视频流");
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		if (audioStreamIndex == -1)
		{
			LOG_WARNING("未找到音频流，将只播放视频");
		}

		// 获取编解码器
		AVCodecParameters *codecParams = formatContext->streams[videoStreamIndex]->codecpar;
		const AVCodec *codec = avcodec_find_decoder(codecParams->codec_id);
		if (!codec)
		{
			LOG_ERROR("未找到解码器");
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 创建编解码器上下文
		codecContext = avcodec_alloc_context3(codec);
		if (!codecContext)
		{
			LOG_ERROR("无法分配编解码器上下文");
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 将编解码器参数复制到上下文
		ret = avcodec_parameters_to_context(codecContext, codecParams);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法复制编解码器参数, 错误: " << errBuf);
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 打开解码器
		ret = avcodec_open2(codecContext, codec, nullptr);
		if (ret < 0)
		{
			char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
			av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
			LOG_ERROR("无法打开解码器, 错误: " << errBuf);
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 分配帧和数据包
		frame = av_frame_alloc();
		if (!frame)
		{
			LOG_ERROR("无法分配帧");
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		packet = av_packet_alloc();
		if (!packet)
		{
			LOG_ERROR("无法分配数据包");
			cleanupFFmpeg(); // 清理已分配的资源
			return false;
		}

		// 初始化音频解码器（如果有音频流）
		if (audioStreamIndex != -1)
		{
			// 获取音频编解码器
			AVCodecParameters *audioCodecParams = formatContext->streams[audioStreamIndex]->codecpar;
			const AVCodec *audioCodec = avcodec_find_decoder(audioCodecParams->codec_id);
			if (!audioCodec)
			{
				LOG_WARNING("未找到音频解码器，将跳过音频");
				audioStreamIndex = -1; // 标记为无音频
			}
			else
			{
				// 创建音频编解码器上下文
				audioCodecContext = avcodec_alloc_context3(audioCodec);
				if (!audioCodecContext)
				{
					LOG_WARNING("无法分配音频编解码器上下文，将跳过音频");
					audioStreamIndex = -1;
				}
				else
				{
					// 将音频编解码器参数复制到上下文
					ret = avcodec_parameters_to_context(audioCodecContext, audioCodecParams);
					if (ret < 0)
					{
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_WARNING("无法复制音频编解码器参数, 错误: " << errBuf << "，将跳过音频");
						avcodec_free_context(&audioCodecContext);
						audioStreamIndex = -1;
					}
					else
					{
						// 打开音频解码器
						ret = avcodec_open2(audioCodecContext, audioCodec, nullptr);
						if (ret < 0)
						{
							char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
							av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
							LOG_WARNING("无法打开音频解码器, 错误: " << errBuf << "，将跳过音频");
							avcodec_free_context(&audioCodecContext);
							audioStreamIndex = -1;
						}
						else
						{
							// 分配音频帧
							audioFrame = av_frame_alloc();
							if (!audioFrame)
							{
								LOG_WARNING("无法分配音频帧，将跳过音频");
								avcodec_free_context(&audioCodecContext);
								audioStreamIndex = -1;
							}
							else
							{
								LOG_DEBUG("音频解码器初始化成功, 音频流索引: " << audioStreamIndex << ", 编解码器: " << audioCodec->name);
								// 初始化音频输出
								initAudioOutput();
							}
						}
					}
				}
			}
		}

		// 初始化成功
		LOG_DEBUG("FFmpeg初始化成功, 视频流索引: " << videoStreamIndex << ", 分辨率: " << codecContext->width << "x" << codecContext->height << ", 编解码器: " << codec->name);

		return true;
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg初始化过程中发生异常: " << e.what());
		cleanupFFmpeg(); // 清理已分配的资源
		return false;
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg初始化过程中发生未知异常");
		cleanupFFmpeg(); // 清理已分配的资源
		return false;
	}
}

// FFmpeg清理方法
void MyThread::cleanupFFmpeg()
{
	try
	{
		LOG_DEBUG("清理FFmpeg资源");

		// 按照与初始化相反的顺序清理资源
		if (swsContext)
		{
			sws_freeContext(swsContext);
			swsContext = nullptr;
		}

		if (packet)
		{
			av_packet_free(&packet);
			packet = nullptr;
		}

		if (frame)
		{
			av_frame_free(&frame);
			frame = nullptr;
		}

		if (audioFrame)
		{
			av_frame_free(&audioFrame);
			audioFrame = nullptr;
		}

		if (codecContext)
		{
			avcodec_free_context(&codecContext);
			codecContext = nullptr;
		}

		if (audioCodecContext)
		{
			avcodec_free_context(&audioCodecContext);
			audioCodecContext = nullptr;
		}

		if (formatContext)
		{
			avformat_close_input(&formatContext);
			formatContext = nullptr;
		}

		// 清理FFmpeg网络资源
		avformat_network_deinit();

		// 清理音频输出
		cleanupAudioOutput();

		// 重置流索引
		videoStreamIndex = -1;
		audioStreamIndex = -1;

		LOG_DEBUG("FFmpeg资源清理完成");
	}
	catch (...)
	{
		LOG_ERROR("清理FFmpeg资源时发生未知异常");
	}
}

// 使用FFmpeg处理RTSP流
void MyThread::handleRtspWithFFmpeg()
{
	LOG_DEBUG("使用FFmpeg处理RTSP流: " << rtspUrl);

	try
	{
		// 初始化FFmpeg
		if (!initFFmpeg())
		{
			LOG_ERROR("FFmpeg初始化失败");

			isRunning = false;
			m_threadStopped = true;

			emit disconnectSlot();
			return;
		}

		// 开始读取和处理帧
		LOG_DEBUG("开始读取视频帧");

		// 获取视频流的帧率
		double framerate = 25.0; // 默认帧率
		if (formatContext->streams[videoStreamIndex]->avg_frame_rate.num &&
			formatContext->streams[videoStreamIndex]->avg_frame_rate.den)
		{
			framerate = (double)formatContext->streams[videoStreamIndex]->avg_frame_rate.num /
						formatContext->streams[videoStreamIndex]->avg_frame_rate.den;
		}
		int frameInterval = 1000 / (int)framerate; // 帧间隔(毫秒)
		LOG_DEBUG("视频帧率: " << framerate << " fps, 帧间隔: " << frameInterval << " ms");

		// 帧缓存控制 - 稳定性优先，减少花屏
		const int MAX_FRAME_CACHE = 8; // 增加缓存帧数，提高稳定性，减少花屏
		int queuedFrames = 0;

		// 用于计算帧率
		int frameCount = 0;
		QTime fpsTimer;
		fpsTimer.start();

		// 用于周期性检查线程状态
		QElapsedTimer checkTimer;
		checkTimer.start();

		// 用于帧同步
		QElapsedTimer frameTimer;
		frameTimer.start();

		// 用于检测和处理延迟
		QElapsedTimer delayTimer;
		delayTimer.start();
		int64_t lastPts = AV_NOPTS_VALUE;
		int skipFrameCount = 0;
		int processedFrameCount = 0;
		bool frameDelayDetected = false;

		// 预先准备QImage对象，避免频繁创建和销毁
		QImage processedImage;
		bool hasInitializedImage = false;

		while (true)
		{
			try
			{
				// 周期性检查线程是否应该停止（每100毫秒检查一次）
				if (checkTimer.elapsed() > 100)
				{
					if (!isRunning)
					{
						break;
					}
					checkTimer.restart();
				}

				// 每10秒检查一次是否出现延迟问题
				if (delayTimer.elapsed() > 10000)
				{
					LOG_DEBUG("性能统计: 处理帧数=" << processedFrameCount << ", 丢弃帧数=" << skipFrameCount
													<< ", 当前队列=" << queuedFrames);
					processedFrameCount = 0;
					skipFrameCount = 0;
					delayTimer.restart();
				}

				// 读取下一帧
				int ret = av_read_frame(formatContext, packet);
				if (ret < 0)
				{
					// 遇到错误，可能是流结束或网络问题
					if (ret == AVERROR_EOF || ret == AVERROR(EAGAIN))
					{
						// 流结束或需要等待更多数据
						LOG_DEBUG("等待更多数据或流结束");
						QThread::msleep(100);
						continue;
					}
					else
					{
						// 其他错误
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("读取帧失败: " << errBuf);

						// 重试几次后再退出
						static int errorCount = 0;
						if (++errorCount > 5)
						{
							LOG_ERROR("连续读取帧失败超过5次，退出处理");
							break;
						}

						QThread::msleep(200);
						continue;
					}
				}

				// 处理视频帧
				if (packet->stream_index == videoStreamIndex)
				{
					// 保守的帧丢弃策略：确保解码器稳定性
					if (queuedFrames >= MAX_FRAME_CACHE)
					{
						// 检查是否为关键帧（I帧）
						bool isKeyFrame = (packet->flags & AV_PKT_FLAG_KEY) != 0;

						if (!isKeyFrame)
						{
							// 只丢弃非关键帧，保持解码器状态稳定
							skipFrameCount++;
							av_packet_unref(packet);
							continue;
						}
						else
						{
							// 如果是关键帧，只减少一帧的计数
							queuedFrames--;
						}
					}

					// 时间戳检查，防止延迟累积
					if (packet->pts != AV_NOPTS_VALUE && lastPts != AV_NOPTS_VALUE)
					{
						// 获取时间基准
						AVRational time_base = formatContext->streams[videoStreamIndex]->time_base;

						// 计算相对于上一帧的时间戳差异（毫秒）
						int64_t pts_diff = av_rescale_q(packet->pts - lastPts,
														time_base,
														AVRational{1, 1000});

						// 计算预期的帧间隔时间（毫秒）
						int expected_interval = frameInterval;

						// 如果时间戳差异超过预期间隔的5倍，检测到严重延迟
						if (pts_diff > expected_interval * 5 && !frameDelayDetected)
						{
							LOG_WARNING("检测到视频延迟: 时间戳差异=" << pts_diff << "ms, 预期间隔=" << expected_interval << "ms");
							frameDelayDetected = true;

							// 遇到严重延迟时，清空队列
							queuedFrames = 0;
						}
					}

					// 更新最后一个时间戳
					if (packet->pts != AV_NOPTS_VALUE)
					{
						lastPts = packet->pts;
					}

					// 增加队列中的帧数
					queuedFrames++;

					// 发送数据包到解码器前进行额外检查
					if (!codecContext || !packet->data || packet->size <= 0)
					{
						LOG_ERROR("无效的解码器上下文或数据包");
						av_packet_unref(packet);
						queuedFrames--; // 调整队列计数
						continue;
					}

					ret = avcodec_send_packet(codecContext, packet);
					if (ret < 0)
					{
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_ERROR("发送视频数据包失败: " << errBuf);

						// 如果是严重错误，尝试刷新解码器
						if (ret == AVERROR(EINVAL) || ret == AVERROR(ENOMEM))
						{
							LOG_WARNING("严重解码错误，尝试刷新解码器");
							avcodec_flush_buffers(codecContext);
						}

						av_packet_unref(packet);
						queuedFrames--; // 调整队列计数
						continue;
					}

					// 从解码器接收帧
					bool frameProcessed = false;
					while (ret >= 0 && !frameProcessed)
					{
						// 再次检查线程状态
						if (!isRunning)
						{
							break;
						}

						ret = avcodec_receive_frame(codecContext, frame);
						if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
						{
							// 需要更多输入数据或已到流尾
							break;
						}
						else if (ret < 0)
						{
							char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
							av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
							LOG_ERROR("接收视频帧失败: " << errBuf);

							// 如果是解码错误，尝试刷新解码器缓冲区
							if (ret != AVERROR(EAGAIN) && ret != AVERROR_EOF)
							{
								LOG_WARNING("解码错误，刷新解码器缓冲区");
								avcodec_flush_buffers(codecContext);

								// 重置SwsContext以防格式问题
								if (swsContext)
								{
									sws_freeContext(swsContext);
									swsContext = nullptr;
								}
							}
							break;
						}

						try
						{
							// 验证帧数据的有效性
							if (!frame->data[0] || frame->width <= 0 || frame->height <= 0)
							{
								LOG_ERROR("接收到无效的视频帧数据");
								break;
							}

							// 检查帧格式是否发生变化
							if (swsContext && (frame->width != codecContext->width ||
											   frame->height != codecContext->height ||
											   frame->format != codecContext->pix_fmt))
							{
								LOG_DEBUG("视频格式发生变化，重新创建缩放上下文");
								sws_freeContext(swsContext);
								swsContext = nullptr;
							}

							// 创建或更新缩放上下文
							if (!swsContext)
							{
								swsContext = sws_getContext(
									frame->width, frame->height, (AVPixelFormat)frame->format,
									frame->width, frame->height, AV_PIX_FMT_RGB24,
									SWS_BILINEAR, nullptr, nullptr, nullptr);

								if (!swsContext)
								{
									LOG_ERROR("无法创建缩放上下文，源格式: " << frame->format
																			 << ", 尺寸: " << frame->width << "x" << frame->height);
									break;
								}
								LOG_DEBUG("成功创建缩放上下文，尺寸: " << frame->width << "x" << frame->height);
							}

							// 创建QImage接收转换后的数据 - 使用实际帧尺寸
							if (!hasInitializedImage || processedImage.width() != frame->width ||
								processedImage.height() != frame->height)
							{
								processedImage = QImage(frame->width, frame->height, QImage::Format_RGB888);
								hasInitializedImage = true;
								LOG_DEBUG("重新创建QImage，尺寸: " << frame->width << "x" << frame->height);
							}

							// 验证QImage是否创建成功
							if (processedImage.isNull() || !processedImage.bits())
							{
								LOG_ERROR("QImage创建失败或无效");
								break;
							}

							// 设置目标数据指针和行大小
							uint8_t *destData[4] = {processedImage.bits(), nullptr, nullptr, nullptr};
							int destLinesize[4] = {processedImage.bytesPerLine(), 0, 0, 0};

							// 执行颜色空间转换 - 使用实际帧高度
							int scaledHeight = sws_scale(swsContext, frame->data, frame->linesize, 0,
														 frame->height, destData, destLinesize);

							if (scaledHeight <= 0)
							{
								LOG_ERROR("颜色空间转换失败");
								break;
							}

							// 基于帧率控制帧发送节奏
							int elapsed = frameTimer.elapsed();
							if (elapsed < frameInterval && !frameDelayDetected)
							{
								// 等待适当的时间以保持正确的帧率
								QThread::msleep(frameInterval - elapsed);
							}
							frameTimer.restart();

							// 在这里进行传输前的缓冲区处理
							QImage copyToSend = processedImage.copy(); // 创建副本用于发送

							// 更新lastFrame - 使用最小的互斥锁范围
							mutex.lock();
							lastFrame = copyToSend; // 深拷贝，避免数据竞争
							mutex.unlock();

							// 再次检查线程状态
							if (isRunning)
							{
								emit transmitData(copyToSend);
								processedFrameCount++;
								frameProcessed = true;

								// 成功处理一帧后，减少队列计数
								queuedFrames--;
							}

							// 计算并显示帧率
							frameCount++;
							if (fpsTimer.elapsed() >= 5000)
							{ // 每5秒显示一次帧率
								double fps = frameCount / (fpsTimer.elapsed() / 1000.0);
								LOG_DEBUG("当前帧率: " << fps << " fps, 队列中帧数: " << queuedFrames);
								frameCount = 0;
								fpsTimer.restart();
							}
						}
						catch (const std::exception &e)
						{
							LOG_ERROR("处理视频帧数据时发生异常: " << e.what());
						}
						catch (...)
						{
							LOG_ERROR("处理视频帧数据时发生未知异常");
						}
					}
				}
				// 处理音频帧
				else if (packet->stream_index == audioStreamIndex && audioCodecContext && !isMuted)
				{
					// 发送音频数据包到解码器
					int ret = avcodec_send_packet(audioCodecContext, packet);
					if (ret < 0)
					{
						char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
						av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
						LOG_DEBUG("发送音频数据包失败: " << errBuf);
					}
					else
					{
						// 从解码器接收音频帧
						while (ret >= 0)
						{
							ret = avcodec_receive_frame(audioCodecContext, audioFrame);
							if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
							{
								break; // 需要更多输入数据或已到流尾
							}
							else if (ret < 0)
							{
								char errBuf[AV_ERROR_MAX_STRING_SIZE] = {0};
								av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
								LOG_DEBUG("接收音频帧失败: " << errBuf);
								break;
							}

							// 播放音频帧
							if (audioDevice && audioOutput)
							{
								LOG_DEBUG_ONCE("音频输出状态: " << audioOutput->state() << ", 错误: " << audioOutput->error());

								if (audioOutput->state() == QAudio::ActiveState)
								{
									// 简化的音频播放：直接写入原始PCM数据
									// 注意：这里假设音频格式兼容，实际应用中可能需要格式转换
									int dataSize = av_samples_get_buffer_size(nullptr, audioFrame->channels,
																			  audioFrame->nb_samples,
																			  (AVSampleFormat)audioFrame->format, 1);
									if (dataSize > 0 && audioFrame->data[0])
									{
										qint64 written = audioDevice->write((const char *)audioFrame->data[0], dataSize);
										LOG_DEBUG_ONCE("写入音频数据: " << written << " / " << dataSize << " 字节");
									}
									else
									{
										LOG_DEBUG_ONCE("音频数据无效，dataSize: " << dataSize);
									}
								}
								else
								{
									LOG_DEBUG_ONCE("音频输出状态不是 ActiveState: " << audioOutput->state());
								}
							}
							else
							{
								LOG_DEBUG_ONCE("音频设备或输出为空");
							}
							LOG_DEBUG_ONCE("音频帧解码成功，采样率: " << audioFrame->sample_rate << ", 通道数: " << audioFrame->channels << ", 格式: " << audioFrame->format);
						}
					}
				}

				// 释放数据包
				av_packet_unref(packet);

				// 平衡CPU使用和响应速度的睡眠策略
				if (frameDelayDetected)
				{
					// 如果检测到延迟，不睡眠，快速处理
					QThread::yieldCurrentThread();
				}
				else if (queuedFrames >= MAX_FRAME_CACHE)
				{
					// 如果缓存满，稍微睡眠避免过度消耗CPU
					QThread::msleep(2);
				}
				else if (queuedFrames < MAX_FRAME_CACHE / 2)
				{
					// 如果队列不满，适当睡眠节省CPU
					QThread::msleep(3);
				}
				else
				{
					// 正常情况下的短暂睡眠
					QThread::msleep(1);
				}
			}
			catch (const std::exception &e)
			{
				LOG_ERROR("处理帧时发生异常: " << e.what());
				QThread::msleep(500); // 出错后稍微暂停一下
			}
			catch (...)
			{
				LOG_ERROR("处理帧时发生未知异常");
				QThread::msleep(500);
			}
		}

		// 清理资源 - 使用互斥锁保护
		mutex.lock();
		cleanupFFmpeg();
		mutex.unlock();

		LOG_DEBUG("FFmpeg处理结束");
	}
	catch (const std::exception &e)
	{
		LOG_ERROR("FFmpeg处理过程中发生异常: " << e.what());

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
	catch (...)
	{
		LOG_ERROR("FFmpeg处理过程中发生未知异常");

		isRunning = false;
		m_threadStopped = true;

		emit disconnectSlot();
	}
}

// 新增：使用外部ffmpeg.exe进行录像的方法
bool MyThread::startRecordingWithFFmpegExe(const QString &outputFile)
{
	if (!isRunning)
	{
		LOG_ERROR("线程未运行，无法开始录像");
		return false;
	}

	// 检查ffmpeg.exe是否存在
	QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";
	if (!QFile::exists(ffmpegPath))
	{
		LOG_ERROR("未找到ffmpeg.exe: " << ffmpegPath);
		return false;
	}

	// 停止之前的录像进程（如果有）
	stopRecordingWithFFmpegExe();

	// 创建录像进程
	recordProcess = new QProcess(this);

	// 连接进程结束信号，以便清理资源
	connect(recordProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
			[this](int exitCode, QProcess::ExitStatus exitStatus)
			{
				LOG_DEBUG("录像进程结束，退出码: " << exitCode);
				recordProcess->deleteLater();
				recordProcess = nullptr;
			});

	// 连接错误信号
	connect(recordProcess, &QProcess::errorOccurred,
			[this](QProcess::ProcessError error)
			{
				LOG_ERROR("录像进程错误: " << error);
			});

	// 设置命令行参数
	QStringList arguments;

	// 输入是RTSP流
	arguments << "-i" << rtspUrl;

	// 添加编码和输出选项
	arguments << "-c:v" << "copy" // 复制视频流，不重新编码
			  << "-c:a" << "copy" // 复制音频流，不重新编码
			  << "-y"			  // 覆盖已有文件
			  << outputFile;	  // 输出文件路径

	LOG_DEBUG("启动录像进程: " << ffmpegPath << arguments.join(" "));

	// 启动进程
	recordProcess->start(ffmpegPath, arguments);

	// 等待进程启动
	if (!recordProcess->waitForStarted(3000))
	{
		LOG_ERROR("录像进程启动失败");
		recordProcess->deleteLater();
		recordProcess = nullptr;
		return false;
	}

	LOG_INFO("录像已开始，输出到: " << outputFile);
	return true;
}

// 新增：停止外部ffmpeg.exe录像
void MyThread::stopRecordingWithFFmpegExe()
{
	if (recordProcess)
	{
// 发送退出信号
#ifdef Q_OS_WIN
		// Windows下使用taskkill强制结束进程
		QProcess::execute("taskkill", QStringList() << "/F" << "/PID" << QString::number(recordProcess->processId()));
#else
		// 其他系统使用terminate
		recordProcess->terminate();
		if (!recordProcess->waitForFinished(3000))
		{
			recordProcess->kill();
		}
#endif

		recordProcess->deleteLater();
		recordProcess = nullptr;
		LOG_INFO("录像已停止");
	}
}

// 初始化音频输出
void MyThread::initAudioOutput()
{
	if (audioStreamIndex == -1 || !audioCodecContext)
	{
		return; // 没有音频流
	}

	try
	{
		// 设置音频格式
		QAudioFormat format;
		format.setSampleRate(audioCodecContext->sample_rate);
		format.setChannelCount(audioCodecContext->channels);
		format.setSampleSize(16); // 16位采样
		format.setCodec("audio/pcm");
		format.setByteOrder(QAudioFormat::LittleEndian);
		format.setSampleType(QAudioFormat::SignedInt);

		LOG_DEBUG("尝试初始化音频输出:");
		LOG_DEBUG("  采样率: " << audioCodecContext->sample_rate);
		LOG_DEBUG("  通道数: " << audioCodecContext->channels);
		LOG_DEBUG("  音频格式: " << audioCodecContext->sample_fmt);

		// 检查格式是否支持
		QAudioDeviceInfo info(QAudioDeviceInfo::defaultOutputDevice());
		if (!info.isFormatSupported(format))
		{
			LOG_WARNING("音频格式不支持，尝试使用最近似的格式");
			format = info.nearestFormat(format);
			LOG_DEBUG("调整后的格式:");
			LOG_DEBUG("  采样率: " << format.sampleRate());
			LOG_DEBUG("  通道数: " << format.channelCount());
			LOG_DEBUG("  采样大小: " << format.sampleSize());
		}

		// 创建音频输出
		audioOutput = new QAudioOutput(format, this);
		audioOutput->setVolume(isMuted ? 0.0 : 1.0);

		// 启动音频输出
		audioDevice = audioOutput->start();

		if (audioOutput->error() != QAudio::NoError)
		{
			LOG_ERROR("音频输出错误: " << audioOutput->error());
		}
		else
		{
			LOG_DEBUG("音频输出初始化成功，采样率: " << format.sampleRate() << ", 通道数: " << format.channelCount());
			LOG_DEBUG("音频输出状态: " << audioOutput->state());
		}
	}
	catch (...)
	{
		LOG_ERROR("音频输出初始化失败");
		cleanupAudioOutput();
	}
}

// 清理音频输出
void MyThread::cleanupAudioOutput()
{
	if (audioOutput)
	{
		audioOutput->stop();
		audioOutput->deleteLater();
		audioOutput = nullptr;
	}
	audioDevice = nullptr;
	LOG_DEBUG("音频输出清理完成");
}
