<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 6.8, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Formats Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Formats Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<div class="Contents_element" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Format-Options" href="#Format-Options">2 Format Options</a>
  <ul class="no-bullet">
    <li><a id="toc-Format-stream-specifiers-1" href="#Format-stream-specifiers-1">2.1 Format stream specifiers</a></li>
  </ul></li>
  <li><a id="toc-Demuxers" href="#Demuxers">3 Demuxers</a>
  <ul class="no-bullet">
    <li><a id="toc-aa" href="#aa">3.1 aa</a></li>
    <li><a id="toc-aac" href="#aac">3.2 aac</a></li>
    <li><a id="toc-apng" href="#apng">3.3 apng</a></li>
    <li><a id="toc-asf-1" href="#asf-1">3.4 asf</a></li>
    <li><a id="toc-concat-1" href="#concat-1">3.5 concat</a>
    <ul class="no-bullet">
      <li><a id="toc-Syntax" href="#Syntax">3.5.1 Syntax</a></li>
      <li><a id="toc-Options" href="#Options">3.5.2 Options</a></li>
      <li><a id="toc-Examples" href="#Examples">3.5.3 Examples</a></li>
    </ul></li>
    <li><a id="toc-dash-1" href="#dash-1">3.6 dash</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-1" href="#Options-1">3.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-imf" href="#imf">3.7 imf</a></li>
    <li><a id="toc-flv_002c-live_005fflv_002c-kux" href="#flv_002c-live_005fflv_002c-kux">3.8 flv, live_flv, kux</a></li>
    <li><a id="toc-gif-1" href="#gif-1">3.9 gif</a></li>
    <li><a id="toc-hls-1" href="#hls-1">3.10 hls</a></li>
    <li><a id="toc-image2-1" href="#image2-1">3.11 image2</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-1" href="#Examples-1">3.11.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-libgme" href="#libgme">3.12 libgme</a></li>
    <li><a id="toc-libmodplug" href="#libmodplug">3.13 libmodplug</a></li>
    <li><a id="toc-libopenmpt" href="#libopenmpt">3.14 libopenmpt</a></li>
    <li><a id="toc-mov_002fmp4_002f3gp" href="#mov_002fmp4_002f3gp">3.15 mov/mp4/3gp</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-2" href="#Options-2">3.15.1 Options</a></li>
      <li><a id="toc-Audible-AAX" href="#Audible-AAX">3.15.2 Audible AAX</a></li>
    </ul></li>
    <li><a id="toc-mpegts" href="#mpegts">3.16 mpegts</a></li>
    <li><a id="toc-mpjpeg" href="#mpjpeg">3.17 mpjpeg</a></li>
    <li><a id="toc-rawvideo" href="#rawvideo">3.18 rawvideo</a></li>
    <li><a id="toc-sbg" href="#sbg">3.19 sbg</a></li>
    <li><a id="toc-tedcaptions" href="#tedcaptions">3.20 tedcaptions</a></li>
    <li><a id="toc-vapoursynth" href="#vapoursynth">3.21 vapoursynth</a></li>
  </ul></li>
  <li><a id="toc-Muxers" href="#Muxers">4 Muxers</a>
  <ul class="no-bullet">
    <li><a id="toc-a64-1" href="#a64-1">4.1 a64</a></li>
    <li><a id="toc-adts-1" href="#adts-1">4.2 adts</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-3" href="#Options-3">4.2.1 Options</a></li>
    </ul></li>
    <li><a id="toc-aiff-1" href="#aiff-1">4.3 aiff</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-4" href="#Options-4">4.3.1 Options</a></li>
    </ul></li>
    <li><a id="toc-alp-1" href="#alp-1">4.4 alp</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-5" href="#Options-5">4.4.1 Options</a></li>
    </ul></li>
    <li><a id="toc-asf-2" href="#asf-2">4.5 asf</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-6" href="#Options-6">4.5.1 Options</a></li>
    </ul></li>
    <li><a id="toc-avi-1" href="#avi-1">4.6 avi</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-7" href="#Options-7">4.6.1 Options</a></li>
    </ul></li>
    <li><a id="toc-chromaprint-1" href="#chromaprint-1">4.7 chromaprint</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-8" href="#Options-8">4.7.1 Options</a></li>
    </ul></li>
    <li><a id="toc-crc-1" href="#crc-1">4.8 crc</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-2" href="#Examples-2">4.8.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-dash-2" href="#dash-2">4.9 dash</a></li>
    <li><a id="toc-fifo-1" href="#fifo-1">4.10 fifo</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-3" href="#Examples-3">4.10.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-flv" href="#flv">4.11 flv</a></li>
    <li><a id="toc-framecrc-1" href="#framecrc-1">4.12 framecrc</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-4" href="#Examples-4">4.12.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-framehash-1" href="#framehash-1">4.13 framehash</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-5" href="#Examples-5">4.13.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-framemd5-1" href="#framemd5-1">4.14 framemd5</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-6" href="#Examples-6">4.14.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-gif-2" href="#gif-2">4.15 gif</a></li>
    <li><a id="toc-hash-1" href="#hash-1">4.16 hash</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-7" href="#Examples-7">4.16.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-hls-2" href="#hls-2">4.17 hls</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-9" href="#Options-9">4.17.1 Options</a></li>
    </ul></li>
    <li><a id="toc-ico-1" href="#ico-1">4.18 ico</a></li>
    <li><a id="toc-image2-2" href="#image2-2">4.19 image2</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-10" href="#Options-10">4.19.1 Options</a></li>
      <li><a id="toc-Examples-8" href="#Examples-8">4.19.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-matroska" href="#matroska">4.20 matroska</a>
    <ul class="no-bullet">
      <li><a id="toc-Metadata" href="#Metadata">4.20.1 Metadata</a></li>
      <li><a id="toc-Options-11" href="#Options-11">4.20.2 Options</a></li>
    </ul></li>
    <li><a id="toc-md5-1" href="#md5-1">4.21 md5</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-9" href="#Examples-9">4.21.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-mov_002c-mp4_002c-ismv" href="#mov_002c-mp4_002c-ismv">4.22 mov, mp4, ismv</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-12" href="#Options-12">4.22.1 Options</a></li>
      <li><a id="toc-Example" href="#Example">4.22.2 Example</a></li>
    </ul></li>
    <li><a id="toc-mp3" href="#mp3">4.23 mp3</a></li>
    <li><a id="toc-mpegts-1" href="#mpegts-1">4.24 mpegts</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-13" href="#Options-13">4.24.1 Options</a></li>
      <li><a id="toc-Example-1" href="#Example-1">4.24.2 Example</a></li>
    </ul></li>
    <li><a id="toc-mxf_002c-mxf_005fd10_002c-mxf_005fopatom" href="#mxf_002c-mxf_005fd10_002c-mxf_005fopatom">4.25 mxf, mxf_d10, mxf_opatom</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-14" href="#Options-14">4.25.1 Options</a></li>
    </ul></li>
    <li><a id="toc-null" href="#null">4.26 null</a></li>
    <li><a id="toc-nut" href="#nut">4.27 nut</a></li>
    <li><a id="toc-ogg" href="#ogg">4.28 ogg</a></li>
    <li><a id="toc-raw-muxers-1" href="#raw-muxers-1">4.29 raw muxers</a>
    <ul class="no-bullet">
      <li><a id="toc-ac3" href="#ac3">4.29.1 ac3</a></li>
      <li><a id="toc-adx" href="#adx">4.29.2 adx</a></li>
      <li><a id="toc-aptx" href="#aptx">4.29.3 aptx</a></li>
      <li><a id="toc-aptx_005fhd" href="#aptx_005fhd">4.29.4 aptx_hd</a></li>
      <li><a id="toc-avs2" href="#avs2">4.29.5 avs2</a></li>
      <li><a id="toc-cavsvideo" href="#cavsvideo">4.29.6 cavsvideo</a></li>
      <li><a id="toc-codec2raw" href="#codec2raw">4.29.7 codec2raw</a></li>
      <li><a id="toc-data" href="#data">4.29.8 data</a></li>
      <li><a id="toc-dirac" href="#dirac">4.29.9 dirac</a></li>
      <li><a id="toc-dnxhd" href="#dnxhd">4.29.10 dnxhd</a></li>
      <li><a id="toc-dts" href="#dts">4.29.11 dts</a></li>
      <li><a id="toc-eac3" href="#eac3">4.29.12 eac3</a></li>
      <li><a id="toc-g722" href="#g722">4.29.13 g722</a></li>
      <li><a id="toc-g723_005f1" href="#g723_005f1">4.29.14 g723_1</a></li>
      <li><a id="toc-g726" href="#g726">4.29.15 g726</a></li>
      <li><a id="toc-g726le" href="#g726le">4.29.16 g726le</a></li>
      <li><a id="toc-gsm" href="#gsm">4.29.17 gsm</a></li>
      <li><a id="toc-h261" href="#h261">4.29.18 h261</a></li>
      <li><a id="toc-h263" href="#h263">4.29.19 h263</a></li>
      <li><a id="toc-h264" href="#h264">4.29.20 h264</a></li>
      <li><a id="toc-hevc" href="#hevc">4.29.21 hevc</a></li>
      <li><a id="toc-m4v" href="#m4v">4.29.22 m4v</a></li>
      <li><a id="toc-mjpeg" href="#mjpeg">4.29.23 mjpeg</a></li>
      <li><a id="toc-mlp" href="#mlp">4.29.24 mlp</a></li>
      <li><a id="toc-mp2" href="#mp2">4.29.25 mp2</a></li>
      <li><a id="toc-mpeg1video" href="#mpeg1video">4.29.26 mpeg1video</a></li>
      <li><a id="toc-mpeg2video" href="#mpeg2video">4.29.27 mpeg2video</a></li>
      <li><a id="toc-obu" href="#obu">4.29.28 obu</a></li>
      <li><a id="toc-rawvideo-1" href="#rawvideo-1">4.29.29 rawvideo</a></li>
      <li><a id="toc-sbc" href="#sbc">4.29.30 sbc</a></li>
      <li><a id="toc-truehd" href="#truehd">4.29.31 truehd</a></li>
      <li><a id="toc-vc1" href="#vc1">4.29.32 vc1</a></li>
    </ul></li>
    <li><a id="toc-segment_002c-stream_005fsegment_002c-ssegment" href="#segment_002c-stream_005fsegment_002c-ssegment">4.30 segment, stream_segment, ssegment</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-15" href="#Options-15">4.30.1 Options</a></li>
      <li><a id="toc-Examples-10" href="#Examples-10">4.30.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-smoothstreaming" href="#smoothstreaming">4.31 smoothstreaming</a></li>
    <li><a id="toc-streamhash-1" href="#streamhash-1">4.32 streamhash</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples-11" href="#Examples-11">4.32.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-tee-1" href="#tee-1">4.33 tee</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-16" href="#Options-16">4.33.1 Options</a></li>
      <li><a id="toc-Examples-12" href="#Examples-12">4.33.2 Examples</a></li>
    </ul></li>
    <li><a id="toc-webm_005fchunk" href="#webm_005fchunk">4.34 webm_chunk</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-17" href="#Options-17">4.34.1 Options</a></li>
      <li><a id="toc-Example-2" href="#Example-2">4.34.2 Example</a></li>
    </ul></li>
    <li><a id="toc-webm_005fdash_005fmanifest" href="#webm_005fdash_005fmanifest">4.35 webm_dash_manifest</a>
    <ul class="no-bullet">
      <li><a id="toc-Options-18" href="#Options-18">4.35.1 Options</a></li>
      <li><a id="toc-Example-3" href="#Example-3">4.35.2 Example</a></li>
    </ul></li>
  </ul></li>
  <li><a id="toc-Metadata-1" href="#Metadata-1">5 Metadata</a></li>
  <li><a id="toc-See-Also" href="#See-Also">6 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">7 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the supported formats (muxers and demuxers)
provided by the libavformat library.
</p>

<a name="Format-Options"></a>
<h2 class="chapter">2 Format Options<span class="pull-right"><a class="anchor hidden-xs" href="#Format-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Format-Options" aria-hidden="true">TOC</a></span></h2>

<p>The libavformat library provides some generic global options, which
can be set on all the muxers and demuxers. In addition each muxer or
demuxer may support so-called private options, which are specific for
that component.
</p>
<p>Options may be set by specifying -<var>option</var> <var>value</var> in the
FFmpeg tools, or by setting the value explicitly in the
<code>AVFormatContext</code> options or using the <samp>libavutil/opt.h</samp> API
for programmatic use.
</p>
<p>The list of supported options follows:
</p>
<dl compact="compact">
<dt><span><samp>avioflags <var>flags</var> (<em>input/output</em>)</samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>direct</samp>&rsquo;</span></dt>
<dd><p>Reduce buffering.
</p></dd>
</dl>

</dd>
<dt><span><samp>probesize <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set probing size in bytes, i.e. the size of the data to analyze to get
stream information. A higher value will enable detecting more
information in case it is dispersed into the stream, but will increase
latency. Must be an integer not lesser than 32. It is 5000000 by default.
</p>
</dd>
<dt><span><samp>max_probe_packets <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set the maximum number of buffered packets when probing a codec.
Default is 2500 packets.
</p>
</dd>
<dt><span><samp>packetsize <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set packet size.
</p>
</dd>
<dt><span><samp>fflags <var>flags</var></samp></span></dt>
<dd><p>Set format flags. Some are implemented for a limited number of formats.
</p>
<p>Possible values for input files:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>discardcorrupt</samp>&rsquo;</span></dt>
<dd><p>Discard corrupted packets.
</p></dd>
<dt><span>&lsquo;<samp>fastseek</samp>&rsquo;</span></dt>
<dd><p>Enable fast, but inaccurate seeks for some formats.
</p></dd>
<dt><span>&lsquo;<samp>genpts</samp>&rsquo;</span></dt>
<dd><p>Generate missing PTS if DTS is present.
</p></dd>
<dt><span>&lsquo;<samp>igndts</samp>&rsquo;</span></dt>
<dd><p>Ignore DTS if PTS is set. Inert when nofillin is set.
</p></dd>
<dt><span>&lsquo;<samp>ignidx</samp>&rsquo;</span></dt>
<dd><p>Ignore index.
</p></dd>
<dt><span>&lsquo;<samp>nobuffer</samp>&rsquo;</span></dt>
<dd><p>Reduce the latency introduced by buffering during initial input streams analysis.
</p></dd>
<dt><span>&lsquo;<samp>nofillin</samp>&rsquo;</span></dt>
<dd><p>Do not fill in missing values in packet fields that can be exactly calculated.
</p></dd>
<dt><span>&lsquo;<samp>noparse</samp>&rsquo;</span></dt>
<dd><p>Disable AVParsers, this needs <code>+nofillin</code> too.
</p></dd>
<dt><span>&lsquo;<samp>sortdts</samp>&rsquo;</span></dt>
<dd><p>Try to interleave output packets by DTS. At present, available only for AVIs with an index.
</p></dd>
</dl>

<p>Possible values for output files:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>autobsf</samp>&rsquo;</span></dt>
<dd><p>Automatically apply bitstream filters as required by the output format. Enabled by default.
</p></dd>
<dt><span>&lsquo;<samp>bitexact</samp>&rsquo;</span></dt>
<dd><p>Only write platform-, build- and time-independent data.
This ensures that file and data checksums are reproducible and match between
platforms. Its primary use is for regression testing.
</p></dd>
<dt><span>&lsquo;<samp>flush_packets</samp>&rsquo;</span></dt>
<dd><p>Write out packets immediately.
</p></dd>
<dt><span>&lsquo;<samp>shortest</samp>&rsquo;</span></dt>
<dd><p>Stop muxing at the end of the shortest stream.
It may be needed to increase max_interleave_delta to avoid flushing the longer
streams before EOF.
</p></dd>
</dl>

</dd>
<dt><span><samp>seek2any <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Allow seeking to non-keyframes on demuxer level when supported if set to 1.
Default is 0.
</p>
</dd>
<dt><span><samp>analyzeduration <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Specify how many microseconds are analyzed to probe the input. A
higher value will enable detecting more accurate information, but will
increase latency. It defaults to 5,000,000 microseconds = 5 seconds.
</p>
</dd>
<dt><span><samp>cryptokey <var>hexadecimal string</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set decryption key.
</p>
</dd>
<dt><span><samp>indexmem <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set max memory used for timestamp index (per stream).
</p>
</dd>
<dt><span><samp>rtbufsize <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set max memory used for buffering real-time frames.
</p>
</dd>
<dt><span><samp>fdebug <var>flags</var> (<em>input/output</em>)</samp></span></dt>
<dd><p>Print specific debug info.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>ts</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>max_delay <var>integer</var> (<em>input/output</em>)</samp></span></dt>
<dd><p>Set maximum muxing or demuxing delay in microseconds.
</p>
</dd>
<dt><span><samp>fpsprobesize <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set number of frames used to probe fps.
</p>
</dd>
<dt><span><samp>audio_preload <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set microseconds by which audio packets should be interleaved earlier.
</p>
</dd>
<dt><span><samp>chunk_duration <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set microseconds for each chunk.
</p>
</dd>
<dt><span><samp>chunk_size <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set size in bytes for each chunk.
</p>
</dd>
<dt><span><samp>err_detect, f_err_detect <var>flags</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set error detection flags. <code>f_err_detect</code> is deprecated and
should be used only via the <code>ffmpeg</code> tool.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>crccheck</samp>&rsquo;</span></dt>
<dd><p>Verify embedded CRCs.
</p></dd>
<dt><span>&lsquo;<samp>bitstream</samp>&rsquo;</span></dt>
<dd><p>Detect bitstream specification deviations.
</p></dd>
<dt><span>&lsquo;<samp>buffer</samp>&rsquo;</span></dt>
<dd><p>Detect improper bitstream length.
</p></dd>
<dt><span>&lsquo;<samp>explode</samp>&rsquo;</span></dt>
<dd><p>Abort decoding on minor error detection.
</p></dd>
<dt><span>&lsquo;<samp>careful</samp>&rsquo;</span></dt>
<dd><p>Consider things that violate the spec and have not been seen in the
wild as errors.
</p></dd>
<dt><span>&lsquo;<samp>compliant</samp>&rsquo;</span></dt>
<dd><p>Consider all spec non compliancies as errors.
</p></dd>
<dt><span>&lsquo;<samp>aggressive</samp>&rsquo;</span></dt>
<dd><p>Consider things that a sane encoder should not do as an error.
</p></dd>
</dl>

</dd>
<dt><span><samp>max_interleave_delta <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set maximum buffering duration for interleaving. The duration is
expressed in microseconds, and defaults to 10000000 (10 seconds).
</p>
<p>To ensure all the streams are interleaved correctly, libavformat will
wait until it has at least one packet for each stream before actually
writing any packets to the output file. When some streams are
&quot;sparse&quot; (i.e. there are large gaps between successive packets), this
can result in excessive buffering.
</p>
<p>This field specifies the maximum difference between the timestamps of the
first and the last packet in the muxing queue, above which libavformat
will output a packet regardless of whether it has queued a packet for all
the streams.
</p>
<p>If set to 0, libavformat will continue buffering packets until it has
a packet for each stream, regardless of the maximum timestamp
difference between the buffered packets.
</p>
</dd>
<dt><span><samp>use_wallclock_as_timestamps <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Use wallclock as timestamps if set to 1. Default is 0.
</p>
</dd>
<dt><span><samp>avoid_negative_ts <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>make_non_negative</samp>&rsquo;</span></dt>
<dd><p>Shift timestamps to make them non-negative.
Also note that this affects only leading negative timestamps, and not
non-monotonic negative timestamps.
</p></dd>
<dt><span>&lsquo;<samp>make_zero</samp>&rsquo;</span></dt>
<dd><p>Shift timestamps so that the first timestamp is 0.
</p></dd>
<dt><span>&lsquo;<samp>auto (default)</samp>&rsquo;</span></dt>
<dd><p>Enables shifting when required by the target format.
</p></dd>
<dt><span>&lsquo;<samp>disabled</samp>&rsquo;</span></dt>
<dd><p>Disables shifting of timestamp.
</p></dd>
</dl>

<p>When shifting is enabled, all output timestamps are shifted by the
same amount. Audio, video, and subtitles desynching and relative
timestamp differences are preserved compared to how they would have
been without shifting.
</p>
</dd>
<dt><span><samp>skip_initial_bytes <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Set number of bytes to skip before reading header and frames if set to 1.
Default is 0.
</p>
</dd>
<dt><span><samp>correct_ts_overflow <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Correct single timestamp overflows if set to 1. Default is 1.
</p>
</dd>
<dt><span><samp>flush_packets <var>integer</var> (<em>output</em>)</samp></span></dt>
<dd><p>Flush the underlying I/O stream after each packet. Default is -1 (auto), which
means that the underlying protocol will decide, 1 enables it, and has the
effect of reducing the latency, 0 disables it and may increase IO throughput in
some cases.
</p>
</dd>
<dt><span><samp>output_ts_offset <var>offset</var> (<em>output</em>)</samp></span></dt>
<dd><p>Set the output time offset.
</p>
<p><var>offset</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#time-duration-syntax">(ffmpeg-utils)the Time duration section in the ffmpeg-utils(1) manual</a>.
</p>
<p>The offset is added by the muxer to the output timestamps.
</p>
<p>Specifying a positive offset means that the corresponding streams are
delayed bt the time duration specified in <var>offset</var>. Default value
is <code>0</code> (meaning that no offset is applied).
</p>
</dd>
<dt><span><samp>format_whitelist <var>list</var> (<em>input</em>)</samp></span></dt>
<dd><p>&quot;,&quot; separated list of allowed demuxers. By default all are allowed.
</p>
</dd>
<dt><span><samp>dump_separator <var>string</var> (<em>input</em>)</samp></span></dt>
<dd><p>Separator used to separate the fields printed on the command line about the
Stream parameters.
For example, to separate the fields with newlines and indentation:
</p><div class="example">
<pre class="example">ffprobe -dump_separator &quot;
                          &quot;  -i ~/videos/matrixbench_mpeg2.mpg
</pre></div>

</dd>
<dt><span><samp>max_streams <var>integer</var> (<em>input</em>)</samp></span></dt>
<dd><p>Specifies the maximum number of streams. This can be used to reject files that
would require too many resources due to a large number of streams.
</p>
</dd>
<dt><span><samp>skip_estimate_duration_from_pts <var>bool</var> (<em>input</em>)</samp></span></dt>
<dd><p>Skip estimation of input duration when calculated using PTS.
At present, applicable for MPEG-PS and MPEG-TS.
</p>
</dd>
<dt><span><samp>strict, f_strict <var>integer</var> (<em>input/output</em>)</samp></span></dt>
<dd><p>Specify how strictly to follow the standards. <code>f_strict</code> is deprecated and
should be used only via the <code>ffmpeg</code> tool.
</p>
<p>Possible values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>very</samp>&rsquo;</span></dt>
<dd><p>strictly conform to an older more strict version of the spec or reference software
</p></dd>
<dt><span>&lsquo;<samp>strict</samp>&rsquo;</span></dt>
<dd><p>strictly conform to all the things in the spec no matter what consequences
</p></dd>
<dt><span>&lsquo;<samp>normal</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>unofficial</samp>&rsquo;</span></dt>
<dd><p>allow unofficial extensions
</p></dd>
<dt><span>&lsquo;<samp>experimental</samp>&rsquo;</span></dt>
<dd><p>allow non standardized experimental things, experimental
(unfinished/work in progress/not well tested) decoders and encoders.
Note: experimental decoders can pose a security risk, do not use this for
decoding untrusted input.
</p></dd>
</dl>

</dd>
</dl>


<span id="Format-stream-specifiers"></span><a name="Format-stream-specifiers-1"></a>
<h3 class="section">2.1 Format stream specifiers<span class="pull-right"><a class="anchor hidden-xs" href="#Format-stream-specifiers-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Format-stream-specifiers-1" aria-hidden="true">TOC</a></span></h3>

<p>Format stream specifiers allow selection of one or more streams that
match specific properties.
</p>
<p>The exact semantics of stream specifiers is defined by the
<code>avformat_match_stream_specifier()</code> function declared in the
<samp>libavformat/avformat.h</samp> header and documented in the
<a data-manual="ffmpeg" href="ffmpeg.html#Stream-specifiers">(ffmpeg)Stream specifiers section in the ffmpeg(1) manual</a>.
</p>
<a name="Demuxers"></a>
<h2 class="chapter">3 Demuxers<span class="pull-right"><a class="anchor hidden-xs" href="#Demuxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Demuxers" aria-hidden="true">TOC</a></span></h2>

<p>Demuxers are configured elements in FFmpeg that can read the
multimedia streams from a particular type of file.
</p>
<p>When you configure your FFmpeg build, all the supported demuxers
are enabled by default. You can list all available ones using the
configure option <code>--list-demuxers</code>.
</p>
<p>You can disable all the demuxers using the configure option
<code>--disable-demuxers</code>, and selectively enable a single demuxer with
the option <code>--enable-demuxer=<var>DEMUXER</var></code>, or disable it
with the option <code>--disable-demuxer=<var>DEMUXER</var></code>.
</p>
<p>The option <code>-demuxers</code> of the ff* tools will display the list of
enabled demuxers. Use <code>-formats</code> to view a combined list of
enabled demuxers and muxers.
</p>
<p>The description of some of the currently available demuxers follows.
</p>
<a name="aa"></a>
<h3 class="section">3.1 aa<span class="pull-right"><a class="anchor hidden-xs" href="#aa" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aa" aria-hidden="true">TOC</a></span></h3>

<p>Audible Format 2, 3, and 4 demuxer.
</p>
<p>This demuxer is used to demux Audible Format 2, 3, and 4 (.aa) files.
</p>
<a name="aac"></a>
<h3 class="section">3.2 aac<span class="pull-right"><a class="anchor hidden-xs" href="#aac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aac" aria-hidden="true">TOC</a></span></h3>

<p>Raw Audio Data Transport Stream AAC demuxer.
</p>
<p>This demuxer is used to demux an ADTS input containing a single AAC stream
alongwith any ID3v1/2 or APE tags in it.
</p>
<a name="apng"></a>
<h3 class="section">3.3 apng<span class="pull-right"><a class="anchor hidden-xs" href="#apng" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-apng" aria-hidden="true">TOC</a></span></h3>

<p>Animated Portable Network Graphics demuxer.
</p>
<p>This demuxer is used to demux APNG files.
All headers, but the PNG signature, up to (but not including) the first
fcTL chunk are transmitted as extradata.
Frames are then split as being all the chunks between two fcTL ones, or
between the last fcTL and IEND chunks.
</p>
<dl compact="compact">
<dt><span><samp>-ignore_loop <var>bool</var></samp></span></dt>
<dd><p>Ignore the loop variable in the file if set. Default is enabled.
</p>
</dd>
<dt><span><samp>-max_fps <var>int</var></samp></span></dt>
<dd><p>Maximum framerate in frames per second. Default of 0 imposes no limit.
</p>
</dd>
<dt><span><samp>-default_fps <var>int</var></samp></span></dt>
<dd><p>Default framerate in frames per second when none is specified in the file
(0 meaning as fast as possible). Default is 15.
</p>
</dd>
</dl>

<a name="asf-1"></a>
<h3 class="section">3.4 asf<span class="pull-right"><a class="anchor hidden-xs" href="#asf-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-asf-1" aria-hidden="true">TOC</a></span></h3>

<p>Advanced Systems Format demuxer.
</p>
<p>This demuxer is used to demux ASF files and MMS network streams.
</p>
<dl compact="compact">
<dt><span><samp>-no_resync_search <var>bool</var></samp></span></dt>
<dd><p>Do not try to resynchronize by looking for a certain optional start code.
</p></dd>
</dl>

<span id="concat"></span><a name="concat-1"></a>
<h3 class="section">3.5 concat<span class="pull-right"><a class="anchor hidden-xs" href="#concat-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-concat-1" aria-hidden="true">TOC</a></span></h3>

<p>Virtual concatenation script demuxer.
</p>
<p>This demuxer reads a list of files and other directives from a text file and
demuxes them one after the other, as if all their packets had been muxed
together.
</p>
<p>The timestamps in the files are adjusted so that the first file starts at 0
and each next file starts where the previous one finishes. Note that it is
done globally and may cause gaps if all streams do not have exactly the same
length.
</p>
<p>All files must have the same streams (same codecs, same time base, etc.).
</p>
<p>The duration of each file is used to adjust the timestamps of the next file:
if the duration is incorrect (because it was computed using the bit-rate or
because the file is truncated, for example), it can cause artifacts. The
<code>duration</code> directive can be used to override the duration stored in
each file.
</p>
<a name="Syntax"></a>
<h4 class="subsection">3.5.1 Syntax<span class="pull-right"><a class="anchor hidden-xs" href="#Syntax" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Syntax" aria-hidden="true">TOC</a></span></h4>

<p>The script is a text file in extended-ASCII, with one directive per line.
Empty lines, leading spaces and lines starting with &rsquo;#&rsquo; are ignored. The
following directive is recognized:
</p>
<dl compact="compact">
<dt><span><samp><code>file <var>path</var></code></samp></span></dt>
<dd><p>Path to a file to read; special characters and spaces must be escaped with
backslash or single quotes.
</p>
<p>All subsequent file-related directives apply to that file.
</p>
</dd>
<dt><span><samp><code>ffconcat version 1.0</code></samp></span></dt>
<dd><p>Identify the script type and version.
</p>
<p>To make FFmpeg recognize the format automatically, this directive must
appear exactly as is (no extra space or byte-order-mark) on the very first
line of the script.
</p>
</dd>
<dt><span><samp><code>duration <var>dur</var></code></samp></span></dt>
<dd><p>Duration of the file. This information can be specified from the file;
specifying it here may be more efficient or help if the information from the
file is not available or accurate.
</p>
<p>If the duration is set for all files, then it is possible to seek in the
whole concatenated video.
</p>
</dd>
<dt><span><samp><code>inpoint <var>timestamp</var></code></samp></span></dt>
<dd><p>In point of the file. When the demuxer opens the file it instantly seeks to the
specified timestamp. Seeking is done so that all streams can be presented
successfully at In point.
</p>
<p>This directive works best with intra frame codecs, because for non-intra frame
ones you will usually get extra packets before the actual In point and the
decoded content will most likely contain frames before In point too.
</p>
<p>For each file, packets before the file In point will have timestamps less than
the calculated start timestamp of the file (negative in case of the first
file), and the duration of the files (if not specified by the <code>duration</code>
directive) will be reduced based on their specified In point.
</p>
<p>Because of potential packets before the specified In point, packet timestamps
may overlap between two concatenated files.
</p>
</dd>
<dt><span><samp><code>outpoint <var>timestamp</var></code></samp></span></dt>
<dd><p>Out point of the file. When the demuxer reaches the specified decoding
timestamp in any of the streams, it handles it as an end of file condition and
skips the current and all the remaining packets from all streams.
</p>
<p>Out point is exclusive, which means that the demuxer will not output packets
with a decoding timestamp greater or equal to Out point.
</p>
<p>This directive works best with intra frame codecs and formats where all streams
are tightly interleaved. For non-intra frame codecs you will usually get
additional packets with presentation timestamp after Out point therefore the
decoded content will most likely contain frames after Out point too. If your
streams are not tightly interleaved you may not get all the packets from all
streams before Out point and you may only will be able to decode the earliest
stream until Out point.
</p>
<p>The duration of the files (if not specified by the <code>duration</code>
directive) will be reduced based on their specified Out point.
</p>
</dd>
<dt><span><samp><code>file_packet_metadata <var>key=value</var></code></samp></span></dt>
<dd><p>Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
This directive is deprecated, use <code>file_packet_meta</code> instead.
</p>
</dd>
<dt><span><samp><code>file_packet_meta <var>key</var> <var>value</var></code></samp></span></dt>
<dd><p>Metadata of the packets of the file. The specified metadata will be set for
each file packet. You can specify this directive multiple times to add multiple
metadata entries.
</p>
</dd>
<dt><span><samp><code>option <var>key</var> <var>value</var></code></samp></span></dt>
<dd><p>Option to access, open and probe the file.
Can be present multiple times.
</p>
</dd>
<dt><span><samp><code>stream</code></samp></span></dt>
<dd><p>Introduce a stream in the virtual file.
All subsequent stream-related directives apply to the last introduced
stream.
Some streams properties must be set in order to allow identifying the
matching streams in the subfiles.
If no streams are defined in the script, the streams from the first file are
copied.
</p>
</dd>
<dt><span><samp><code>exact_stream_id <var>id</var></code></samp></span></dt>
<dd><p>Set the id of the stream.
If this directive is given, the string with the corresponding id in the
subfiles will be used.
This is especially useful for MPEG-PS (VOB) files, where the order of the
streams is not reliable.
</p>
</dd>
<dt><span><samp><code>stream_meta <var>key</var> <var>value</var></code></samp></span></dt>
<dd><p>Metadata for the stream.
Can be present multiple times.
</p>
</dd>
<dt><span><samp><code>stream_codec <var>value</var></code></samp></span></dt>
<dd><p>Codec for the stream.
</p>
</dd>
<dt><span><samp><code>stream_extradata <var>hex_string</var></code></samp></span></dt>
<dd><p>Extradata for the string, encoded in hexadecimal.
</p>
</dd>
<dt><span><samp><code>chapter <var>id</var> <var>start</var> <var>end</var></code></samp></span></dt>
<dd><p>Add a chapter. <var>id</var> is an unique identifier, possibly small and
consecutive.
</p>
</dd>
</dl>

<a name="Options"></a>
<h4 class="subsection">3.5.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following option:
</p>
<dl compact="compact">
<dt><span><samp>safe</samp></span></dt>
<dd><p>If set to 1, reject unsafe file paths and directives.
A file path is considered safe if it
does not contain a protocol specification and is relative and all components
only contain characters from the portable character set (letters, digits,
period, underscore and hyphen) and have no period at the beginning of a
component.
</p>
<p>If set to 0, any file name is accepted.
</p>
<p>The default is 1.
</p>
</dd>
<dt><span><samp>auto_convert</samp></span></dt>
<dd><p>If set to 1, try to perform automatic conversions on packet data to make the
streams concatenable.
The default is 1.
</p>
<p>Currently, the only conversion is adding the h264_mp4toannexb bitstream
filter to H.264 streams in MP4 format. This is necessary in particular if
there are resolution changes.
</p>
</dd>
<dt><span><samp>segment_time_metadata</samp></span></dt>
<dd><p>If set to 1, every packet will contain the <var>lavf.concat.start_time</var> and the
<var>lavf.concat.duration</var> packet metadata values which are the start_time and
the duration of the respective file segments in the concatenated output
expressed in microseconds. The duration metadata is only set if it is known
based on the concat file.
The default is 0.
</p>
</dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">3.5.3 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Use absolute filenames and include some comments:
<div class="example">
<pre class="example"># my first filename
file /mnt/share/file-1.wav
# my second filename including whitespace
file '/mnt/share/file 2.wav'
# my third filename including whitespace plus single quote
file '/mnt/share/file 3'\''.wav'
</pre></div>

</li><li> Allow for input format auto-probing, use safe filenames and set the duration of
the first file:
<div class="example">
<pre class="example">ffconcat version 1.0

file file-1.wav
duration 20.0

file subdir/file-2.wav
</pre></div>
</li></ul>

<a name="dash-1"></a>
<h3 class="section">3.6 dash<span class="pull-right"><a class="anchor hidden-xs" href="#dash-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dash-1" aria-hidden="true">TOC</a></span></h3>

<p>Dynamic Adaptive Streaming over HTTP demuxer.
</p>
<p>This demuxer presents all AVStreams found in the manifest.
By setting the discard flags on AVStreams the caller can decide
which streams to actually receive.
Each stream mirrors the <code>id</code> and <code>bandwidth</code> properties from the
<code>&lt;Representation&gt;</code> as metadata keys named &quot;id&quot; and &quot;variant_bitrate&quot; respectively.
</p>
<a name="Options-1"></a>
<h4 class="subsection">3.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-1" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following option:
</p>
<dl compact="compact">
<dt><span><samp>cenc_decryption_key</samp></span></dt>
<dd><p>16-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES-128 CTR; ISO/IEC 23001-7).
</p>
</dd>
</dl>

<a name="imf"></a>
<h3 class="section">3.7 imf<span class="pull-right"><a class="anchor hidden-xs" href="#imf" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-imf" aria-hidden="true">TOC</a></span></h3>

<p>Interoperable Master Format demuxer.
</p>
<p>This demuxer presents audio and video streams found in an IMF Composition.
</p>
<a name="flv_002c-live_005fflv_002c-kux"></a>
<h3 class="section">3.8 flv, live_flv, kux<span class="pull-right"><a class="anchor hidden-xs" href="#flv_002c-live_005fflv_002c-kux" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flv_002c-live_005fflv_002c-kux" aria-hidden="true">TOC</a></span></h3>

<p>Adobe Flash Video Format demuxer.
</p>
<p>This demuxer is used to demux FLV files and RTMP network streams. In case of live network streams, if you force format, you may use live_flv option instead of flv to survive timestamp discontinuities.
KUX is a flv variant used on the Youku platform.
</p>
<div class="example">
<pre class="example">ffmpeg -f flv -i myfile.flv ...
ffmpeg -f live_flv -i rtmp://&lt;any.server&gt;/anything/key ....
</pre></div>


<dl compact="compact">
<dt><span><samp>-flv_metadata <var>bool</var></samp></span></dt>
<dd><p>Allocate the streams according to the onMetaData array content.
</p>
</dd>
<dt><span><samp>-flv_ignore_prevtag <var>bool</var></samp></span></dt>
<dd><p>Ignore the size of previous tag value.
</p>
</dd>
<dt><span><samp>-flv_full_metadata <var>bool</var></samp></span></dt>
<dd><p>Output all context of the onMetadata.
</p></dd>
</dl>

<a name="gif-1"></a>
<h3 class="section">3.9 gif<span class="pull-right"><a class="anchor hidden-xs" href="#gif-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gif-1" aria-hidden="true">TOC</a></span></h3>

<p>Animated GIF demuxer.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>min_delay</samp></span></dt>
<dd><p>Set the minimum valid delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 2.
</p>
</dd>
<dt><span><samp>max_gif_delay</samp></span></dt>
<dd><p>Set the maximum valid delay between frames in hundredth of seconds.
Range is 0 to 65535. Default value is 65535 (nearly eleven minutes),
the maximum value allowed by the specification.
</p>
</dd>
<dt><span><samp>default_delay</samp></span></dt>
<dd><p>Set the default delay between frames in hundredths of seconds.
Range is 0 to 6000. Default value is 10.
</p>
</dd>
<dt><span><samp>ignore_loop</samp></span></dt>
<dd><p>GIF files can contain information to loop a certain number of times (or
infinitely). If <samp>ignore_loop</samp> is set to 1, then the loop setting
from the input will be ignored and looping will not occur. If set to 0,
then looping will occur and will cycle the number of times according to
the GIF. Default value is 1.
</p></dd>
</dl>

<p>For example, with the overlay filter, place an infinitely looping GIF
over another video:
</p><div class="example">
<pre class="example">ffmpeg -i input.mp4 -ignore_loop 0 -i input.gif -filter_complex overlay=shortest=1 out.mkv
</pre></div>

<p>Note that in the above example the shortest option for overlay filter is
used to end the output video at the length of the shortest input file,
which in this case is <samp>input.mp4</samp> as the GIF in this example loops
infinitely.
</p>
<a name="hls-1"></a>
<h3 class="section">3.10 hls<span class="pull-right"><a class="anchor hidden-xs" href="#hls-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hls-1" aria-hidden="true">TOC</a></span></h3>

<p>HLS demuxer
</p>
<p>Apple HTTP Live Streaming demuxer.
</p>
<p>This demuxer presents all AVStreams from all variant streams.
The id field is set to the bitrate variant index number. By setting
the discard flags on AVStreams (by pressing &rsquo;a&rsquo; or &rsquo;v&rsquo; in ffplay),
the caller can decide which variant streams to actually receive.
The total bitrate of the variant that the stream belongs to is
available in a metadata key named &quot;variant_bitrate&quot;.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>live_start_index</samp></span></dt>
<dd><p>segment index to start live streams at (negative values are from the end).
</p>
</dd>
<dt><span><samp>prefer_x_start</samp></span></dt>
<dd><p>prefer to use #EXT-X-START if it&rsquo;s in playlist instead of live_start_index.
</p>
</dd>
<dt><span><samp>allowed_extensions</samp></span></dt>
<dd><p>&rsquo;,&rsquo; separated list of file extensions that hls is allowed to access.
</p>
</dd>
<dt><span><samp>max_reload</samp></span></dt>
<dd><p>Maximum number of times a insufficient list is attempted to be reloaded.
Default value is 1000.
</p>
</dd>
<dt><span><samp>m3u8_hold_counters</samp></span></dt>
<dd><p>The maximum number of times to load m3u8 when it refreshes without new segments.
Default value is 1000.
</p>
</dd>
<dt><span><samp>http_persistent</samp></span></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP streams.
Enabled by default.
</p>
</dd>
<dt><span><samp>http_multiple</samp></span></dt>
<dd><p>Use multiple HTTP connections for downloading HTTP segments.
Enabled by default for HTTP/1.1 servers.
</p>
</dd>
<dt><span><samp>http_seekable</samp></span></dt>
<dd><p>Use HTTP partial requests for downloading HTTP segments.
0 = disable, 1 = enable, -1 = auto, Default is auto.
</p>
</dd>
<dt><span><samp>seg_format_options</samp></span></dt>
<dd><p>Set options for the demuxer of media segments using a list of key=value pairs separated by <code>:</code>.
</p></dd>
</dl>

<a name="image2-1"></a>
<h3 class="section">3.11 image2<span class="pull-right"><a class="anchor hidden-xs" href="#image2-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-image2-1" aria-hidden="true">TOC</a></span></h3>

<p>Image file demuxer.
</p>
<p>This demuxer reads from a list of image files specified by a pattern.
The syntax and meaning of the pattern is specified by the
option <var>pattern_type</var>.
</p>
<p>The pattern may contain a suffix which is used to automatically
determine the format of the images contained in the files.
</p>
<p>The size, the pixel format, and the format of each image must be the
same for all the files in the sequence.
</p>
<p>This demuxer accepts the following options:
</p><dl compact="compact">
<dt><span><samp>framerate</samp></span></dt>
<dd><p>Set the frame rate for the video stream. It defaults to 25.
</p></dd>
<dt><span><samp>loop</samp></span></dt>
<dd><p>If set to 1, loop over the input. Default value is 0.
</p></dd>
<dt><span><samp>pattern_type</samp></span></dt>
<dd><p>Select the pattern type used to interpret the provided filename.
</p>
<p><var>pattern_type</var> accepts one of the following values.
</p><dl compact="compact">
<dt><span><samp>none</samp></span></dt>
<dd><p>Disable pattern matching, therefore the video will only contain the specified
image. You should use this option if you do not want to create sequences from
multiple images and your filenames may contain special pattern characters.
</p></dd>
<dt><span><samp>sequence</samp></span></dt>
<dd><p>Select a sequence pattern type, used to specify a sequence of files
indexed by sequential numbers.
</p>
<p>A sequence pattern may contain the string &quot;%d&quot; or &quot;%0<var>N</var>d&quot;, which
specifies the position of the characters representing a sequential
number in each filename matched by the pattern. If the form
&quot;%d0<var>N</var>d&quot; is used, the string representing the number in each
filename is 0-padded and <var>N</var> is the total number of 0-padded
digits representing the number. The literal character &rsquo;%&rsquo; can be
specified in the pattern with the string &quot;%%&quot;.
</p>
<p>If the sequence pattern contains &quot;%d&quot; or &quot;%0<var>N</var>d&quot;, the first filename of
the file list specified by the pattern must contain a number
inclusively contained between <var>start_number</var> and
<var>start_number</var>+<var>start_number_range</var>-1, and all the following
numbers must be sequential.
</p>
<p>For example the pattern &quot;img-%03d.bmp&quot; will match a sequence of
filenames of the form <samp>img-001.bmp</samp>, <samp>img-002.bmp</samp>, ...,
<samp>img-010.bmp</samp>, etc.; the pattern &quot;i%%m%%g-%d.jpg&quot; will match a
sequence of filenames of the form <samp>i%m%g-1.jpg</samp>,
<samp>i%m%g-2.jpg</samp>, ..., <samp>i%m%g-10.jpg</samp>, etc.
</p>
<p>Note that the pattern must not necessarily contain &quot;%d&quot; or
&quot;%0<var>N</var>d&quot;, for example to convert a single image file
<samp>img.jpeg</samp> you can employ the command:
</p><div class="example">
<pre class="example">ffmpeg -i img.jpeg img.png
</pre></div>

</dd>
<dt><span><samp>glob</samp></span></dt>
<dd><p>Select a glob wildcard pattern type.
</p>
<p>The pattern is interpreted like a <code>glob()</code> pattern. This is only
selectable if libavformat was compiled with globbing support.
</p>
</dd>
<dt><span><samp>glob_sequence <em>(deprecated, will be removed)</em></samp></span></dt>
<dd><p>Select a mixed glob wildcard/sequence pattern.
</p>
<p>If your version of libavformat was compiled with globbing support, and
the provided pattern contains at least one glob meta character among
<code>%*?[]{}</code> that is preceded by an unescaped &quot;%&quot;, the pattern is
interpreted like a <code>glob()</code> pattern, otherwise it is interpreted
like a sequence pattern.
</p>
<p>All glob special characters <code>%*?[]{}</code> must be prefixed
with &quot;%&quot;. To escape a literal &quot;%&quot; you shall use &quot;%%&quot;.
</p>
<p>For example the pattern <code>foo-%*.jpeg</code> will match all the
filenames prefixed by &quot;foo-&quot; and terminating with &quot;.jpeg&quot;, and
<code>foo-%?%?%?.jpeg</code> will match all the filenames prefixed with
&quot;foo-&quot;, followed by a sequence of three characters, and terminating
with &quot;.jpeg&quot;.
</p>
<p>This pattern type is deprecated in favor of <var>glob</var> and
<var>sequence</var>.
</p></dd>
</dl>

<p>Default value is <var>glob_sequence</var>.
</p></dd>
<dt><span><samp>pixel_format</samp></span></dt>
<dd><p>Set the pixel format of the images to read. If not specified the pixel
format is guessed from the first image file in the sequence.
</p></dd>
<dt><span><samp>start_number</samp></span></dt>
<dd><p>Set the index of the file matched by the image file pattern to start
to read from. Default value is 0.
</p></dd>
<dt><span><samp>start_number_range</samp></span></dt>
<dd><p>Set the index interval range to check when looking for the first image
file in the sequence, starting from <var>start_number</var>. Default value
is 5.
</p></dd>
<dt><span><samp>ts_from_file</samp></span></dt>
<dd><p>If set to 1, will set frame timestamp to modification time of image file. Note
that monotonity of timestamps is not provided: images go in the same order as
without this option. Default value is 0.
If set to 2, will set frame timestamp to the modification time of the image file in
nanosecond precision.
</p></dd>
<dt><span><samp>video_size</samp></span></dt>
<dd><p>Set the video size of the images to read. If not specified the video
size is guessed from the first image file in the sequence.
</p></dd>
<dt><span><samp>export_path_metadata</samp></span></dt>
<dd><p>If set to 1, will add two extra fields to the metadata found in input, making them
also available for other filters (see <var>drawtext</var> filter for examples). Default
value is 0. The extra fields are described below:
</p><dl compact="compact">
<dt><span><samp>lavf.image2dec.source_path</samp></span></dt>
<dd><p>Corresponds to the full path to the input file being read.
</p></dd>
<dt><span><samp>lavf.image2dec.source_basename</samp></span></dt>
<dd><p>Corresponds to the name of the file being read.
</p></dd>
</dl>

</dd>
</dl>

<a name="Examples-1"></a>
<h4 class="subsection">3.11.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-1" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Use <code>ffmpeg</code> for creating a video from the images in the file
sequence <samp>img-001.jpeg</samp>, <samp>img-002.jpeg</samp>, ..., assuming an
input frame rate of 10 frames per second:
<div class="example">
<pre class="example">ffmpeg -framerate 10 -i 'img-%03d.jpeg' out.mkv
</pre></div>

</li><li> As above, but start by reading from a file with index 100 in the sequence:
<div class="example">
<pre class="example">ffmpeg -framerate 10 -start_number 100 -i 'img-%03d.jpeg' out.mkv
</pre></div>

</li><li> Read images matching the &quot;*.png&quot; glob pattern , that is all the files
terminating with the &quot;.png&quot; suffix:
<div class="example">
<pre class="example">ffmpeg -framerate 10 -pattern_type glob -i &quot;*.png&quot; out.mkv
</pre></div>
</li></ul>

<a name="libgme"></a>
<h3 class="section">3.12 libgme<span class="pull-right"><a class="anchor hidden-xs" href="#libgme" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libgme" aria-hidden="true">TOC</a></span></h3>

<p>The Game Music Emu library is a collection of video game music file emulators.
</p>
<p>See <a href="https://bitbucket.org/mpyne/game-music-emu/overview">https://bitbucket.org/mpyne/game-music-emu/overview</a> for more information.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>track_index</samp></span></dt>
<dd><p>Set the index of which track to demux. The demuxer can only export one track.
Track indexes start at 0. Default is to pick the first track. Number of tracks
is exported as <var>tracks</var> metadata entry.
</p>
</dd>
<dt><span><samp>sample_rate</samp></span></dt>
<dd><p>Set the sampling rate of the exported track. Range is 1000 to 999999. Default is 44100.
</p>
</dd>
<dt><span><samp>max_size <em>(bytes)</em></samp></span></dt>
<dd><p>The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read.
Default is 50 MiB.
</p>
</dd>
</dl>

<a name="libmodplug"></a>
<h3 class="section">3.13 libmodplug<span class="pull-right"><a class="anchor hidden-xs" href="#libmodplug" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libmodplug" aria-hidden="true">TOC</a></span></h3>

<p>ModPlug based module demuxer
</p>
<p>See <a href="https://github.com/Konstanty/libmodplug">https://github.com/Konstanty/libmodplug</a>
</p>
<p>It will export one 2-channel 16-bit 44.1 kHz audio stream.
Optionally, a <code>pal8</code> 16-color video stream can be exported with or without printed metadata.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>noise_reduction</samp></span></dt>
<dd><p>Apply a simple low-pass filter. Can be 1 (on) or 0 (off). Default is 0.
</p>
</dd>
<dt><span><samp>reverb_depth</samp></span></dt>
<dd><p>Set amount of reverb. Range 0-100. Default is 0.
</p>
</dd>
<dt><span><samp>reverb_delay</samp></span></dt>
<dd><p>Set delay in ms, clamped to 40-250 ms. Default is 0.
</p>
</dd>
<dt><span><samp>bass_amount</samp></span></dt>
<dd><p>Apply bass expansion a.k.a. XBass or megabass. Range is 0 (quiet) to 100 (loud). Default is 0.
</p>
</dd>
<dt><span><samp>bass_range</samp></span></dt>
<dd><p>Set cutoff i.e. upper-bound for bass frequencies. Range is 10-100 Hz. Default is 0.
</p>
</dd>
<dt><span><samp>surround_depth</samp></span></dt>
<dd><p>Apply a Dolby Pro-Logic surround effect. Range is 0 (quiet) to 100 (heavy). Default is 0.
</p>
</dd>
<dt><span><samp>surround_delay</samp></span></dt>
<dd><p>Set surround delay in ms, clamped to 5-40 ms. Default is 0.
</p>
</dd>
<dt><span><samp>max_size</samp></span></dt>
<dd><p>The demuxer buffers the entire file into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of files that can be read. Range is 0 to 100 MiB.
0 removes buffer size limit (not recommended). Default is 5 MiB.
</p>
</dd>
<dt><span><samp>video_stream_expr</samp></span></dt>
<dd><p>String which is evaluated using the eval API to assign colors to the generated video stream.
Variables which can be used are <code>x</code>, <code>y</code>, <code>w</code>, <code>h</code>, <code>t</code>, <code>speed</code>,
<code>tempo</code>, <code>order</code>, <code>pattern</code> and <code>row</code>.
</p>
</dd>
<dt><span><samp>video_stream</samp></span></dt>
<dd><p>Generate video stream. Can be 1 (on) or 0 (off). Default is 0.
</p>
</dd>
<dt><span><samp>video_stream_w</samp></span></dt>
<dd><p>Set video frame width in &rsquo;chars&rsquo; where one char indicates 8 pixels. Range is 20-512. Default is 30.
</p>
</dd>
<dt><span><samp>video_stream_h</samp></span></dt>
<dd><p>Set video frame height in &rsquo;chars&rsquo; where one char indicates 8 pixels. Range is 20-512. Default is 30.
</p>
</dd>
<dt><span><samp>video_stream_ptxt</samp></span></dt>
<dd><p>Print metadata on video stream. Includes <code>speed</code>, <code>tempo</code>, <code>order</code>, <code>pattern</code>,
<code>row</code> and <code>ts</code> (time in ms). Can be 1 (on) or 0 (off). Default is 1.
</p>
</dd>
</dl>

<a name="libopenmpt"></a>
<h3 class="section">3.14 libopenmpt<span class="pull-right"><a class="anchor hidden-xs" href="#libopenmpt" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-libopenmpt" aria-hidden="true">TOC</a></span></h3>

<p>libopenmpt based module demuxer
</p>
<p>See <a href="https://lib.openmpt.org/libopenmpt/">https://lib.openmpt.org/libopenmpt/</a> for more information.
</p>
<p>Some files have multiple subsongs (tracks) this can be set with the <samp>subsong</samp>
option.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>subsong</samp></span></dt>
<dd><p>Set the subsong index. This can be either  &rsquo;all&rsquo;, &rsquo;auto&rsquo;, or the index of the
subsong. Subsong indexes start at 0. The default is &rsquo;auto&rsquo;.
</p>
<p>The default value is to let libopenmpt choose.
</p>
</dd>
<dt><span><samp>layout</samp></span></dt>
<dd><p>Set the channel layout. Valid values are 1, 2, and 4 channel layouts.
The default value is STEREO.
</p>
</dd>
<dt><span><samp>sample_rate</samp></span></dt>
<dd><p>Set the sample rate for libopenmpt to output.
Range is from 1000 to INT_MAX. The value default is 48000.
</p></dd>
</dl>

<a name="mov_002fmp4_002f3gp"></a>
<h3 class="section">3.15 mov/mp4/3gp<span class="pull-right"><a class="anchor hidden-xs" href="#mov_002fmp4_002f3gp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mov_002fmp4_002f3gp" aria-hidden="true">TOC</a></span></h3>

<p>Demuxer for Quicktime File Format &amp; ISO/IEC Base Media File Format (ISO/IEC 14496-12 or MPEG-4 Part 12, ISO/IEC 15444-12 or JPEG 2000 Part 12).
</p>
<p>Registered extensions: mov, mp4, m4a, 3gp, 3g2, mj2, psp, m4b, ism, ismv, isma, f4v
</p>
<a name="Options-2"></a>
<h4 class="subsection">3.15.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-2" aria-hidden="true">TOC</a></span></h4>

<p>This demuxer accepts the following options:
</p><dl compact="compact">
<dt><span><samp>enable_drefs</samp></span></dt>
<dd><p>Enable loading of external tracks, disabled by default.
Enabling this can theoretically leak information in some use cases.
</p>
</dd>
<dt><span><samp>use_absolute_path</samp></span></dt>
<dd><p>Allows loading of external tracks via absolute paths, disabled by default.
Enabling this poses a security risk. It should only be enabled if the source
is known to be non-malicious.
</p>
</dd>
<dt><span><samp>seek_streams_individually</samp></span></dt>
<dd><p>When seeking, identify the closest point in each stream individually and demux packets in
that stream from identified point. This can lead to a different sequence of packets compared
to demuxing linearly from the beginning. Default is true.
</p>
</dd>
<dt><span><samp>ignore_editlist</samp></span></dt>
<dd><p>Ignore any edit list atoms. The demuxer, by default, modifies the stream index to reflect the
timeline described by the edit list. Default is false.
</p>
</dd>
<dt><span><samp>advanced_editlist</samp></span></dt>
<dd><p>Modify the stream index to reflect the timeline described by the edit list. <code>ignore_editlist</code>
must be set to false for this option to be effective.
If both <code>ignore_editlist</code> and this option are set to false, then only the
start of the stream index is modified to reflect initial dwell time or starting timestamp
described by the edit list. Default is true.
</p>
</dd>
<dt><span><samp>ignore_chapters</samp></span></dt>
<dd><p>Don&rsquo;t parse chapters. This includes GoPro &rsquo;HiLight&rsquo; tags/moments. Note that chapters are
only parsed when input is seekable. Default is false.
</p>
</dd>
<dt><span><samp>use_mfra_for</samp></span></dt>
<dd><p>For seekable fragmented input, set fragment&rsquo;s starting timestamp from media fragment random access box, if present.
</p>
<p>Following options are available:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Auto-detect whether to set mfra timestamps as PTS or DTS <em>(default)</em>
</p>
</dd>
<dt><span>&lsquo;<samp>dts</samp>&rsquo;</span></dt>
<dd><p>Set mfra timestamps as DTS
</p>
</dd>
<dt><span>&lsquo;<samp>pts</samp>&rsquo;</span></dt>
<dd><p>Set mfra timestamps as PTS
</p>
</dd>
<dt><span>&lsquo;<samp>0</samp>&rsquo;</span></dt>
<dd><p>Don&rsquo;t use mfra box to set timestamps
</p></dd>
</dl>

</dd>
<dt><span><samp>use_tfdt</samp></span></dt>
<dd><p>For fragmented input, set fragment&rsquo;s starting timestamp to <code>baseMediaDecodeTime</code> from the <code>tfdt</code> box.
Default is enabled, which will prefer to use the <code>tfdt</code> box to set DTS. Disable to use the <code>earliest_presentation_time</code> from the <code>sidx</code> box.
In either case, the timestamp from the <code>mfra</code> box will be used if it&rsquo;s available and <code>use_mfra_for</code> is
set to pts or dts.
</p>
</dd>
<dt><span><samp>export_all</samp></span></dt>
<dd><p>Export unrecognized boxes within the <var>udta</var> box as metadata entries. The first four
characters of the box type are set as the key. Default is false.
</p>
</dd>
<dt><span><samp>export_xmp</samp></span></dt>
<dd><p>Export entire contents of <var>XMP_</var> box and <var>uuid</var> box as a string with key <code>xmp</code>. Note that
if <code>export_all</code> is set and this option isn&rsquo;t, the contents of <var>XMP_</var> box are still exported
but with key <code>XMP_</code>. Default is false.
</p>
</dd>
<dt><span><samp>activation_bytes</samp></span></dt>
<dd><p>4-byte key required to decrypt Audible AAX and AAX+ files. See Audible AAX subsection below.
</p>
</dd>
<dt><span><samp>audible_fixed_key</samp></span></dt>
<dd><p>Fixed key used for handling Audible AAX/AAX+ files. It has been pre-set so should not be necessary to
specify.
</p>
</dd>
<dt><span><samp>decryption_key</samp></span></dt>
<dd><p>16-byte key, in hex, to decrypt files encrypted using ISO Common Encryption (CENC/AES-128 CTR; ISO/IEC 23001-7).
</p>
</dd>
<dt><span><samp>max_stts_delta</samp></span></dt>
<dd><p>Very high sample deltas written in a trak&rsquo;s stts box may occasionally be intended but usually they are written in
error or used to store a negative value for dts correction when treated as signed 32-bit integers. This option lets
the user set an upper limit, beyond which the delta is clamped to 1. Values greater than the limit if negative when
cast to int32 are used to adjust onward dts.
</p>
<p>Unit is the track time scale. Range is 0 to UINT_MAX. Default is <code>UINT_MAX - 48000*10</code> which allows upto
a 10 second dts correction for 48 kHz audio streams while accommodating 99.9% of <code>uint32</code> range.
</p></dd>
</dl>

<a name="Audible-AAX"></a>
<h4 class="subsection">3.15.2 Audible AAX<span class="pull-right"><a class="anchor hidden-xs" href="#Audible-AAX" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Audible-AAX" aria-hidden="true">TOC</a></span></h4>

<p>Audible AAX files are encrypted M4B files, and they can be decrypted by specifying a 4 byte activation secret.
</p><div class="example">
<pre class="example">ffmpeg -activation_bytes 1CEB00DA -i test.aax -vn -c:a copy output.mp4
</pre></div>

<a name="mpegts"></a>
<h3 class="section">3.16 mpegts<span class="pull-right"><a class="anchor hidden-xs" href="#mpegts" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpegts" aria-hidden="true">TOC</a></span></h3>

<p>MPEG-2 transport stream demuxer.
</p>
<p>This demuxer accepts the following options:
</p><dl compact="compact">
<dt><span><samp>resync_size</samp></span></dt>
<dd><p>Set size limit for looking up a new synchronization. Default value is
65536.
</p>
</dd>
<dt><span><samp>skip_unknown_pmt</samp></span></dt>
<dd><p>Skip PMTs for programs not defined in the PAT. Default value is 0.
</p>
</dd>
<dt><span><samp>fix_teletext_pts</samp></span></dt>
<dd><p>Override teletext packet PTS and DTS values with the timestamps calculated
from the PCR of the first program which the teletext stream is part of and is
not discarded. Default value is 1, set this option to 0 if you want your
teletext packet PTS and DTS values untouched.
</p>
</dd>
<dt><span><samp>ts_packetsize</samp></span></dt>
<dd><p>Output option carrying the raw packet size in bytes.
Show the detected raw packet size, cannot be set by the user.
</p>
</dd>
<dt><span><samp>scan_all_pmts</samp></span></dt>
<dd><p>Scan and combine all PMTs. The value is an integer with value from -1
to 1 (-1 means automatic setting, 1 means enabled, 0 means
disabled). Default value is -1.
</p>
</dd>
<dt><span><samp>merge_pmt_versions</samp></span></dt>
<dd><p>Re-use existing streams when a PMT&rsquo;s version is updated and elementary
streams move to different PIDs. Default value is 0.
</p>
</dd>
<dt><span><samp>max_packet_size</samp></span></dt>
<dd><p>Set maximum size, in bytes, of packet emitted by the demuxer. Payloads above this size
are split across multiple packets. Range is 1 to INT_MAX/2. Default is 204800 bytes.
</p></dd>
</dl>

<a name="mpjpeg"></a>
<h3 class="section">3.17 mpjpeg<span class="pull-right"><a class="anchor hidden-xs" href="#mpjpeg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpjpeg" aria-hidden="true">TOC</a></span></h3>

<p>MJPEG encapsulated in multi-part MIME demuxer.
</p>
<p>This demuxer allows reading of MJPEG, where each frame is represented as a part of
multipart/x-mixed-replace stream.
</p><dl compact="compact">
<dt><span><samp>strict_mime_boundary</samp></span></dt>
<dd><p>Default implementation applies a relaxed standard to multi-part MIME boundary detection,
to prevent regression with numerous existing endpoints not generating a proper MIME
MJPEG stream. Turning this option on by setting it to 1 will result in a stricter check
of the boundary value.
</p></dd>
</dl>

<a name="rawvideo"></a>
<h3 class="section">3.18 rawvideo<span class="pull-right"><a class="anchor hidden-xs" href="#rawvideo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rawvideo" aria-hidden="true">TOC</a></span></h3>

<p>Raw video demuxer.
</p>
<p>This demuxer allows one to read raw video data. Since there is no header
specifying the assumed video parameters, the user must specify them
in order to be able to decode the data correctly.
</p>
<p>This demuxer accepts the following options:
</p><dl compact="compact">
<dt><span><samp>framerate</samp></span></dt>
<dd><p>Set input video frame rate. Default value is 25.
</p>
</dd>
<dt><span><samp>pixel_format</samp></span></dt>
<dd><p>Set the input video pixel format. Default value is <code>yuv420p</code>.
</p>
</dd>
<dt><span><samp>video_size</samp></span></dt>
<dd><p>Set the input video size. This value must be specified explicitly.
</p></dd>
</dl>

<p>For example to read a rawvideo file <samp>input.raw</samp> with
<code>ffplay</code>, assuming a pixel format of <code>rgb24</code>, a video
size of <code>320x240</code>, and a frame rate of 10 images per second, use
the command:
</p><div class="example">
<pre class="example">ffplay -f rawvideo -pixel_format rgb24 -video_size 320x240 -framerate 10 input.raw
</pre></div>

<a name="sbg"></a>
<h3 class="section">3.19 sbg<span class="pull-right"><a class="anchor hidden-xs" href="#sbg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sbg" aria-hidden="true">TOC</a></span></h3>

<p>SBaGen script demuxer.
</p>
<p>This demuxer reads the script language used by SBaGen
<a href="http://uazu.net/sbagen/">http://uazu.net/sbagen/</a> to generate binaural beats sessions. A SBG
script looks like that:
</p><div class="example">
<pre class="example">-SE
a: 300-2.5/3 440+4.5/0
b: 300-2.5/0 440+4.5/3
off: -
NOW      == a
+0:07:00 == b
+0:14:00 == a
+0:21:00 == b
+0:30:00    off
</pre></div>

<p>A SBG script can mix absolute and relative timestamps. If the script uses
either only absolute timestamps (including the script start time) or only
relative ones, then its layout is fixed, and the conversion is
straightforward. On the other hand, if the script mixes both kind of
timestamps, then the <var>NOW</var> reference for relative timestamps will be
taken from the current time of day at the time the script is read, and the
script layout will be frozen according to that reference. That means that if
the script is directly played, the actual times will match the absolute
timestamps up to the sound controller&rsquo;s clock accuracy, but if the user
somehow pauses the playback or seeks, all times will be shifted accordingly.
</p>
<a name="tedcaptions"></a>
<h3 class="section">3.20 tedcaptions<span class="pull-right"><a class="anchor hidden-xs" href="#tedcaptions" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-tedcaptions" aria-hidden="true">TOC</a></span></h3>

<p>JSON captions used for <a href="http://www.ted.com/">TED Talks</a>.
</p>
<p>TED does not provide links to the captions, but they can be guessed from the
page. The file <samp>tools/bookmarklets.html</samp> from the FFmpeg source tree
contains a bookmarklet to expose them.
</p>
<p>This demuxer accepts the following option:
</p><dl compact="compact">
<dt><span><samp>start_time</samp></span></dt>
<dd><p>Set the start time of the TED talk, in milliseconds. The default is 15000
(15s). It is used to sync the captions with the downloadable videos, because
they include a 15s intro.
</p></dd>
</dl>

<p>Example: convert the captions to a format most players understand:
</p><div class="example">
<pre class="example">ffmpeg -i http://www.ted.com/talks/subtitles/id/1/lang/en talk1-en.srt
</pre></div>

<a name="vapoursynth"></a>
<h3 class="section">3.21 vapoursynth<span class="pull-right"><a class="anchor hidden-xs" href="#vapoursynth" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vapoursynth" aria-hidden="true">TOC</a></span></h3>

<p>Vapoursynth wrapper.
</p>
<p>Due to security concerns, Vapoursynth scripts will not
be autodetected so the input format has to be forced. For ff* CLI tools,
add <code>-f vapoursynth</code> before the input <code>-i yourscript.vpy</code>.
</p>
<p>This demuxer accepts the following option:
</p><dl compact="compact">
<dt><span><samp>max_script_size</samp></span></dt>
<dd><p>The demuxer buffers the entire script into memory. Adjust this value to set the maximum buffer size,
which in turn, acts as a ceiling for the size of scripts that can be read.
Default is 1 MiB.
</p></dd>
</dl>

<a name="Muxers"></a>
<h2 class="chapter">4 Muxers<span class="pull-right"><a class="anchor hidden-xs" href="#Muxers" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Muxers" aria-hidden="true">TOC</a></span></h2>

<p>Muxers are configured elements in FFmpeg which allow writing
multimedia streams to a particular type of file.
</p>
<p>When you configure your FFmpeg build, all the supported muxers
are enabled by default. You can list all available muxers using the
configure option <code>--list-muxers</code>.
</p>
<p>You can disable all the muxers with the configure option
<code>--disable-muxers</code> and selectively enable / disable single muxers
with the options <code>--enable-muxer=<var>MUXER</var></code> /
<code>--disable-muxer=<var>MUXER</var></code>.
</p>
<p>The option <code>-muxers</code> of the ff* tools will display the list of
enabled muxers. Use <code>-formats</code> to view a combined list of
enabled demuxers and muxers.
</p>
<p>A description of some of the currently available muxers follows.
</p>
<span id="a64"></span><a name="a64-1"></a>
<h3 class="section">4.1 a64<span class="pull-right"><a class="anchor hidden-xs" href="#a64-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-a64-1" aria-hidden="true">TOC</a></span></h3>

<p>A64 muxer for Commodore 64 video. Accepts a single <code>a64_multi</code> or <code>a64_multi5</code> codec video stream.
</p>
<span id="adts"></span><a name="adts-1"></a>
<h3 class="section">4.2 adts<span class="pull-right"><a class="anchor hidden-xs" href="#adts-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-adts-1" aria-hidden="true">TOC</a></span></h3>

<p>Audio Data Transport Stream muxer. It accepts a single AAC stream.
</p>
<a name="Options-3"></a>
<h4 class="subsection">4.2.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-3" aria-hidden="true">TOC</a></span></h4>

<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>write_id3v2 <var>bool</var></samp></span></dt>
<dd><p>Enable to write ID3v2.4 tags at the start of the stream. Default is disabled.
</p>
</dd>
<dt><span><samp>write_apetag <var>bool</var></samp></span></dt>
<dd><p>Enable to write APE tags at the end of the stream. Default is disabled.
</p>
</dd>
<dt><span><samp>write_mpeg2 <var>bool</var></samp></span></dt>
<dd><p>Enable to set MPEG version bit in the ADTS frame header to 1 which indicates MPEG-2. Default is 0, which indicates MPEG-4.
</p>
</dd>
</dl>

<span id="aiff"></span><a name="aiff-1"></a>
<h3 class="section">4.3 aiff<span class="pull-right"><a class="anchor hidden-xs" href="#aiff-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aiff-1" aria-hidden="true">TOC</a></span></h3>

<p>Audio Interchange File Format muxer.
</p>
<a name="Options-4"></a>
<h4 class="subsection">4.3.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-4" aria-hidden="true">TOC</a></span></h4>

<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>write_id3v2</samp></span></dt>
<dd><p>Enable ID3v2 tags writing when set to 1. Default is 0 (disabled).
</p>
</dd>
<dt><span><samp>id3v2_version</samp></span></dt>
<dd><p>Select ID3v2 version to write. Currently only version 3 and 4 (aka.
ID3v2.3 and ID3v2.4) are supported. The default is version 4.
</p>
</dd>
</dl>

<span id="alp"></span><a name="alp-1"></a>
<h3 class="section">4.4 alp<span class="pull-right"><a class="anchor hidden-xs" href="#alp-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-alp-1" aria-hidden="true">TOC</a></span></h3>

<p>Muxer for audio of High Voltage Software&rsquo;s Lego Racers game. It accepts a single ADPCM_IMA_ALP stream
with no more than 2 channels nor a sample rate greater than 44100 Hz.
</p>
<p>Extensions: tun, pcm
</p>
<a name="Options-5"></a>
<h4 class="subsection">4.4.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-5" aria-hidden="true">TOC</a></span></h4>

<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>type <var>type</var></samp></span></dt>
<dd><p>Set file type.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>tun</samp>&rsquo;</span></dt>
<dd><p>Set file type as music. Must have a sample rate of 22050 Hz.
</p>
</dd>
<dt><span>&lsquo;<samp>pcm</samp>&rsquo;</span></dt>
<dd><p>Set file type as sfx.
</p>
</dd>
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Set file type as per output file extension. <code>.pcm</code> results in type <code>pcm</code> else type <code>tun</code> is set. <var>(default)</var>
</p>
</dd>
</dl>

</dd>
</dl>

<span id="asf"></span><a name="asf-2"></a>
<h3 class="section">4.5 asf<span class="pull-right"><a class="anchor hidden-xs" href="#asf-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-asf-2" aria-hidden="true">TOC</a></span></h3>

<p>Advanced Systems Format muxer.
</p>
<p>Note that Windows Media Audio (wma) and Windows Media Video (wmv) use this
muxer too.
</p>
<a name="Options-6"></a>
<h4 class="subsection">4.5.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-6" aria-hidden="true">TOC</a></span></h4>

<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>packet_size</samp></span></dt>
<dd><p>Set the muxer packet size. By tuning this setting you may reduce data
fragmentation or muxer overhead depending on your source. Default value is
3200, minimum is 100, maximum is 64k.
</p>
</dd>
</dl>

<span id="avi"></span><a name="avi-1"></a>
<h3 class="section">4.6 avi<span class="pull-right"><a class="anchor hidden-xs" href="#avi-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avi-1" aria-hidden="true">TOC</a></span></h3>

<p>Audio Video Interleaved muxer.
</p>
<a name="Options-7"></a>
<h4 class="subsection">4.6.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-7" aria-hidden="true">TOC</a></span></h4>

<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>reserve_index_space</samp></span></dt>
<dd><p>Reserve the specified amount of bytes for the OpenDML master index of each
stream within the file header. By default additional master indexes are
embedded within the data packets if there is no space left in the first master
index and are linked together as a chain of indexes. This index structure can
cause problems for some use cases, e.g. third-party software strictly relying
on the OpenDML index specification or when file seeking is slow. Reserving
enough index space in the file header avoids these problems.
</p>
<p>The required index space depends on the output file size and should be about 16
bytes per gigabyte. When this option is omitted or set to zero the necessary
index space is guessed.
</p>
</dd>
<dt><span><samp>write_channel_mask</samp></span></dt>
<dd><p>Write the channel layout mask into the audio stream header.
</p>
<p>This option is enabled by default. Disabling the channel mask can be useful in
specific scenarios, e.g. when merging multiple audio streams into one for
compatibility with software that only supports a single audio stream in AVI
(see <a data-manual="ffmpeg-filters" href="ffmpeg-filters.html#amerge">(ffmpeg-filters)the &quot;amerge&quot; section in the ffmpeg-filters manual</a>).
</p>
</dd>
<dt><span><samp>flipped_raw_rgb</samp></span></dt>
<dd><p>If set to true, store positive height for raw RGB bitmaps, which indicates
bitmap is stored bottom-up. Note that this option does not flip the bitmap
which has to be done manually beforehand, e.g. by using the vflip filter.
Default is <var>false</var> and indicates bitmap is stored top down.
</p>
</dd>
</dl>

<span id="chromaprint"></span><a name="chromaprint-1"></a>
<h3 class="section">4.7 chromaprint<span class="pull-right"><a class="anchor hidden-xs" href="#chromaprint-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-chromaprint-1" aria-hidden="true">TOC</a></span></h3>

<p>Chromaprint fingerprinter.
</p>
<p>This muxer feeds audio data to the Chromaprint library,
which generates a fingerprint for the provided audio data. See <a href="https://acoustid.org/chromaprint">https://acoustid.org/chromaprint</a>
</p>
<p>It takes a single signed native-endian 16-bit raw audio stream of at most 2 channels.
</p>
<a name="Options-8"></a>
<h4 class="subsection">4.7.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-8" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>silence_threshold</samp></span></dt>
<dd><p>Threshold for detecting silence. Range is from -1 to 32767, where -1 disables
silence detection. Silence detection can only be used with version 3 of the
algorithm.
Silence detection must be disabled for use with the AcoustID service. Default is -1.
</p>
</dd>
<dt><span><samp>algorithm</samp></span></dt>
<dd><p>Version of algorithm to fingerprint with. Range is 0 to 4.
Version 3 enables silence detection. Default is 1.
</p>
</dd>
<dt><span><samp>fp_format</samp></span></dt>
<dd><p>Format to output the fingerprint as. Accepts the following options:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>raw</samp>&rsquo;</span></dt>
<dd><p>Binary raw fingerprint
</p>
</dd>
<dt><span>&lsquo;<samp>compressed</samp>&rsquo;</span></dt>
<dd><p>Binary compressed fingerprint
</p>
</dd>
<dt><span>&lsquo;<samp>base64</samp>&rsquo;</span></dt>
<dd><p>Base64 compressed fingerprint <em>(default)</em>
</p>
</dd>
</dl>

</dd>
</dl>

<span id="crc"></span><a name="crc-1"></a>
<h3 class="section">4.8 crc<span class="pull-right"><a class="anchor hidden-xs" href="#crc-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-crc-1" aria-hidden="true">TOC</a></span></h3>

<p>CRC (Cyclic Redundancy Check) testing format.
</p>
<p>This muxer computes and prints the Adler-32 CRC of all the input audio
and video frames. By default audio frames are converted to signed
16-bit raw audio and video frames to raw video before computing the
CRC.
</p>
<p>The output of the muxer consists of a single line of the form:
CRC=0x<var>CRC</var>, where <var>CRC</var> is a hexadecimal number 0-padded to
8 digits containing the CRC for all the decoded input frames.
</p>
<p>See also the <a href="#framecrc">framecrc</a> muxer.
</p>
<a name="Examples-2"></a>
<h4 class="subsection">4.8.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-2" aria-hidden="true">TOC</a></span></h4>

<p>For example to compute the CRC of the input, and store it in the file
<samp>out.crc</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f crc out.crc
</pre></div>

<p>You can print the CRC to stdout with the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f crc -
</pre></div>

<p>You can select the output format of each frame with <code>ffmpeg</code> by
specifying the audio and video codec and format. For example to
compute the CRC of the input audio converted to PCM unsigned 8-bit
and the input video converted to MPEG-2 video, use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:a pcm_u8 -c:v mpeg2video -f crc -
</pre></div>

<span id="dash"></span><a name="dash-2"></a>
<h3 class="section">4.9 dash<span class="pull-right"><a class="anchor hidden-xs" href="#dash-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dash-2" aria-hidden="true">TOC</a></span></h3>

<p>Dynamic Adaptive Streaming over HTTP (DASH) muxer that creates segments
and manifest files according to the MPEG-DASH standard ISO/IEC 23009-1:2014.
</p>
<p>For more information see:
</p>
<ul>
<li> ISO DASH Specification: <a href="http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip">http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip</a>
</li><li> WebM DASH Specification: <a href="https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification">https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification</a>
</li></ul>

<p>It creates a MPD manifest file and segment files for each stream.
</p>
<p>The segment filename might contain pre-defined identifiers used with SegmentTemplate
as defined in section *******.4 of the standard. Available identifiers are &quot;$RepresentationID$&quot;,
&quot;$Number$&quot;, &quot;$Bandwidth$&quot; and &quot;$Time$&quot;.
In addition to the standard identifiers, an ffmpeg-specific &quot;$ext$&quot; identifier is also supported.
When specified ffmpeg will replace $ext$ in the file name with muxing format&rsquo;s extensions such as mp4, webm etc.,
</p>
<div class="example">
<pre class="example">ffmpeg -re -i &lt;input&gt; -map 0 -map 0 -c:a libfdk_aac -c:v libx264 \
-b:v:0 800k -b:v:1 300k -s:v:1 320x170 -profile:v:1 baseline \
-profile:v:0 main -bf 1 -keyint_min 120 -g 120 -sc_threshold 0 \
-b_strategy 0 -ar:a:1 22050 -use_timeline 1 -use_template 1 \
-window_size 5 -adaptation_sets &quot;id=0,streams=v id=1,streams=a&quot; \
-f dash /path/to/out.mpd
</pre></div>

<dl compact="compact">
<dt><span><samp>seg_duration <var>duration</var></samp></span></dt>
<dd><p>Set the segment length in seconds (fractional value can be set). The value is
treated as average segment duration when <var>use_template</var> is enabled and
<var>use_timeline</var> is disabled and as minimum segment duration for all the other
use cases.
</p></dd>
<dt><span><samp>frag_duration <var>duration</var></samp></span></dt>
<dd><p>Set the length in seconds of fragments within segments (fractional value can be set).
</p></dd>
<dt><span><samp>frag_type <var>type</var></samp></span></dt>
<dd><p>Set the type of interval for fragmentation.
</p></dd>
<dt><span><samp>window_size <var>size</var></samp></span></dt>
<dd><p>Set the maximum number of segments kept in the manifest.
</p></dd>
<dt><span><samp>extra_window_size <var>size</var></samp></span></dt>
<dd><p>Set the maximum number of segments kept outside of the manifest before removing from disk.
</p></dd>
<dt><span><samp>remove_at_exit <var>remove</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) removal of all segments when finished.
</p></dd>
<dt><span><samp>use_template <var>template</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) use of SegmentTemplate instead of SegmentList.
</p></dd>
<dt><span><samp>use_timeline <var>timeline</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) use of SegmentTimeline in SegmentTemplate.
</p></dd>
<dt><span><samp>single_file <var>single_file</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) storing all segments in one file, accessed using byte ranges.
</p></dd>
<dt><span><samp>single_file_name <var>file_name</var></samp></span></dt>
<dd><p>DASH-templated name to be used for baseURL. Implies <var>single_file</var> set to &quot;1&quot;. In the template, &quot;$ext$&quot; is replaced with the file name extension specific for the segment format.
</p></dd>
<dt><span><samp>init_seg_name <var>init_name</var></samp></span></dt>
<dd><p>DASH-templated name to used for the initialization segment. Default is &quot;init-stream$RepresentationID$.$ext$&quot;. &quot;$ext$&quot; is replaced with the file name extension specific for the segment format.
</p></dd>
<dt><span><samp>media_seg_name <var>segment_name</var></samp></span></dt>
<dd><p>DASH-templated name to used for the media segments. Default is &quot;chunk-stream$RepresentationID$-$Number%05d$.$ext$&quot;. &quot;$ext$&quot; is replaced with the file name extension specific for the segment format.
</p></dd>
<dt><span><samp>utc_timing_url <var>utc_url</var></samp></span></dt>
<dd><p>URL of the page that will return the UTC timestamp in ISO format. Example: &quot;https://time.akamai.com/?iso&quot;
</p></dd>
<dt><span><samp>method <var>method</var></samp></span></dt>
<dd><p>Use the given HTTP method to create output files. Generally set to PUT or POST.
</p></dd>
<dt><span><samp>http_user_agent <var>user_agent</var></samp></span></dt>
<dd><p>Override User-Agent field in HTTP header. Applicable only for HTTP output.
</p></dd>
<dt><span><samp>http_persistent <var>http_persistent</var></samp></span></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP output.
</p></dd>
<dt><span><samp>hls_playlist <var>hls_playlist</var></samp></span></dt>
<dd><p>Generate HLS playlist files as well. The master playlist is generated with the filename <var>hls_master_name</var>.
One media playlist file is generated for each stream with filenames media_0.m3u8, media_1.m3u8, etc.
</p></dd>
<dt><span><samp>hls_master_name <var>file_name</var></samp></span></dt>
<dd><p>HLS master playlist name. Default is &quot;master.m3u8&quot;.
</p></dd>
<dt><span><samp>streaming <var>streaming</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) chunk streaming mode of output. In chunk streaming
mode, each frame will be a moof fragment which forms a chunk.
</p></dd>
<dt><span><samp>adaptation_sets <var>adaptation_sets</var></samp></span></dt>
<dd><p>Assign streams to AdaptationSets. Syntax is &quot;id=x,streams=a,b,c id=y,streams=d,e&quot; with x and y being the IDs
of the adaptation sets and a,b,c,d and e are the indices of the mapped streams.
</p>
<p>To map all video (or audio) streams to an AdaptationSet, &quot;v&quot; (or &quot;a&quot;) can be used as stream identifier instead of IDs.
</p>
<p>When no assignment is defined, this defaults to an AdaptationSet for each stream.
</p>
<p>Optional syntax is &quot;id=x,seg_duration=x,frag_duration=x,frag_type=type,descriptor=descriptor_string,streams=a,b,c id=y,seg_duration=y,frag_type=type,streams=d,e&quot; and so on,
descriptor is useful to the scheme defined by ISO/IEC 23009-1:2014/Amd.2:2015.
For example, -adaptation_sets &quot;id=0,descriptor=&lt;SupplementalProperty schemeIdUri=\&quot;urn:mpeg:dash:srd:2014\&quot; value=\&quot;0,0,0,1,1,2,2\&quot;/&gt;,streams=v&quot;.
Please note that descriptor string should be a self-closing xml tag.
seg_duration, frag_duration and frag_type override the global option values for each adaptation set.
For example, -adaptation_sets &quot;id=0,seg_duration=2,frag_duration=1,frag_type=duration,streams=v id=1,seg_duration=2,frag_type=none,streams=a&quot;
type_id marks an adaptation set as containing streams meant to be used for Trick Mode for the referenced adaptation set.
For example, -adaptation_sets &quot;id=0,seg_duration=2,frag_type=none,streams=0 id=1,seg_duration=10,frag_type=none,trick_id=0,streams=1&quot;
</p></dd>
<dt><span><samp>timeout <var>timeout</var></samp></span></dt>
<dd><p>Set timeout for socket I/O operations. Applicable only for HTTP output.
</p></dd>
<dt><span><samp>index_correction <var>index_correction</var></samp></span></dt>
<dd><p>Enable (1) or Disable (0) segment index correction logic. Applicable only when
<var>use_template</var> is enabled and <var>use_timeline</var> is disabled.
</p>
<p>When enabled, the logic monitors the flow of segment indexes. If a streams&rsquo;s
segment index value is not at the expected real time position, then the logic
corrects that index value.
</p>
<p>Typically this logic is needed in live streaming use cases. The network bandwidth
fluctuations are common during long run streaming. Each fluctuation can cause
the segment indexes fall behind the expected real time position.
</p></dd>
<dt><span><samp>format_options <var>options_list</var></samp></span></dt>
<dd><p>Set container format (mp4/webm) options using a <code>:</code> separated list of
key=value parameters. Values containing <code>:</code> special characters must be
escaped.
</p>
</dd>
<dt><span><samp>global_sidx <var>global_sidx</var></samp></span></dt>
<dd><p>Write global SIDX atom. Applicable only for single file, mp4 output, non-streaming mode.
</p>
</dd>
<dt><span><samp>dash_segment_type <var>dash_segment_type</var></samp></span></dt>
<dd><p>Possible values:
</p><dl compact="compact">
<dt><span><samp>auto</samp></span></dt>
<dd><p>If this flag is set, the dash segment files format will be selected based on the stream codec. This is the default mode.
</p>
</dd>
<dt><span><samp>mp4</samp></span></dt>
<dd><p>If this flag is set, the dash segment files will be in in ISOBMFF format.
</p>
</dd>
<dt><span><samp>webm</samp></span></dt>
<dd><p>If this flag is set, the dash segment files will be in in WebM format.
</p></dd>
</dl>

</dd>
<dt><span><samp>ignore_io_errors <var>ignore_io_errors</var></samp></span></dt>
<dd><p>Ignore IO errors during open and write. Useful for long-duration runs with network output.
</p>
</dd>
<dt><span><samp>lhls <var>lhls</var></samp></span></dt>
<dd><p>Enable Low-latency HLS(LHLS). Adds #EXT-X-PREFETCH tag with current segment&rsquo;s URI.
hls.js player folks are trying to standardize an open LHLS spec. The draft spec is available in https://github.com/video-dev/hlsjs-rfcs/blob/lhls-spec/proposals/0001-lhls.md
This option tries to comply with the above open spec.
It enables <var>streaming</var> and <var>hls_playlist</var> options automatically.
This is an experimental feature.
</p>
<p>Note: This is not Apple&rsquo;s version LHLS. See <a href="https://datatracker.ietf.org/doc/html/draft-pantos-hls-rfc8216bis">https://datatracker.ietf.org/doc/html/draft-pantos-hls-rfc8216bis</a>
</p>
</dd>
<dt><span><samp>ldash <var>ldash</var></samp></span></dt>
<dd><p>Enable Low-latency Dash by constraining the presence and values of some elements.
</p>
</dd>
<dt><span><samp>master_m3u8_publish_rate <var>master_m3u8_publish_rate</var></samp></span></dt>
<dd><p>Publish master playlist repeatedly every after specified number of segment intervals.
</p>
</dd>
<dt><span><samp>write_prft <var>write_prft</var></samp></span></dt>
<dd><p>Write Producer Reference Time elements on supported streams. This also enables writing
prft boxes in the underlying muxer. Applicable only when the <var>utc_url</var> option is enabled.
It&rsquo;s set to auto by default, in which case the muxer will attempt to enable it only in modes
that require it.
</p>
</dd>
<dt><span><samp>mpd_profile <var>mpd_profile</var></samp></span></dt>
<dd><p>Set one or more manifest profiles.
</p>
</dd>
<dt><span><samp>http_opts <var>http_opts</var></samp></span></dt>
<dd><p>A :-separated list of key=value options to pass to the underlying HTTP
protocol. Applicable only for HTTP output.
</p>
</dd>
<dt><span><samp>target_latency <var>target_latency</var></samp></span></dt>
<dd><p>Set an intended target latency in seconds (fractional value can be set) for serving. Applicable only when <var>streaming</var> and <var>write_prft</var> options are enabled.
This is an informative fields clients can use to measure the latency of the service.
</p>
</dd>
<dt><span><samp>min_playback_rate <var>min_playback_rate</var></samp></span></dt>
<dd><p>Set the minimum playback rate indicated as appropriate for the purposes of automatically
adjusting playback latency and buffer occupancy during normal playback by clients.
</p>
</dd>
<dt><span><samp>max_playback_rate <var>max_playback_rate</var></samp></span></dt>
<dd><p>Set the maximum playback rate indicated as appropriate for the purposes of automatically
adjusting playback latency and buffer occupancy during normal playback by clients.
</p>
</dd>
<dt><span><samp>update_period <var>update_period</var></samp></span></dt>
<dd><p>Set the mpd update period ,for dynamic content.
 The unit is second.
</p>
</dd>
</dl>

<span id="fifo"></span><a name="fifo-1"></a>
<h3 class="section">4.10 fifo<span class="pull-right"><a class="anchor hidden-xs" href="#fifo-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-fifo-1" aria-hidden="true">TOC</a></span></h3>

<p>The fifo pseudo-muxer allows the separation of encoding and muxing by using
first-in-first-out queue and running the actual muxer in a separate thread. This
is especially useful in combination with the <a href="#tee">tee</a> muxer and can be used to
send data to several destinations with different reliability/writing speed/latency.
</p>
<p>API users should be aware that callback functions (interrupt_callback,
io_open and io_close) used within its AVFormatContext must be thread-safe.
</p>
<p>The behavior of the fifo muxer if the queue fills up or if the output fails is
selectable,
</p>
<ul>
<li> output can be transparently restarted with configurable delay between retries
based on real time or time of the processed stream.

</li><li> encoding can be blocked during temporary failure, or continue transparently
dropping packets in case fifo queue fills up.

</li></ul>

<dl compact="compact">
<dt><span><samp>fifo_format</samp></span></dt>
<dd><p>Specify the format name. Useful if it cannot be guessed from the
output name suffix.
</p>
</dd>
<dt><span><samp>queue_size</samp></span></dt>
<dd><p>Specify size of the queue (number of packets). Default value is 60.
</p>
</dd>
<dt><span><samp>format_opts</samp></span></dt>
<dd><p>Specify format options for the underlying muxer. Muxer options can be specified
as a list of <var>key</var>=<var>value</var> pairs separated by &rsquo;:&rsquo;.
</p>
</dd>
<dt><span><samp>drop_pkts_on_overflow <var>bool</var></samp></span></dt>
<dd><p>If set to 1 (true), in case the fifo queue fills up, packets will be dropped
rather than blocking the encoder. This makes it possible to continue streaming without
delaying the input, at the cost of omitting part of the stream. By default
this option is set to 0 (false), so in such cases the encoder will be blocked
until the muxer processes some of the packets and none of them is lost.
</p>
</dd>
<dt><span><samp>attempt_recovery <var>bool</var></samp></span></dt>
<dd><p>If failure occurs, attempt to recover the output. This is especially useful
when used with network output, since it makes it possible to restart streaming transparently.
By default this option is set to 0 (false).
</p>
</dd>
<dt><span><samp>max_recovery_attempts</samp></span></dt>
<dd><p>Sets maximum number of successive unsuccessful recovery attempts after which
the output fails permanently. By default this option is set to 0 (unlimited).
</p>
</dd>
<dt><span><samp>recovery_wait_time <var>duration</var></samp></span></dt>
<dd><p>Waiting time before the next recovery attempt after previous unsuccessful
recovery attempt. Default value is 5 seconds.
</p>
</dd>
<dt><span><samp>recovery_wait_streamtime <var>bool</var></samp></span></dt>
<dd><p>If set to 0 (false), the real time is used when waiting for the recovery
attempt (i.e. the recovery will be attempted after at least
recovery_wait_time seconds).
If set to 1 (true), the time of the processed stream is taken into account
instead (i.e. the recovery will be attempted after at least <var>recovery_wait_time</var>
seconds of the stream is omitted).
By default, this option is set to 0 (false).
</p>
</dd>
<dt><span><samp>recover_any_error <var>bool</var></samp></span></dt>
<dd><p>If set to 1 (true), recovery will be attempted regardless of type of the error
causing the failure. By default this option is set to 0 (false) and in case of
certain (usually permanent) errors the recovery is not attempted even when
<var>attempt_recovery</var> is set to 1.
</p>
</dd>
<dt><span><samp>restart_with_keyframe <var>bool</var></samp></span></dt>
<dd><p>Specify whether to wait for the keyframe after recovering from
queue overflow or failure. This option is set to 0 (false) by default.
</p>
</dd>
<dt><span><samp>timeshift <var>duration</var></samp></span></dt>
<dd><p>Buffer the specified amount of packets and delay writing the output. Note that
<var>queue_size</var> must be big enough to store the packets for timeshift. At the
end of the input the fifo buffer is flushed at realtime speed.
</p>
</dd>
</dl>

<a name="Examples-3"></a>
<h4 class="subsection">4.10.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-3" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Stream something to rtmp server, continue processing the stream at real-time
rate even in case of temporary failure (network outage) and attempt to recover
streaming every second indefinitely.
<div class="example">
<pre class="example">ffmpeg -re -i ... -c:v libx264 -c:a aac -f fifo -fifo_format flv -map 0:v -map 0:a
  -drop_pkts_on_overflow 1 -attempt_recovery 1 -recovery_wait_time 1 rtmp://example.com/live/stream_name
</pre></div>

</li></ul>

<a name="flv"></a>
<h3 class="section">4.11 flv<span class="pull-right"><a class="anchor hidden-xs" href="#flv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-flv" aria-hidden="true">TOC</a></span></h3>

<p>Adobe Flash Video Format muxer.
</p>
<p>This muxer accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>flvflags <var>flags</var></samp></span></dt>
<dd><p>Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>aac_seq_header_detect</samp>&rsquo;</span></dt>
<dd><p>Place AAC sequence header based on audio stream data.
</p>
</dd>
<dt><span>&lsquo;<samp>no_sequence_end</samp>&rsquo;</span></dt>
<dd><p>Disable sequence end tag.
</p>
</dd>
<dt><span>&lsquo;<samp>no_metadata</samp>&rsquo;</span></dt>
<dd><p>Disable metadata tag.
</p>
</dd>
<dt><span>&lsquo;<samp>no_duration_filesize</samp>&rsquo;</span></dt>
<dd><p>Disable duration and filesize in metadata when they are equal to zero
at the end of stream. (Be used to non-seekable living stream).
</p>
</dd>
<dt><span>&lsquo;<samp>add_keyframe_index</samp>&rsquo;</span></dt>
<dd><p>Used to facilitate seeking; particularly for HTTP pseudo streaming.
</p></dd>
</dl>
</dd>
</dl>

<span id="framecrc"></span><a name="framecrc-1"></a>
<h3 class="section">4.12 framecrc<span class="pull-right"><a class="anchor hidden-xs" href="#framecrc-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framecrc-1" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet CRC (Cyclic Redundancy Check) testing format.
</p>
<p>This muxer computes and prints the Adler-32 CRC for each audio
and video packet. By default audio frames are converted to signed
16-bit raw audio and video frames to raw video before computing the
CRC.
</p>
<p>The output of the muxer consists of a line for each audio and video
packet of the form:
</p><div class="example">
<pre class="example"><var>stream_index</var>, <var>packet_dts</var>, <var>packet_pts</var>, <var>packet_duration</var>, <var>packet_size</var>, 0x<var>CRC</var>
</pre></div>

<p><var>CRC</var> is a hexadecimal number 0-padded to 8 digits containing the
CRC of the packet.
</p>
<a name="Examples-4"></a>
<h4 class="subsection">4.12.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-4" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-4" aria-hidden="true">TOC</a></span></h4>

<p>For example to compute the CRC of the audio and video frames in
<samp>INPUT</samp>, converted to raw audio and video packets, and store it
in the file <samp>out.crc</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framecrc out.crc
</pre></div>

<p>To print the information to stdout, use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framecrc -
</pre></div>

<p>With <code>ffmpeg</code>, you can select the output format to which the
audio and video frames are encoded before computing the CRC for each
packet by specifying the audio and video codec. For example, to
compute the CRC of each decoded input audio frame converted to PCM
unsigned 8-bit and of each decoded input video frame converted to
MPEG-2 video, use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:a pcm_u8 -c:v mpeg2video -f framecrc -
</pre></div>

<p>See also the <a href="#crc">crc</a> muxer.
</p>
<span id="framehash"></span><a name="framehash-1"></a>
<h3 class="section">4.13 framehash<span class="pull-right"><a class="anchor hidden-xs" href="#framehash-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framehash-1" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash for each audio
and video packet. This can be used for packet-by-packet equality
checks without having to individually do a binary comparison on each.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. It uses the
SHA-256 cryptographic hash function by default, but supports several
other algorithms.
</p>
<p>The output of the muxer consists of a line for each audio and video
packet of the form:
</p><div class="example">
<pre class="example"><var>stream_index</var>, <var>packet_dts</var>, <var>packet_pts</var>, <var>packet_duration</var>, <var>packet_size</var>, <var>hash</var>
</pre></div>

<p><var>hash</var> is a hexadecimal number representing the computed hash
for the packet.
</p>
<dl compact="compact">
<dt><span><samp>hash <var>algorithm</var></samp></span></dt>
<dd><p>Use the cryptographic hash function specified by the string <var>algorithm</var>.
Supported values include <code>MD5</code>, <code>murmur3</code>, <code>RIPEMD128</code>,
<code>RIPEMD160</code>, <code>RIPEMD256</code>, <code>RIPEMD320</code>, <code>SHA160</code>,
<code>SHA224</code>, <code>SHA256</code> (default), <code>SHA512/224</code>, <code>SHA512/256</code>,
<code>SHA384</code>, <code>SHA512</code>, <code>CRC32</code> and <code>adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-5"></a>
<h4 class="subsection">4.13.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-5" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-5" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the audio and video frames in <samp>INPUT</samp>,
converted to raw audio and video packets, and store it in the file
<samp>out.sha256</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framehash out.sha256
</pre></div>

<p>To print the information to stdout, using the MD5 hash function, use
the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framehash -hash md5 -
</pre></div>

<p>See also the <a href="#hash">hash</a> muxer.
</p>
<span id="framemd5"></span><a name="framemd5-1"></a>
<h3 class="section">4.14 framemd5<span class="pull-right"><a class="anchor hidden-xs" href="#framemd5-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-framemd5-1" aria-hidden="true">TOC</a></span></h3>

<p>Per-packet MD5 testing format.
</p>
<p>This is a variant of the <a href="#framehash">framehash</a> muxer. Unlike that muxer,
it defaults to using the MD5 hash function.
</p>
<a name="Examples-6"></a>
<h4 class="subsection">4.14.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-6" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-6" aria-hidden="true">TOC</a></span></h4>

<p>To compute the MD5 hash of the audio and video frames in <samp>INPUT</samp>,
converted to raw audio and video packets, and store it in the file
<samp>out.md5</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framemd5 out.md5
</pre></div>

<p>To print the information to stdout, use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f framemd5 -
</pre></div>

<p>See also the <a href="#framehash">framehash</a> and <a href="#md5">md5</a> muxers.
</p>
<span id="gif"></span><a name="gif-2"></a>
<h3 class="section">4.15 gif<span class="pull-right"><a class="anchor hidden-xs" href="#gif-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gif-2" aria-hidden="true">TOC</a></span></h3>

<p>Animated GIF muxer.
</p>
<p>It accepts the following options:
</p>
<dl compact="compact">
<dt><span><samp>loop</samp></span></dt>
<dd><p>Set the number of times to loop the output. Use <code>-1</code> for no loop, <code>0</code>
for looping indefinitely (default).
</p>
</dd>
<dt><span><samp>final_delay</samp></span></dt>
<dd><p>Force the delay (expressed in centiseconds) after the last frame. Each frame
ends with a delay until the next frame. The default is <code>-1</code>, which is a
special value to tell the muxer to re-use the previous delay. In case of a
loop, you might want to customize this value to mark a pause for instance.
</p></dd>
</dl>

<p>For example, to encode a gif looping 10 times, with a 5 seconds delay between
the loops:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -loop 10 -final_delay 500 out.gif
</pre></div>

<p>Note 1: if you wish to extract the frames into separate GIF files, you need to
force the <a href="#image2">image2</a> muxer:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:v gif -f image2 &quot;out%d.gif&quot;
</pre></div>

<p>Note 2: the GIF format has a very large time base: the delay between two frames
can therefore not be smaller than one centi second.
</p>
<span id="hash"></span><a name="hash-1"></a>
<h3 class="section">4.16 hash<span class="pull-right"><a class="anchor hidden-xs" href="#hash-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hash-1" aria-hidden="true">TOC</a></span></h3>

<p>Hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash of all the input
audio and video frames. This can be used for equality checks without
having to do a complete binary comparison.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA-256 cryptographic hash function by default,
but supports several other algorithms.
</p>
<p>The output of the muxer consists of a single line of the form:
<var>algo</var>=<var>hash</var>, where <var>algo</var> is a short string representing
the hash function used, and <var>hash</var> is a hexadecimal number
representing the computed hash.
</p>
<dl compact="compact">
<dt><span><samp>hash <var>algorithm</var></samp></span></dt>
<dd><p>Use the cryptographic hash function specified by the string <var>algorithm</var>.
Supported values include <code>MD5</code>, <code>murmur3</code>, <code>RIPEMD128</code>,
<code>RIPEMD160</code>, <code>RIPEMD256</code>, <code>RIPEMD320</code>, <code>SHA160</code>,
<code>SHA224</code>, <code>SHA256</code> (default), <code>SHA512/224</code>, <code>SHA512/256</code>,
<code>SHA384</code>, <code>SHA512</code>, <code>CRC32</code> and <code>adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-7"></a>
<h4 class="subsection">4.16.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-7" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-7" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the input converted to raw audio and
video, and store it in the file <samp>out.sha256</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f hash out.sha256
</pre></div>

<p>To print an MD5 hash to stdout use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f hash -hash md5 -
</pre></div>

<p>See also the <a href="#framehash">framehash</a> muxer.
</p>
<span id="hls"></span><a name="hls-2"></a>
<h3 class="section">4.17 hls<span class="pull-right"><a class="anchor hidden-xs" href="#hls-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hls-2" aria-hidden="true">TOC</a></span></h3>

<p>Apple HTTP Live Streaming muxer that segments MPEG-TS according to
the HTTP Live Streaming (HLS) specification.
</p>
<p>It creates a playlist file, and one or more segment files. The output filename
specifies the playlist filename.
</p>
<p>By default, the muxer creates a file for each segment produced. These files
have the same name as the playlist, followed by a sequential number and a
.ts extension.
</p>
<p>Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
</p>
<p>For example, to convert an input file with <code>ffmpeg</code>:
</p><div class="example">
<pre class="example">ffmpeg -i in.mkv -c:v h264 -flags +cgop -g 30 -hls_time 1 out.m3u8
</pre></div>
<p>This example will produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>out0.ts</samp>, <samp>out1.ts</samp>, <samp>out2.ts</samp>, etc.
</p>
<p>See also the <a href="#segment">segment</a> muxer, which provides a more generic and
flexible implementation of a segmenter, and can be used to perform HLS
segmentation.
</p>
<a name="Options-9"></a>
<h4 class="subsection">4.17.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-9" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>hls_init_time <var>duration</var></samp></span></dt>
<dd><p>Set the initial target segment length. Default value is <var>0</var>.
</p>
<p><var>duration</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#time-duration-syntax">(ffmpeg-utils)the Time duration section in the ffmpeg-utils(1) manual</a>.
</p>
<p>Segment will be cut on the next key frame after this time has passed on the first m3u8 list.
After the initial playlist is filled <code>ffmpeg</code> will cut segments
at duration equal to <code>hls_time</code>
</p>
</dd>
<dt><span><samp>hls_time <var>duration</var></samp></span></dt>
<dd><p>Set the target segment length. Default value is 2.
</p>
<p><var>duration</var> must be a time duration specification,
see <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#time-duration-syntax">(ffmpeg-utils)the Time duration section in the ffmpeg-utils(1) manual</a>.
Segment will be cut on the next key frame after this time has passed.
</p>
</dd>
<dt><span><samp>hls_list_size <var>size</var></samp></span></dt>
<dd><p>Set the maximum number of playlist entries. If set to 0 the list file
will contain all the segments. Default value is 5.
</p>
</dd>
<dt><span><samp>hls_delete_threshold <var>size</var></samp></span></dt>
<dd><p>Set the number of unreferenced segments to keep on disk before <code>hls_flags delete_segments</code>
deletes them. Increase this to allow continue clients to download segments which
were recently referenced in the playlist. Default value is 1, meaning segments older than
<code>hls_list_size+1</code> will be deleted.
</p>
</dd>
<dt><span><samp>hls_ts_options <var>options_list</var></samp></span></dt>
<dd><p>Set output format options using a :-separated list of key=value
parameters. Values containing <code>:</code> special characters must be
escaped.
<code>hls_ts_options</code> is deprecated, use hls_segment_options instead of it..
</p>
</dd>
<dt><span><samp>hls_start_number_source</samp></span></dt>
<dd><p>Start the playlist sequence number (<code>#EXT-X-MEDIA-SEQUENCE</code>) according to the specified source.
Unless <code>hls_flags single_file</code> is set, it also specifies source of starting sequence numbers of
segment and subtitle filenames. In any case, if <code>hls_flags append_list</code>
is set and read playlist sequence number is greater than the specified start sequence number,
then that value will be used as start value.
</p>
<p>It accepts the following values:
</p>
<dl compact="compact">
<dt><span><samp>generic (default)</samp></span></dt>
<dd><p>Set the starting sequence numbers according to <var>start_number</var> option value.
</p>
</dd>
<dt><span><samp>epoch</samp></span></dt>
<dd><p>The start number will be the seconds since epoch (1970-01-01 00:00:00)
</p>
</dd>
<dt><span><samp>epoch_us</samp></span></dt>
<dd><p>The start number will be the microseconds since epoch (1970-01-01 00:00:00)
</p>
</dd>
<dt><span><samp>datetime</samp></span></dt>
<dd><p>The start number will be based on the current date/time as YYYYmmddHHMMSS. e.g. 20161231235759.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>start_number <var>number</var></samp></span></dt>
<dd><p>Start the playlist sequence number (<code>#EXT-X-MEDIA-SEQUENCE</code>) from the specified <var>number</var>
when <var>hls_start_number_source</var> value is <var>generic</var>. (This is the default case.)
Unless <code>hls_flags single_file</code> is set, it also specifies starting sequence numbers of segment and subtitle filenames.
Default value is 0.
</p>
</dd>
<dt><span><samp>hls_allow_cache <var>allowcache</var></samp></span></dt>
<dd><p>Explicitly set whether the client MAY (1) or MUST NOT (0) cache media segments.
</p>
</dd>
<dt><span><samp>hls_base_url <var>baseurl</var></samp></span></dt>
<dd><p>Append <var>baseurl</var> to every entry in the playlist.
Useful to generate playlists with absolute paths.
</p>
<p>Note that the playlist sequence number must be unique for each segment
and it is not to be confused with the segment filename sequence number
which can be cyclic, for example if the <samp>wrap</samp> option is
specified.
</p>
</dd>
<dt><span><samp>hls_segment_filename <var>filename</var></samp></span></dt>
<dd><p>Set the segment filename. Unless <code>hls_flags single_file</code> is set,
<var>filename</var> is used as a string format with the segment number:
</p><div class="example">
<pre class="example">ffmpeg -i in.nut -hls_segment_filename 'file%03d.ts' out.m3u8
</pre></div>
<p>This example will produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>file000.ts</samp>, <samp>file001.ts</samp>, <samp>file002.ts</samp>, etc.
</p>
<p><var>filename</var> may contain full path or relative path specification,
but only the file name part without any path info will be contained in the m3u8 segment list.
Should a relative path be specified, the path of the created segment
files will be relative to the current working directory.
When strftime_mkdir is set, the whole expanded value of <var>filename</var> will be written into the m3u8 segment list.
</p>
<p>When <code>var_stream_map</code> is set with two or more variant streams, the
<var>filename</var> pattern must contain the string &quot;%v&quot;, this string specifies
the position of variant stream index in the generated segment file names.
</p><div class="example">
<pre class="example">ffmpeg -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  -hls_segment_filename 'file_%v_%03d.ts' out_%v.m3u8
</pre></div>
<p>This example will produce the playlists segment file sets:
<samp>file_0_000.ts</samp>, <samp>file_0_001.ts</samp>, <samp>file_0_002.ts</samp>, etc. and
<samp>file_1_000.ts</samp>, <samp>file_1_001.ts</samp>, <samp>file_1_002.ts</samp>, etc.
</p>
<p>The string &quot;%v&quot; may be present in the filename or in the last directory name
containing the file, but only in one of them. (Additionally, %v may appear multiple times in the last
sub-directory or filename.) If the string %v is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of segments corresponding to different variant streams in
subdirectories.
</p><div class="example">
<pre class="example">ffmpeg -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  -hls_segment_filename 'vs%v/file_%03d.ts' vs%v/out.m3u8
</pre></div>
<p>This example will produce the playlists segment file sets:
<samp>vs0/file_000.ts</samp>, <samp>vs0/file_001.ts</samp>, <samp>vs0/file_002.ts</samp>, etc. and
<samp>vs1/file_000.ts</samp>, <samp>vs1/file_001.ts</samp>, <samp>vs1/file_002.ts</samp>, etc.
</p>
</dd>
<dt><span><samp>strftime</samp></span></dt>
<dd><p>Use strftime() on <var>filename</var> to expand the segment filename with localtime.
The segment number is also available in this mode, but to use it, you need to specify second_level_segment_index
hls_flag and %%d will be the specifier.
</p><div class="example">
<pre class="example">ffmpeg -i in.nut -strftime 1 -hls_segment_filename 'file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>This example will produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>file-20160215-1455569023.ts</samp>, <samp>file-20160215-1455569024.ts</samp>, etc.
Note: On some systems/environments, the <code>%s</code> specifier is not available. See
  <code>strftime()</code> documentation.
</p><div class="example">
<pre class="example">ffmpeg -i in.nut -strftime 1 -hls_flags second_level_segment_index -hls_segment_filename 'file-%Y%m%d-%%04d.ts' out.m3u8
</pre></div>
<p>This example will produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>file-20160215-0001.ts</samp>, <samp>file-20160215-0002.ts</samp>, etc.
</p>
</dd>
<dt><span><samp>strftime_mkdir</samp></span></dt>
<dd><p>Used together with -strftime_mkdir, it will create all subdirectories which
is expanded in <var>filename</var>.
</p><div class="example">
<pre class="example">ffmpeg -i in.nut -strftime 1 -strftime_mkdir 1 -hls_segment_filename '%Y%m%d/file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>This example will create a directory 201560215 (if it does not exist), and then
produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>20160215/file-20160215-1455569023.ts</samp>, <samp>20160215/file-20160215-1455569024.ts</samp>, etc.
</p>
<div class="example">
<pre class="example">ffmpeg -i in.nut -strftime 1 -strftime_mkdir 1 -hls_segment_filename '%Y/%m/%d/file-%Y%m%d-%s.ts' out.m3u8
</pre></div>
<p>This example will create a directory hierarchy 2016/02/15 (if any of them do not exist), and then
produce the playlist, <samp>out.m3u8</samp>, and segment files:
<samp>2016/02/15/file-20160215-1455569023.ts</samp>, <samp>2016/02/15/file-20160215-1455569024.ts</samp>, etc.
</p>
</dd>
<dt><span><samp>hls_segment_options <var>options_list</var></samp></span></dt>
<dd><p>Set output format options using a :-separated list of key=value
parameters. Values containing <code>:</code> special characters must be
escaped.
</p>
</dd>
<dt><span><samp>hls_key_info_file <var>key_info_file</var></samp></span></dt>
<dd><p>Use the information in <var>key_info_file</var> for segment encryption. The first
line of <var>key_info_file</var> specifies the key URI written to the playlist. The
key URL is used to access the encryption key during playback. The second line
specifies the path to the key file used to obtain the key during the encryption
process. The key file is read as a single packed array of 16 octets in binary
format. The optional third line specifies the initialization vector (IV) as a
hexadecimal string to be used instead of the segment sequence number (default)
for encryption. Changes to <var>key_info_file</var> will result in segment
encryption with the new key/IV and an entry in the playlist for the new key
URI/IV if <code>hls_flags periodic_rekey</code> is enabled.
</p>
<p>Key info file format:
</p><div class="example">
<pre class="example"><var>key URI</var>
<var>key file path</var>
<var>IV</var> (optional)
</pre></div>

<p>Example key URIs:
</p><div class="example">
<pre class="example">http://server/file.key
/path/to/file.key
file.key
</pre></div>

<p>Example key file paths:
</p><div class="example">
<pre class="example">file.key
/path/to/file.key
</pre></div>

<p>Example IV:
</p><div class="example">
<pre class="example">0123456789ABCDEF0123456789ABCDEF
</pre></div>

<p>Key info file example:
</p><div class="example">
<pre class="example">http://server/file.key
/path/to/file.key
0123456789ABCDEF0123456789ABCDEF
</pre></div>

<p>Example shell script:
</p><div class="example">
<pre class="example">#!/bin/sh
BASE_URL=${1:-'.'}
openssl rand 16 &gt; file.key
echo $BASE_URL/file.key &gt; file.keyinfo
echo file.key &gt;&gt; file.keyinfo
echo $(openssl rand -hex 16) &gt;&gt; file.keyinfo
ffmpeg -f lavfi -re -i testsrc -c:v h264 -hls_flags delete_segments \
  -hls_key_info_file file.keyinfo out.m3u8
</pre></div>

</dd>
<dt><span><samp>-hls_enc <var>enc</var></samp></span></dt>
<dd><p>Enable (1) or disable (0) the AES128 encryption.
When enabled every segment generated is encrypted and the encryption key
is saved as <var>playlist name</var>.key.
</p>
</dd>
<dt><span><samp>-hls_enc_key <var>key</var></samp></span></dt>
<dd><p>16-octet key to encrypt the segments, by default it
is randomly generated.
</p>
</dd>
<dt><span><samp>-hls_enc_key_url <var>keyurl</var></samp></span></dt>
<dd><p>If set, <var>keyurl</var> is prepended instead of <var>baseurl</var> to the key filename
in the playlist.
</p>
</dd>
<dt><span><samp>-hls_enc_iv <var>iv</var></samp></span></dt>
<dd><p>16-octet initialization vector for every segment instead
of the autogenerated ones.
</p>
</dd>
<dt><span><samp>hls_segment_type <var>flags</var></samp></span></dt>
<dd><p>Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>mpegts</samp>&rsquo;</span></dt>
<dd><p>Output segment files in MPEG-2 Transport Stream format. This is
compatible with all HLS versions.
</p>
</dd>
<dt><span>&lsquo;<samp>fmp4</samp>&rsquo;</span></dt>
<dd><p>Output segment files in fragmented MP4 format, similar to MPEG-DASH.
fmp4 files may be used in HLS version 7 and above.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>hls_fmp4_init_filename <var>filename</var></samp></span></dt>
<dd><p>Set filename to the fragment files header file, default filename is <samp>init.mp4</samp>.
</p>
<p>Use <code>-strftime 1</code> on <var>filename</var> to expand the segment filename with localtime.
</p><div class="example">
<pre class="example">ffmpeg -i in.nut  -hls_segment_type fmp4 -strftime 1 -hls_fmp4_init_filename &quot;%s_init.mp4&quot; out.m3u8
</pre></div>
<p>This will produce init like this
<samp>1602678741_init.mp4</samp>
</p>
</dd>
<dt><span><samp>hls_fmp4_init_resend</samp></span></dt>
<dd><p>Resend init file after m3u8 file refresh every time, default is <var>0</var>.
</p>
<p>When <code>var_stream_map</code> is set with two or more variant streams, the
<var>filename</var> pattern must contain the string &quot;%v&quot;, this string specifies
the position of variant stream index in the generated init file names.
The string &quot;%v&quot; may be present in the filename or in the last directory name
containing the file. If the string is present in the directory name, then
sub-directories are created after expanding the directory name pattern. This
enables creation of init files corresponding to different variant streams in
subdirectories.
</p>
</dd>
<dt><span><samp>hls_flags <var>flags</var></samp></span></dt>
<dd><p>Possible values:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>single_file</samp>&rsquo;</span></dt>
<dd><p>If this flag is set, the muxer will store all segments in a single MPEG-TS
file, and will use byte ranges in the playlist. HLS playlists generated with
this way will have the version number 4.
For example:
</p><div class="example">
<pre class="example">ffmpeg -i in.nut -hls_flags single_file out.m3u8
</pre></div>
<p>Will produce the playlist, <samp>out.m3u8</samp>, and a single segment file,
<samp>out.ts</samp>.
</p>
</dd>
<dt><span>&lsquo;<samp>delete_segments</samp>&rsquo;</span></dt>
<dd><p>Segment files removed from the playlist are deleted after a period of time
equal to the duration of the segment plus the duration of the playlist.
</p>
</dd>
<dt><span>&lsquo;<samp>append_list</samp>&rsquo;</span></dt>
<dd><p>Append new segments into the end of old segment list,
and remove the <code>#EXT-X-ENDLIST</code> from the old segment list.
</p>
</dd>
<dt><span>&lsquo;<samp>round_durations</samp>&rsquo;</span></dt>
<dd><p>Round the duration info in the playlist file segment info to integer
values, instead of using floating point.
If there are no other features requiring higher HLS versions be used,
then this will allow ffmpeg to output a HLS version 2 m3u8.
</p>
</dd>
<dt><span>&lsquo;<samp>discont_start</samp>&rsquo;</span></dt>
<dd><p>Add the <code>#EXT-X-DISCONTINUITY</code> tag to the playlist, before the
first segment&rsquo;s information.
</p>
</dd>
<dt><span>&lsquo;<samp>omit_endlist</samp>&rsquo;</span></dt>
<dd><p>Do not append the <code>EXT-X-ENDLIST</code> tag at the end of the playlist.
</p>
</dd>
<dt><span>&lsquo;<samp>periodic_rekey</samp>&rsquo;</span></dt>
<dd><p>The file specified by <code>hls_key_info_file</code> will be checked periodically and
detect updates to the encryption info. Be sure to replace this file atomically,
including the file containing the AES encryption key.
</p>
</dd>
<dt><span>&lsquo;<samp>independent_segments</samp>&rsquo;</span></dt>
<dd><p>Add the <code>#EXT-X-INDEPENDENT-SEGMENTS</code> to playlists that has video segments
and when all the segments of that playlist are guaranteed to start with a Key frame.
</p>
</dd>
<dt><span>&lsquo;<samp>iframes_only</samp>&rsquo;</span></dt>
<dd><p>Add the <code>#EXT-X-I-FRAMES-ONLY</code> to playlists that has video segments
and can play only I-frames in the <code>#EXT-X-BYTERANGE</code> mode.
</p>
</dd>
<dt><span>&lsquo;<samp>split_by_time</samp>&rsquo;</span></dt>
<dd><p>Allow segments to start on frames other than keyframes. This improves
behavior on some players when the time between keyframes is inconsistent,
but may make things worse on others, and can cause some oddities during
seeking. This flag should be used with the <code>hls_time</code> option.
</p>
</dd>
<dt><span>&lsquo;<samp>program_date_time</samp>&rsquo;</span></dt>
<dd><p>Generate <code>EXT-X-PROGRAM-DATE-TIME</code> tags.
</p>
</dd>
<dt><span>&lsquo;<samp>second_level_segment_index</samp>&rsquo;</span></dt>
<dd><p>Makes it possible to use segment indexes as %%d in hls_segment_filename expression
besides date/time values when strftime is on.
To get fixed width numbers with trailing zeroes, %%0xd format is available where x is the required width.
</p>
</dd>
<dt><span>&lsquo;<samp>second_level_segment_size</samp>&rsquo;</span></dt>
<dd><p>Makes it possible to use segment sizes (counted in bytes) as %%s in hls_segment_filename
expression besides date/time values when strftime is on.
To get fixed width numbers with trailing zeroes, %%0xs format is available where x is the required width.
</p>
</dd>
<dt><span>&lsquo;<samp>second_level_segment_duration</samp>&rsquo;</span></dt>
<dd><p>Makes it possible to use segment duration (calculated  in microseconds) as %%t in hls_segment_filename
expression besides date/time values when strftime is on.
To get fixed width numbers with trailing zeroes, %%0xt format is available where x is the required width.
</p>
<div class="example">
<pre class="example">ffmpeg -i sample.mpeg \
   -f hls -hls_time 3 -hls_list_size 5 \
   -hls_flags second_level_segment_index+second_level_segment_size+second_level_segment_duration \
   -strftime 1 -strftime_mkdir 1 -hls_segment_filename &quot;segment_%Y%m%d%H%M%S_%%04d_%%08s_%%013t.ts&quot; stream.m3u8
</pre></div>
<p>This will produce segments like this:
<samp>segment_20170102194334_0003_00122200_0000003000000.ts</samp>, <samp>segment_20170102194334_0004_00120072_0000003000000.ts</samp> etc.
</p>
</dd>
<dt><span>&lsquo;<samp>temp_file</samp>&rsquo;</span></dt>
<dd><p>Write segment data to filename.tmp and rename to filename only once the segment is complete. A webserver
serving up segments can be configured to reject requests to *.tmp to prevent access to in-progress segments
before they have been added to the m3u8 playlist. This flag also affects how m3u8 playlist files are created.
If this flag is set, all playlist files will written into temporary file and renamed after they are complete, similarly as segments are handled.
But playlists with <code>file</code> protocol and with type (<code>hls_playlist_type</code>) other than <code>vod</code>
are always written into temporary file regardless of this flag. Master playlist files (<code>master_pl_name</code>), if any, with <code>file</code> protocol,
are always written into temporary file regardless of this flag if <code>master_pl_publish_rate</code> value is other than zero.
</p>
</dd>
</dl>

</dd>
<dt><span><samp>hls_playlist_type event</samp></span></dt>
<dd><p>Emit <code>#EXT-X-PLAYLIST-TYPE:EVENT</code> in the m3u8 header. Forces
<samp>hls_list_size</samp> to 0; the playlist can only be appended to.
</p>
</dd>
<dt><span><samp>hls_playlist_type vod</samp></span></dt>
<dd><p>Emit <code>#EXT-X-PLAYLIST-TYPE:VOD</code> in the m3u8 header. Forces
<samp>hls_list_size</samp> to 0; the playlist must not change.
</p>
</dd>
<dt><span><samp>method</samp></span></dt>
<dd><p>Use the given HTTP method to create the hls files.
</p><div class="example">
<pre class="example">ffmpeg -re -i in.ts -f hls -method PUT http://example.com/live/out.m3u8
</pre></div>
<p>This example will upload all the mpegts segment files to the HTTP
server using the HTTP PUT method, and update the m3u8 files every
<code>refresh</code> times using the same method.
Note that the HTTP server must support the given method for uploading
files.
</p>
</dd>
<dt><span><samp>http_user_agent</samp></span></dt>
<dd><p>Override User-Agent field in HTTP header. Applicable only for HTTP output.
</p>
</dd>
<dt><span><samp>var_stream_map</samp></span></dt>
<dd><p>Map string which specifies how to group the audio, video and subtitle streams
into different variant streams. The variant stream groups are separated
by space.
Expected string format is like this &quot;a:0,v:0 a:1,v:1 ....&quot;. Here a:, v:, s: are
the keys to specify audio, video and subtitle streams respectively.
Allowed values are 0 to 9 (limited just based on practical usage).
</p>
<p>When there are two or more variant streams, the output filename pattern must
contain the string &quot;%v&quot;, this string specifies the position of variant stream
index in the output media playlist filenames. The string &quot;%v&quot; may be present in
the filename or in the last directory name containing the file. If the string is
present in the directory name, then sub-directories are created after expanding
the directory name pattern. This enables creation of variant streams in
subdirectories.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example creates two hls variant streams. The first variant stream will
contain video stream of bitrate 1000k and audio stream of bitrate 64k and the
second variant stream will contain video stream of bitrate 256k and audio
stream of bitrate 32k. Here, two media playlist with file names out_0.m3u8 and
out_1.m3u8 will be created. If you want something meaningful text instead of indexes
in result names, you may specify names for each or some of the variants
as in the following example.
</p>

<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0,name:my_hd v:1,a:1,name:my_sd&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>

<p>This example creates two hls variant streams as in the previous one.
But here, the two media playlist with file names out_my_hd.m3u8 and
out_my_sd.m3u8 will be created.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k \
  -map 0:v -map 0:a -map 0:v -f hls -var_stream_map &quot;v:0 a:0 v:1&quot; \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example creates three hls variant streams. The first variant stream will
be a video only stream with video bitrate 1000k, the second variant stream will
be an audio only stream with bitrate 64k and the third variant stream will be a
video only stream with bitrate 256k. Here, three media playlist with file names
out_0.m3u8, out_1.m3u8 and out_2.m3u8 will be created.
</p><div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls -var_stream_map &quot;v:0,a:0 v:1,a:1&quot; \
  http://example.com/live/vs_%v/out.m3u8
</pre></div>
<p>This example creates the variant streams in subdirectories. Here, the first
media playlist is created at <samp>http://example.com/live/vs_0/out.m3u8</samp> and
the second one at <samp>http://example.com/live/vs_1/out.m3u8</samp>.
</p><div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k -b:v:1 3000k  \
  -map 0:a -map 0:a -map 0:v -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low a:1,agroup:aud_high v:0,agroup:aud_low v:1,agroup:aud_high&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example creates two audio only and two video only variant streams. In
addition to the #EXT-X-STREAM-INF tag for each variant stream in the master
playlist, #EXT-X-MEDIA tag is also added for the two audio only variant streams
and they are mapped to the two video only variant streams with audio group names
&rsquo;aud_low&rsquo; and &rsquo;aud_high&rsquo;.
</p>
<p>By default, a single hls variant containing all the encoded streams is created.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k \
  -map 0:a -map 0:a -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low,default:yes a:1,agroup:aud_low v:0,agroup:aud_low&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example creates two audio only and one video only variant streams. In
addition to the #EXT-X-STREAM-INF tag for each variant stream in the master
playlist, #EXT-X-MEDIA tag is also added for the two audio only variant streams
and they are mapped to the one video only variant streams with audio group name
&rsquo;aud_low&rsquo;, and the audio group have default stat is NO or YES.
</p>
<p>By default, a single hls variant containing all the encoded streams is created.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:a:0 32k -b:a:1 64k -b:v:0 1000k \
  -map 0:a -map 0:a -map 0:v -f hls \
  -var_stream_map &quot;a:0,agroup:aud_low,default:yes,language:ENG a:1,agroup:aud_low,language:CHN v:0,agroup:aud_low&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example creates two audio only and one video only variant streams. In
addition to the #EXT-X-STREAM-INF tag for each variant stream in the master
playlist, #EXT-X-MEDIA tag is also added for the two audio only variant streams
and they are mapped to the one video only variant streams with audio group name
&rsquo;aud_low&rsquo;, and the audio group have default stat is NO or YES, and one audio
have and language is named ENG, the other audio language is named CHN.
</p>
<p>By default, a single hls variant containing all the encoded streams is created.
</p>
<div class="example">
<pre class="example">ffmpeg -y -i input_with_subtitle.mkv \
 -b:v:0 5250k -c:v h264 -pix_fmt yuv420p -profile:v main -level 4.1 \
 -b:a:0 256k \
 -c:s webvtt -c:a mp2 -ar 48000 -ac 2 -map 0:v -map 0:a:0 -map 0:s:0 \
 -f hls -var_stream_map &quot;v:0,a:0,s:0,sgroup:subtitle&quot; \
 -master_pl_name master.m3u8 -t 300 -hls_time 10 -hls_init_time 4 -hls_list_size \
 10 -master_pl_publish_rate 10  -hls_flags \
 delete_segments+discont_start+split_by_time ./tmp/video.m3u8
</pre></div>

<p>This example adds <code>#EXT-X-MEDIA</code> tag with <code>TYPE=SUBTITLES</code> in
the master playlist with webvtt subtitle group name &rsquo;subtitle&rsquo;. Please make sure
the input file has one text subtitle stream at least.
</p>
</dd>
<dt><span><samp>cc_stream_map</samp></span></dt>
<dd><p>Map string which specifies different closed captions groups and their
attributes. The closed captions stream groups are separated by space.
Expected string format is like this
&quot;ccgroup:&lt;group name&gt;,instreamid:&lt;INSTREAM-ID&gt;,language:&lt;language code&gt; ....&quot;.
&rsquo;ccgroup&rsquo; and &rsquo;instreamid&rsquo; are mandatory attributes. &rsquo;language&rsquo; is an optional
attribute.
The closed captions groups configured using this option are mapped to different
variant streams by providing the same &rsquo;ccgroup&rsquo; name in the
<code>var_stream_map</code> string. If <code>var_stream_map</code> is not set, then the
first available ccgroup in <code>cc_stream_map</code> is mapped to the output variant
stream. The examples for these two use cases are given below.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v 1000k -b:a 64k -a53cc 1 -f hls \
  -cc_stream_map &quot;ccgroup:cc,instreamid:CC1,language:en&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out.m3u8
</pre></div>
<p>This example adds <code>#EXT-X-MEDIA</code> tag with <code>TYPE=CLOSED-CAPTIONS</code> in
the master playlist with group name &rsquo;cc&rsquo;, language &rsquo;en&rsquo; (english) and
INSTREAM-ID &rsquo;CC1&rsquo;. Also, it adds <code>CLOSED-CAPTIONS</code> attribute with group
name &rsquo;cc&rsquo; for the output variant stream.
</p><div class="example">
<pre class="example">ffmpeg -re -i in.ts -b:v:0 1000k -b:v:1 256k -b:a:0 64k -b:a:1 32k \
  -a53cc:0 1 -a53cc:1 1\
  -map 0:v -map 0:a -map 0:v -map 0:a -f hls \
  -cc_stream_map &quot;ccgroup:cc,instreamid:CC1,language:en ccgroup:cc,instreamid:CC2,language:sp&quot; \
  -var_stream_map &quot;v:0,a:0,ccgroup:cc v:1,a:1,ccgroup:cc&quot; \
  -master_pl_name master.m3u8 \
  http://example.com/live/out_%v.m3u8
</pre></div>
<p>This example adds two <code>#EXT-X-MEDIA</code> tags with <code>TYPE=CLOSED-CAPTIONS</code> in
the master playlist for the INSTREAM-IDs &rsquo;CC1&rsquo; and &rsquo;CC2&rsquo;. Also, it adds
<code>CLOSED-CAPTIONS</code> attribute with group name &rsquo;cc&rsquo; for the two output variant
streams.
</p>
</dd>
<dt><span><samp>master_pl_name</samp></span></dt>
<dd><p>Create HLS master playlist with the given name.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -f hls -master_pl_name master.m3u8 http://example.com/live/out.m3u8
</pre></div>
<p>This example creates HLS master playlist with name master.m3u8 and it is
published at http://example.com/live/
</p>
</dd>
<dt><span><samp>master_pl_publish_rate</samp></span></dt>
<dd><p>Publish master play list repeatedly every after specified number of segment intervals.
</p>
<div class="example">
<pre class="example">ffmpeg -re -i in.ts -f hls -master_pl_name master.m3u8 \
-hls_time 2 -master_pl_publish_rate 30 http://example.com/live/out.m3u8
</pre></div>

<p>This example creates HLS master playlist with name master.m3u8 and keep
publishing it repeatedly every after 30 segments i.e. every after 60s.
</p>
</dd>
<dt><span><samp>http_persistent</samp></span></dt>
<dd><p>Use persistent HTTP connections. Applicable only for HTTP output.
</p>
</dd>
<dt><span><samp>timeout</samp></span></dt>
<dd><p>Set timeout for socket I/O operations. Applicable only for HTTP output.
</p>
</dd>
<dt><span><samp>-ignore_io_errors</samp></span></dt>
<dd><p>Ignore IO errors during open, write and delete. Useful for long-duration runs with network output.
</p>
</dd>
<dt><span><samp>headers</samp></span></dt>
<dd><p>Set custom HTTP headers, can override built in default headers. Applicable only for HTTP output.
</p>
</dd>
</dl>

<span id="ico"></span><a name="ico-1"></a>
<h3 class="section">4.18 ico<span class="pull-right"><a class="anchor hidden-xs" href="#ico-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ico-1" aria-hidden="true">TOC</a></span></h3>

<p>ICO file muxer.
</p>
<p>Microsoft&rsquo;s icon file format (ICO) has some strict limitations that should be noted:
</p>
<ul>
<li> Size cannot exceed 256 pixels in any dimension

</li><li> Only BMP and PNG images can be stored

</li><li> If a BMP image is used, it must be one of the following pixel formats:
<div class="example">
<pre class="example">BMP Bit Depth      FFmpeg Pixel Format
1bit               pal8
4bit               pal8
8bit               pal8
16bit              rgb555le
24bit              bgr24
32bit              bgra
</pre></div>

</li><li> If a BMP image is used, it must use the BITMAPINFOHEADER DIB header

</li><li> If a PNG image is used, it must use the rgba pixel format
</li></ul>

<span id="image2"></span><a name="image2-2"></a>
<h3 class="section">4.19 image2<span class="pull-right"><a class="anchor hidden-xs" href="#image2-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-image2-2" aria-hidden="true">TOC</a></span></h3>

<p>Image file muxer.
</p>
<p>The image file muxer writes video frames to image files.
</p>
<p>The output filenames are specified by a pattern, which can be used to
produce sequentially numbered series of files.
The pattern may contain the string &quot;%d&quot; or &quot;%0<var>N</var>d&quot;, this string
specifies the position of the characters representing a numbering in
the filenames. If the form &quot;%0<var>N</var>d&quot; is used, the string
representing the number in each filename is 0-padded to <var>N</var>
digits. The literal character &rsquo;%&rsquo; can be specified in the pattern with
the string &quot;%%&quot;.
</p>
<p>If the pattern contains &quot;%d&quot; or &quot;%0<var>N</var>d&quot;, the first filename of
the file list specified will contain the number 1, all the following
numbers will be sequential.
</p>
<p>The pattern may contain a suffix which is used to automatically
determine the format of the image files to write.
</p>
<p>For example the pattern &quot;img-%03d.bmp&quot; will specify a sequence of
filenames of the form <samp>img-001.bmp</samp>, <samp>img-002.bmp</samp>, ...,
<samp>img-010.bmp</samp>, etc.
The pattern &quot;img%%-%d.jpg&quot; will specify a sequence of filenames of the
form <samp>img%-1.jpg</samp>, <samp>img%-2.jpg</samp>, ..., <samp>img%-10.jpg</samp>,
etc.
</p>
<p>The image muxer supports the .Y.U.V image file format. This format is
special in that that each image frame consists of three files, for
each of the YUV420P components. To read or write this image file format,
specify the name of the &rsquo;.Y&rsquo; file. The muxer will automatically open the
&rsquo;.U&rsquo; and &rsquo;.V&rsquo; files as required.
</p>
<a name="Options-10"></a>
<h4 class="subsection">4.19.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-10" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>frame_pts</samp></span></dt>
<dd><p>If set to 1, expand the filename with pts from pkt-&gt;pts.
Default value is 0.
</p>
</dd>
<dt><span><samp>start_number</samp></span></dt>
<dd><p>Start the sequence from the specified number. Default value is 1.
</p>
</dd>
<dt><span><samp>update</samp></span></dt>
<dd><p>If set to 1, the filename will always be interpreted as just a
filename, not a pattern, and the corresponding file will be continuously
overwritten with new images. Default value is 0.
</p>
</dd>
<dt><span><samp>strftime</samp></span></dt>
<dd><p>If set to 1, expand the filename with date and time information from
<code>strftime()</code>. Default value is 0.
</p>
</dd>
<dt><span><samp>atomic_writing</samp></span></dt>
<dd><p>Write output to a temporary file, which is renamed to target filename once
writing is completed. Default is disabled.
</p>
</dd>
<dt><span><samp>protocol_opts <var>options_list</var></samp></span></dt>
<dd><p>Set protocol options as a :-separated list of key=value parameters. Values
containing the <code>:</code> special character must be escaped.
</p>
</dd>
</dl>

<a name="Examples-8"></a>
<h4 class="subsection">4.19.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-8" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-8" aria-hidden="true">TOC</a></span></h4>

<p>The following example shows how to use <code>ffmpeg</code> for creating a
sequence of files <samp>img-001.jpeg</samp>, <samp>img-002.jpeg</samp>, ...,
taking one image every second from the input video:
</p><div class="example">
<pre class="example">ffmpeg -i in.avi -vsync cfr -r 1 -f image2 'img-%03d.jpeg'
</pre></div>

<p>Note that with <code>ffmpeg</code>, if the format is not specified with the
<code>-f</code> option and the output filename specifies an image file
format, the image2 muxer is automatically selected, so the previous
command can be written as:
</p><div class="example">
<pre class="example">ffmpeg -i in.avi -vsync cfr -r 1 'img-%03d.jpeg'
</pre></div>

<p>Note also that the pattern must not necessarily contain &quot;%d&quot; or
&quot;%0<var>N</var>d&quot;, for example to create a single image file
<samp>img.jpeg</samp> from the start of the input video you can employ the command:
</p><div class="example">
<pre class="example">ffmpeg -i in.avi -f image2 -frames:v 1 img.jpeg
</pre></div>

<p>The <samp>strftime</samp> option allows you to expand the filename with
date and time information. Check the documentation of
the <code>strftime()</code> function for the syntax.
</p>
<p>For example to generate image files from the <code>strftime()</code>
&quot;%Y-%m-%d_%H-%M-%S&quot; pattern, the following <code>ffmpeg</code> command
can be used:
</p><div class="example">
<pre class="example">ffmpeg -f v4l2 -r 1 -i /dev/video0 -f image2 -strftime 1 &quot;%Y-%m-%d_%H-%M-%S.jpg&quot;
</pre></div>

<p>You can set the file name with current frame&rsquo;s PTS:
</p><div class="example">
<pre class="example">ffmpeg -f v4l2 -r 1 -i /dev/video0 -copyts -f image2 -frame_pts true %d.jpg&quot;
</pre></div>

<p>A more complex example is to publish contents of your desktop directly to a
WebDAV server every second:
</p><div class="example">
<pre class="example">ffmpeg -f x11grab -framerate 1 -i :0.0 -q:v 6 -update 1 -protocol_opts method=PUT http://example.com/desktop.jpg
</pre></div>

<a name="matroska"></a>
<h3 class="section">4.20 matroska<span class="pull-right"><a class="anchor hidden-xs" href="#matroska" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-matroska" aria-hidden="true">TOC</a></span></h3>

<p>Matroska container muxer.
</p>
<p>This muxer implements the matroska and webm container specs.
</p>
<a name="Metadata"></a>
<h4 class="subsection">4.20.1 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata" aria-hidden="true">TOC</a></span></h4>

<p>The recognized metadata settings in this muxer are:
</p>
<dl compact="compact">
<dt><span><samp>title</samp></span></dt>
<dd><p>Set title name provided to a single track. This gets mapped to
the FileDescription element for a stream written as attachment.
</p>
</dd>
<dt><span><samp>language</samp></span></dt>
<dd><p>Specify the language of the track in the Matroska languages form.
</p>
<p>The language can be either the 3 letters bibliographic ISO-639-2 (ISO
639-2/B) form (like &quot;fre&quot; for French), or a language code mixed with a
country code for specialities in languages (like &quot;fre-ca&quot; for Canadian
French).
</p>
</dd>
<dt><span><samp>stereo_mode</samp></span></dt>
<dd><p>Set stereo 3D video layout of two views in a single video track.
</p>
<p>The following values are recognized:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>mono</samp>&rsquo;</span></dt>
<dd><p>video is not stereo
</p></dd>
<dt><span>&lsquo;<samp>left_right</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged side by side, Left-eye view is on the left
</p></dd>
<dt><span>&lsquo;<samp>bottom_top</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged in top-bottom orientation, Left-eye view is at bottom
</p></dd>
<dt><span>&lsquo;<samp>top_bottom</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged in top-bottom orientation, Left-eye view is on top
</p></dd>
<dt><span>&lsquo;<samp>checkerboard_rl</samp>&rsquo;</span></dt>
<dd><p>Each view is arranged in a checkerboard interleaved pattern, Left-eye view being first
</p></dd>
<dt><span>&lsquo;<samp>checkerboard_lr</samp>&rsquo;</span></dt>
<dd><p>Each view is arranged in a checkerboard interleaved pattern, Right-eye view being first
</p></dd>
<dt><span>&lsquo;<samp>row_interleaved_rl</samp>&rsquo;</span></dt>
<dd><p>Each view is constituted by a row based interleaving, Right-eye view is first row
</p></dd>
<dt><span>&lsquo;<samp>row_interleaved_lr</samp>&rsquo;</span></dt>
<dd><p>Each view is constituted by a row based interleaving, Left-eye view is first row
</p></dd>
<dt><span>&lsquo;<samp>col_interleaved_rl</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged in a column based interleaving manner, Right-eye view is first column
</p></dd>
<dt><span>&lsquo;<samp>col_interleaved_lr</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged in a column based interleaving manner, Left-eye view is first column
</p></dd>
<dt><span>&lsquo;<samp>anaglyph_cyan_red</samp>&rsquo;</span></dt>
<dd><p>All frames are in anaglyph format viewable through red-cyan filters
</p></dd>
<dt><span>&lsquo;<samp>right_left</samp>&rsquo;</span></dt>
<dd><p>Both views are arranged side by side, Right-eye view is on the left
</p></dd>
<dt><span>&lsquo;<samp>anaglyph_green_magenta</samp>&rsquo;</span></dt>
<dd><p>All frames are in anaglyph format viewable through green-magenta filters
</p></dd>
<dt><span>&lsquo;<samp>block_lr</samp>&rsquo;</span></dt>
<dd><p>Both eyes laced in one Block, Left-eye view is first
</p></dd>
<dt><span>&lsquo;<samp>block_rl</samp>&rsquo;</span></dt>
<dd><p>Both eyes laced in one Block, Right-eye view is first
</p></dd>
</dl>
</dd>
</dl>

<p>For example a 3D WebM clip can be created using the following command line:
</p><div class="example">
<pre class="example">ffmpeg -i sample_left_right_clip.mpg -an -c:v libvpx -metadata stereo_mode=left_right -y stereo_clip.webm
</pre></div>

<a name="Options-11"></a>
<h4 class="subsection">4.20.2 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-11" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>reserve_index_space</samp></span></dt>
<dd><p>By default, this muxer writes the index for seeking (called cues in Matroska
terms) at the end of the file, because it cannot know in advance how much space
to leave for the index at the beginning of the file. However for some use cases
&ndash; e.g.  streaming where seeking is possible but slow &ndash; it is useful to put the
index at the beginning of the file.
</p>
<p>If this option is set to a non-zero value, the muxer will reserve a given amount
of space in the file header and then try to write the cues there when the muxing
finishes. If the reserved space does not suffice, no Cues will be written, the
file will be finalized and writing the trailer will return an error.
A safe size for most use cases should be about 50kB per hour of video.
</p>
<p>Note that cues are only written if the output is seekable and this option will
have no effect if it is not.
</p>
</dd>
<dt><span><samp>cues_to_front</samp></span></dt>
<dd><p>If set, the muxer will write the index at the beginning of the file
by shifting the main data if necessary. This can be combined with
reserve_index_space in which case the data is only shifted if
the initially reserved space turns out to be insufficient.
</p>
<p>This option is ignored if the output is unseekable.
</p>
</dd>
<dt><span><samp>default_mode</samp></span></dt>
<dd><p>This option controls how the FlagDefault of the output tracks will be set.
It influences which tracks players should play by default. The default mode
is &lsquo;<samp>passthrough</samp>&rsquo;.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>infer</samp>&rsquo;</span></dt>
<dd><p>Every track with disposition default will have the FlagDefault set.
Additionally, for each type of track (audio, video or subtitle), if no track
with disposition default of this type exists, then the first track of this type
will be marked as default (if existing). This ensures that the default flag
is set in a sensible way even if the input originated from containers that
lack the concept of default tracks.
</p></dd>
<dt><span>&lsquo;<samp>infer_no_subs</samp>&rsquo;</span></dt>
<dd><p>This mode is the same as infer except that if no subtitle track with
disposition default exists, no subtitle track will be marked as default.
</p></dd>
<dt><span>&lsquo;<samp>passthrough</samp>&rsquo;</span></dt>
<dd><p>In this mode the FlagDefault is set if and only if the AV_DISPOSITION_DEFAULT
flag is set in the disposition of the corresponding stream.
</p></dd>
</dl>

</dd>
<dt><span><samp>flipped_raw_rgb</samp></span></dt>
<dd><p>If set to true, store positive height for raw RGB bitmaps, which indicates
bitmap is stored bottom-up. Note that this option does not flip the bitmap
which has to be done manually beforehand, e.g. by using the vflip filter.
Default is <var>false</var> and indicates bitmap is stored top down.
</p>
</dd>
</dl>

<span id="md5"></span><a name="md5-1"></a>
<h3 class="section">4.21 md5<span class="pull-right"><a class="anchor hidden-xs" href="#md5-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-md5-1" aria-hidden="true">TOC</a></span></h3>

<p>MD5 testing format.
</p>
<p>This is a variant of the <a href="#hash">hash</a> muxer. Unlike that muxer, it
defaults to using the MD5 hash function.
</p>
<a name="Examples-9"></a>
<h4 class="subsection">4.21.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-9" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-9" aria-hidden="true">TOC</a></span></h4>

<p>To compute the MD5 hash of the input converted to raw
audio and video, and store it in the file <samp>out.md5</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f md5 out.md5
</pre></div>

<p>You can print the MD5 to stdout with the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f md5 -
</pre></div>

<p>See also the <a href="#hash">hash</a> and <a href="#framemd5">framemd5</a> muxers.
</p>
<a name="mov_002c-mp4_002c-ismv"></a>
<h3 class="section">4.22 mov, mp4, ismv<span class="pull-right"><a class="anchor hidden-xs" href="#mov_002c-mp4_002c-ismv" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mov_002c-mp4_002c-ismv" aria-hidden="true">TOC</a></span></h3>

<p>MOV/MP4/ISMV (Smooth Streaming) muxer.
</p>
<p>The mov/mp4/ismv muxer supports fragmentation. Normally, a MOV/MP4
file has all the metadata about all packets stored in one location
(written at the end of the file, it can be moved to the start for
better playback by adding <var>faststart</var> to the <var>movflags</var>, or
using the <code>qt-faststart</code> tool). A fragmented
file consists of a number of fragments, where packets and metadata
about these packets are stored together. Writing a fragmented
file has the advantage that the file is decodable even if the
writing is interrupted (while a normal MOV/MP4 is undecodable if
it is not properly finished), and it requires less memory when writing
very long files (since writing normal MOV/MP4 files stores info about
every single packet in memory until the file is closed). The downside
is that it is less compatible with other applications.
</p>
<a name="Options-12"></a>
<h4 class="subsection">4.22.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-12" aria-hidden="true">TOC</a></span></h4>

<p>Fragmentation is enabled by setting one of the AVOptions that define
how to cut the file into fragments:
</p>
<dl compact="compact">
<dt><span><samp>-moov_size <var>bytes</var></samp></span></dt>
<dd><p>Reserves space for the moov atom at the beginning of the file instead of placing the
moov atom at the end. If the space reserved is insufficient, muxing will fail.
</p></dd>
<dt><span><samp>-movflags frag_keyframe</samp></span></dt>
<dd><p>Start a new fragment at each video keyframe.
</p></dd>
<dt><span><samp>-frag_duration <var>duration</var></samp></span></dt>
<dd><p>Create fragments that are <var>duration</var> microseconds long.
</p></dd>
<dt><span><samp>-frag_size <var>size</var></samp></span></dt>
<dd><p>Create fragments that contain up to <var>size</var> bytes of payload data.
</p></dd>
<dt><span><samp>-movflags frag_custom</samp></span></dt>
<dd><p>Allow the caller to manually choose when to cut fragments, by
calling <code>av_write_frame(ctx, NULL)</code> to write a fragment with
the packets written so far. (This is only useful with other
applications integrating libavformat, not from <code>ffmpeg</code>.)
</p></dd>
<dt><span><samp>-min_frag_duration <var>duration</var></samp></span></dt>
<dd><p>Don&rsquo;t create fragments that are shorter than <var>duration</var> microseconds long.
</p></dd>
</dl>

<p>If more than one condition is specified, fragments are cut when
one of the specified conditions is fulfilled. The exception to this is
<code>-min_frag_duration</code>, which has to be fulfilled for any of the other
conditions to apply.
</p>
<p>Additionally, the way the output file is written can be adjusted
through a few other options:
</p>
<dl compact="compact">
<dt><span><samp>-movflags empty_moov</samp></span></dt>
<dd><p>Write an initial moov atom directly at the start of the file, without
describing any samples in it. Generally, an mdat/moov pair is written
at the start of the file, as a normal MOV/MP4 file, containing only
a short portion of the file. With this option set, there is no initial
mdat atom, and the moov atom only describes the tracks but has
a zero duration.
</p>
<p>This option is implicitly set when writing ismv (Smooth Streaming) files.
</p></dd>
<dt><span><samp>-movflags separate_moof</samp></span></dt>
<dd><p>Write a separate moof (movie fragment) atom for each track. Normally,
packets for all tracks are written in a moof atom (which is slightly
more efficient), but with this option set, the muxer writes one moof/mdat
pair for each track, making it easier to separate tracks.
</p>
<p>This option is implicitly set when writing ismv (Smooth Streaming) files.
</p></dd>
<dt><span><samp>-movflags skip_sidx</samp></span></dt>
<dd><p>Skip writing of sidx atom. When bitrate overhead due to sidx atom is high,
this option could be used for cases where sidx atom is not mandatory.
When global_sidx flag is enabled, this option will be ignored.
</p></dd>
<dt><span><samp>-movflags faststart</samp></span></dt>
<dd><p>Run a second pass moving the index (moov atom) to the beginning of the file.
This operation can take a while, and will not work in various situations such
as fragmented output, thus it is not enabled by default.
</p></dd>
<dt><span><samp>-movflags rtphint</samp></span></dt>
<dd><p>Add RTP hinting tracks to the output file.
</p></dd>
<dt><span><samp>-movflags disable_chpl</samp></span></dt>
<dd><p>Disable Nero chapter markers (chpl atom).  Normally, both Nero chapters
and a QuickTime chapter track are written to the file. With this option
set, only the QuickTime chapter track will be written. Nero chapters can
cause failures when the file is reprocessed with certain tagging programs, like
mp3Tag 2.61a and iTunes 11.3, most likely other versions are affected as well.
</p></dd>
<dt><span><samp>-movflags omit_tfhd_offset</samp></span></dt>
<dd><p>Do not write any absolute base_data_offset in tfhd atoms. This avoids
tying fragments to absolute byte positions in the file/streams.
</p></dd>
<dt><span><samp>-movflags default_base_moof</samp></span></dt>
<dd><p>Similarly to the omit_tfhd_offset, this flag avoids writing the
absolute base_data_offset field in tfhd atoms, but does so by using
the new default-base-is-moof flag instead. This flag is new from
14496-12:2012. This may make the fragments easier to parse in certain
circumstances (avoiding basing track fragment location calculations
on the implicit end of the previous track fragment).
</p></dd>
<dt><span><samp>-write_tmcd</samp></span></dt>
<dd><p>Specify <code>on</code> to force writing a timecode track, <code>off</code> to disable it
and <code>auto</code> to write a timecode track only for mov and mp4 output (default).
</p></dd>
<dt><span><samp>-movflags negative_cts_offsets</samp></span></dt>
<dd><p>Enables utilization of version 1 of the CTTS box, in which the CTS offsets can
be negative. This enables the initial sample to have DTS/CTS of zero, and
reduces the need for edit lists for some cases such as video tracks with
B-frames. Additionally, eases conformance with the DASH-IF interoperability
guidelines.
</p>
<p>This option is implicitly set when writing ismv (Smooth Streaming) files.
</p>
</dd>
<dt><span><samp>-write_btrt <var>bool</var></samp></span></dt>
<dd><p>Force or disable writing bitrate box inside stsd box of a track.
The box contains decoding buffer size (in bytes), maximum bitrate and
average bitrate for the track. The box will be skipped if none of these values
can be computed.
Default is <code>-1</code> or <code>auto</code>, which will write the box only in MP4 mode.
</p>
</dd>
<dt><span><samp>-write_prft</samp></span></dt>
<dd><p>Write producer time reference box (PRFT) with a specified time source for the
NTP field in the PRFT box. Set value as &lsquo;<samp>wallclock</samp>&rsquo; to specify timesource
as wallclock time and &lsquo;<samp>pts</samp>&rsquo; to specify timesource as input packets&rsquo; PTS
values.
</p>
<p>Setting value to &lsquo;<samp>pts</samp>&rsquo; is applicable only for a live encoding use case,
where PTS values are set as as wallclock time at the source. For example, an
encoding use case with decklink capture source where <samp>video_pts</samp> and
<samp>audio_pts</samp> are set to &lsquo;<samp>abs_wallclock</samp>&rsquo;.
</p>
</dd>
<dt><span><samp>-empty_hdlr_name <var>bool</var></samp></span></dt>
<dd><p>Enable to skip writing the name inside a <code>hdlr</code> box.
Default is <code>false</code>.
</p>
</dd>
<dt><span><samp>-movie_timescale <var>scale</var></samp></span></dt>
<dd><p>Set the timescale written in the movie header box (<code>mvhd</code>).
Range is 1 to INT_MAX. Default is 1000.
</p>
</dd>
<dt><span><samp>-video_track_timescale <var>scale</var></samp></span></dt>
<dd><p>Set the timescale used for video tracks. Range is 0 to INT_MAX.
If set to <code>0</code>, the timescale is automatically set based on
the native stream time base. Default is 0.
</p></dd>
</dl>

<a name="Example"></a>
<h4 class="subsection">4.22.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example" aria-hidden="true">TOC</a></span></h4>

<p>Smooth Streaming content can be pushed in real time to a publishing
point on IIS with this muxer. Example:
</p><div class="example">
<pre class="example">ffmpeg -re <var>&lt;normal input/transcoding options&gt;</var> -movflags isml+frag_keyframe -f ismv http://server/publishingpoint.isml/Streams(Encoder1)
</pre></div>

<a name="mp3"></a>
<h3 class="section">4.23 mp3<span class="pull-right"><a class="anchor hidden-xs" href="#mp3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mp3" aria-hidden="true">TOC</a></span></h3>

<p>The MP3 muxer writes a raw MP3 stream with the following optional features:
</p><ul>
<li> An ID3v2 metadata header at the beginning (enabled by default). Versions 2.3 and
2.4 are supported, the <code>id3v2_version</code> private option controls which one is
used (3 or 4). Setting <code>id3v2_version</code> to 0 disables the ID3v2 header
completely.

<p>The muxer supports writing attached pictures (APIC frames) to the ID3v2 header.
The pictures are supplied to the muxer in form of a video stream with a single
packet. There can be any number of those streams, each will correspond to a
single APIC frame.  The stream metadata tags <var>title</var> and <var>comment</var> map
to APIC <var>description</var> and <var>picture type</var> respectively. See
<a href="http://id3.org/id3v2.4.0-frames">http://id3.org/id3v2.4.0-frames</a> for allowed picture types.
</p>
<p>Note that the APIC frames must be written at the beginning, so the muxer will
buffer the audio frames until it gets all the pictures. It is therefore advised
to provide the pictures as soon as possible to avoid excessive buffering.
</p>
</li><li> A Xing/LAME frame right after the ID3v2 header (if present). It is enabled by
default, but will be written only if the output is seekable. The
<code>write_xing</code> private option can be used to disable it.  The frame contains
various information that may be useful to the decoder, like the audio duration
or encoder delay.

</li><li> A legacy ID3v1 tag at the end of the file (disabled by default). It may be
enabled with the <code>write_id3v1</code> private option, but as its capabilities are
very limited, its usage is not recommended.
</li></ul>

<p>Examples:
</p>
<p>Write an mp3 with an ID3v2.3 header and an ID3v1 footer:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -id3v2_version 3 -write_id3v1 1 out.mp3
</pre></div>

<p>To attach a picture to an mp3 file select both the audio and the picture stream
with <code>map</code>:
</p><div class="example">
<pre class="example">ffmpeg -i input.mp3 -i cover.png -c copy -map 0 -map 1
-metadata:s:v title=&quot;Album cover&quot; -metadata:s:v comment=&quot;Cover (Front)&quot; out.mp3
</pre></div>

<p>Write a &quot;clean&quot; MP3 without any extra features:
</p><div class="example">
<pre class="example">ffmpeg -i input.wav -write_xing 0 -id3v2_version 0 out.mp3
</pre></div>

<a name="mpegts-1"></a>
<h3 class="section">4.24 mpegts<span class="pull-right"><a class="anchor hidden-xs" href="#mpegts-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpegts-1" aria-hidden="true">TOC</a></span></h3>

<p>MPEG transport stream muxer.
</p>
<p>This muxer implements ISO 13818-1 and part of ETSI EN 300 468.
</p>
<p>The recognized metadata settings in mpegts muxer are <code>service_provider</code>
and <code>service_name</code>. If they are not set the default for
<code>service_provider</code> is &lsquo;<samp>FFmpeg</samp>&rsquo; and the default for
<code>service_name</code> is &lsquo;<samp>Service01</samp>&rsquo;.
</p>
<a name="Options-13"></a>
<h4 class="subsection">4.24.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-13" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-13" aria-hidden="true">TOC</a></span></h4>

<p>The muxer options are:
</p>
<dl compact="compact">
<dt><span><samp>mpegts_transport_stream_id <var>integer</var></samp></span></dt>
<dd><p>Set the &lsquo;<samp>transport_stream_id</samp>&rsquo;. This identifies a transponder in DVB.
Default is <code>0x0001</code>.
</p>
</dd>
<dt><span><samp>mpegts_original_network_id <var>integer</var></samp></span></dt>
<dd><p>Set the &lsquo;<samp>original_network_id</samp>&rsquo;. This is unique identifier of a
network in DVB. Its main use is in the unique identification of a service
through the path &lsquo;<samp>Original_Network_ID, Transport_Stream_ID</samp>&rsquo;. Default
is <code>0x0001</code>.
</p>
</dd>
<dt><span><samp>mpegts_service_id <var>integer</var></samp></span></dt>
<dd><p>Set the &lsquo;<samp>service_id</samp>&rsquo;, also known as program in DVB. Default is
<code>0x0001</code>.
</p>
</dd>
<dt><span><samp>mpegts_service_type <var>integer</var></samp></span></dt>
<dd><p>Set the program &lsquo;<samp>service_type</samp>&rsquo;. Default is <code>digital_tv</code>.
Accepts the following options:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>hex_value</samp>&rsquo;</span></dt>
<dd><p>Any hexadecimal value between <code>0x01</code> and <code>0xff</code> as defined in
ETSI 300 468.
</p></dd>
<dt><span>&lsquo;<samp>digital_tv</samp>&rsquo;</span></dt>
<dd><p>Digital TV service.
</p></dd>
<dt><span>&lsquo;<samp>digital_radio</samp>&rsquo;</span></dt>
<dd><p>Digital Radio service.
</p></dd>
<dt><span>&lsquo;<samp>teletext</samp>&rsquo;</span></dt>
<dd><p>Teletext service.
</p></dd>
<dt><span>&lsquo;<samp>advanced_codec_digital_radio</samp>&rsquo;</span></dt>
<dd><p>Advanced Codec Digital Radio service.
</p></dd>
<dt><span>&lsquo;<samp>mpeg2_digital_hdtv</samp>&rsquo;</span></dt>
<dd><p>MPEG2 Digital HDTV service.
</p></dd>
<dt><span>&lsquo;<samp>advanced_codec_digital_sdtv</samp>&rsquo;</span></dt>
<dd><p>Advanced Codec Digital SDTV service.
</p></dd>
<dt><span>&lsquo;<samp>advanced_codec_digital_hdtv</samp>&rsquo;</span></dt>
<dd><p>Advanced Codec Digital HDTV service.
</p></dd>
</dl>

</dd>
<dt><span><samp>mpegts_pmt_start_pid <var>integer</var></samp></span></dt>
<dd><p>Set the first PID for PMTs. Default is <code>0x1000</code>, minimum is <code>0x0020</code>,
maximum is <code>0x1ffa</code>. This option has no effect in m2ts mode where the PMT
PID is fixed <code>0x0100</code>.
</p>
</dd>
<dt><span><samp>mpegts_start_pid <var>integer</var></samp></span></dt>
<dd><p>Set the first PID for elementary streams. Default is <code>0x0100</code>, minimum is
<code>0x0020</code>, maximum is <code>0x1ffa</code>. This option has no effect in m2ts mode
where the elementary stream PIDs are fixed.
</p>
</dd>
<dt><span><samp>mpegts_m2ts_mode <var>boolean</var></samp></span></dt>
<dd><p>Enable m2ts mode if set to <code>1</code>. Default value is <code>-1</code> which
disables m2ts mode.
</p>
</dd>
<dt><span><samp>muxrate <var>integer</var></samp></span></dt>
<dd><p>Set a constant muxrate. Default is VBR.
</p>
</dd>
<dt><span><samp>pes_payload_size <var>integer</var></samp></span></dt>
<dd><p>Set minimum PES packet payload in bytes. Default is <code>2930</code>.
</p>
</dd>
<dt><span><samp>mpegts_flags <var>flags</var></samp></span></dt>
<dd><p>Set mpegts flags. Accepts the following options:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>resend_headers</samp>&rsquo;</span></dt>
<dd><p>Reemit PAT/PMT before writing the next packet.
</p></dd>
<dt><span>&lsquo;<samp>latm</samp>&rsquo;</span></dt>
<dd><p>Use LATM packetization for AAC.
</p></dd>
<dt><span>&lsquo;<samp>pat_pmt_at_frames</samp>&rsquo;</span></dt>
<dd><p>Reemit PAT and PMT at each video frame.
</p></dd>
<dt><span>&lsquo;<samp>system_b</samp>&rsquo;</span></dt>
<dd><p>Conform to System B (DVB) instead of System A (ATSC).
</p></dd>
<dt><span>&lsquo;<samp>initial_discontinuity</samp>&rsquo;</span></dt>
<dd><p>Mark the initial packet of each stream as discontinuity.
</p></dd>
<dt><span>&lsquo;<samp>nit</samp>&rsquo;</span></dt>
<dd><p>Emit NIT table.
</p></dd>
</dl>

</dd>
<dt><span><samp>mpegts_copyts <var>boolean</var></samp></span></dt>
<dd><p>Preserve original timestamps, if value is set to <code>1</code>. Default value
is <code>-1</code>, which results in shifting timestamps so that they start from 0.
</p>
</dd>
<dt><span><samp>omit_video_pes_length <var>boolean</var></samp></span></dt>
<dd><p>Omit the PES packet length for video packets. Default is <code>1</code> (true).
</p>
</dd>
<dt><span><samp>pcr_period <var>integer</var></samp></span></dt>
<dd><p>Override the default PCR retransmission time in milliseconds. Default is
<code>-1</code> which means that the PCR interval will be determined automatically:
20 ms is used for CBR streams, the highest multiple of the frame duration which
is less than 100 ms is used for VBR streams.
</p>
</dd>
<dt><span><samp>pat_period <var>duration</var></samp></span></dt>
<dd><p>Maximum time in seconds between PAT/PMT tables. Default is <code>0.1</code>.
</p>
</dd>
<dt><span><samp>sdt_period <var>duration</var></samp></span></dt>
<dd><p>Maximum time in seconds between SDT tables. Default is <code>0.5</code>.
</p>
</dd>
<dt><span><samp>nit_period <var>duration</var></samp></span></dt>
<dd><p>Maximum time in seconds between NIT tables. Default is <code>0.5</code>.
</p>
</dd>
<dt><span><samp>tables_version <var>integer</var></samp></span></dt>
<dd><p>Set PAT, PMT, SDT and NIT version (default <code>0</code>, valid values are from 0 to 31, inclusively).
This option allows updating stream structure so that standard consumer may
detect the change. To do so, reopen output <code>AVFormatContext</code> (in case of API
usage) or restart <code>ffmpeg</code> instance, cyclically changing
<samp>tables_version</samp> value:
</p>
<div class="example">
<pre class="example">ffmpeg -i source1.ts -codec copy -f mpegts -tables_version 0 udp://1.1.1.1:1111
ffmpeg -i source2.ts -codec copy -f mpegts -tables_version 1 udp://1.1.1.1:1111
...
ffmpeg -i source3.ts -codec copy -f mpegts -tables_version 31 udp://1.1.1.1:1111
ffmpeg -i source1.ts -codec copy -f mpegts -tables_version 0 udp://1.1.1.1:1111
ffmpeg -i source2.ts -codec copy -f mpegts -tables_version 1 udp://1.1.1.1:1111
...
</pre></div>
</dd>
</dl>

<a name="Example-1"></a>
<h4 class="subsection">4.24.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-1" aria-hidden="true">TOC</a></span></h4>

<div class="example">
<pre class="example">ffmpeg -i file.mpg -c copy \
     -mpegts_original_network_id 0x1122 \
     -mpegts_transport_stream_id 0x3344 \
     -mpegts_service_id 0x5566 \
     -mpegts_pmt_start_pid 0x1500 \
     -mpegts_start_pid 0x150 \
     -metadata service_provider=&quot;Some provider&quot; \
     -metadata service_name=&quot;Some Channel&quot; \
     out.ts
</pre></div>

<a name="mxf_002c-mxf_005fd10_002c-mxf_005fopatom"></a>
<h3 class="section">4.25 mxf, mxf_d10, mxf_opatom<span class="pull-right"><a class="anchor hidden-xs" href="#mxf_002c-mxf_005fd10_002c-mxf_005fopatom" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mxf_002c-mxf_005fd10_002c-mxf_005fopatom" aria-hidden="true">TOC</a></span></h3>

<p>MXF muxer.
</p>
<a name="Options-14"></a>
<h4 class="subsection">4.25.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-14" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-14" aria-hidden="true">TOC</a></span></h4>

<p>The muxer options are:
</p>
<dl compact="compact">
<dt><span><samp>store_user_comments <var>bool</var></samp></span></dt>
<dd><p>Set if user comments should be stored if available or never.
IRT D-10 does not allow user comments. The default is thus to write them for
mxf and mxf_opatom but not for mxf_d10
</p></dd>
</dl>

<a name="null"></a>
<h3 class="section">4.26 null<span class="pull-right"><a class="anchor hidden-xs" href="#null" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-null" aria-hidden="true">TOC</a></span></h3>

<p>Null muxer.
</p>
<p>This muxer does not generate any output file, it is mainly useful for
testing or benchmarking purposes.
</p>
<p>For example to benchmark decoding with <code>ffmpeg</code> you can use the
command:
</p><div class="example">
<pre class="example">ffmpeg -benchmark -i INPUT -f null out.null
</pre></div>

<p>Note that the above command does not read or write the <samp>out.null</samp>
file, but specifying the output file is required by the <code>ffmpeg</code>
syntax.
</p>
<p>Alternatively you can write the command as:
</p><div class="example">
<pre class="example">ffmpeg -benchmark -i INPUT -f null -
</pre></div>

<a name="nut"></a>
<h3 class="section">4.27 nut<span class="pull-right"><a class="anchor hidden-xs" href="#nut" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-nut" aria-hidden="true">TOC</a></span></h3>

<dl compact="compact">
<dt><span><samp>-syncpoints <var>flags</var></samp></span></dt>
<dd><p>Change the syncpoint usage in nut:
</p><dl compact="compact">
<dt><span><samp><var>default</var> use the normal low-overhead seeking aids.</samp></span></dt>
<dt><span><samp><var>none</var> do not use the syncpoints at all, reducing the overhead but making the stream non-seekable;</samp></span></dt>
<dd><p>Use of this option is not recommended, as the resulting files are very damage
    sensitive and seeking is not possible. Also in general the overhead from
    syncpoints is negligible. Note, -<code>write_index</code> 0 can be used to disable
    all growing data tables, allowing to mux endless streams with limited memory
    and without these disadvantages.
</p></dd>
<dt><span><samp><var>timestamped</var> extend the syncpoint with a wallclock field.</samp></span></dt>
</dl>
<p>The <var>none</var> and <var>timestamped</var> flags are experimental.
</p></dd>
<dt><span><samp>-write_index <var>bool</var></samp></span></dt>
<dd><p>Write index at the end, the default is to write an index.
</p></dd>
</dl>

<div class="example">
<pre class="example">ffmpeg -i INPUT -f_strict experimental -syncpoints none - | processor
</pre></div>

<a name="ogg"></a>
<h3 class="section">4.28 ogg<span class="pull-right"><a class="anchor hidden-xs" href="#ogg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ogg" aria-hidden="true">TOC</a></span></h3>

<p>Ogg container muxer.
</p>
<dl compact="compact">
<dt><span><samp>-page_duration <var>duration</var></samp></span></dt>
<dd><p>Preferred page duration, in microseconds. The muxer will attempt to create
pages that are approximately <var>duration</var> microseconds long. This allows the
user to compromise between seek granularity and container overhead. The default
is 1 second. A value of 0 will fill all segments, making pages as large as
possible. A value of 1 will effectively use 1 packet-per-page in most
situations, giving a small seek granularity at the cost of additional container
overhead.
</p></dd>
<dt><span><samp>-serial_offset <var>value</var></samp></span></dt>
<dd><p>Serial value from which to set the streams serial number.
Setting it to different and sufficiently large values ensures that the produced
ogg files can be safely chained.
</p>
</dd>
</dl>

<span id="raw-muxers"></span><a name="raw-muxers-1"></a>
<h3 class="section">4.29 raw muxers<span class="pull-right"><a class="anchor hidden-xs" href="#raw-muxers-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-raw-muxers-1" aria-hidden="true">TOC</a></span></h3>

<p>Raw muxers accept a single stream matching the designated codec. They do not store timestamps or metadata.
The recognized extension is the same as the muxer name unless indicated otherwise.
</p>
<a name="ac3"></a>
<h4 class="subsection">4.29.1 ac3<span class="pull-right"><a class="anchor hidden-xs" href="#ac3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-ac3" aria-hidden="true">TOC</a></span></h4>

<p>Dolby Digital, also known as AC-3, audio.
</p>
<a name="adx"></a>
<h4 class="subsection">4.29.2 adx<span class="pull-right"><a class="anchor hidden-xs" href="#adx" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-adx" aria-hidden="true">TOC</a></span></h4>

<p>CRI Middleware ADX audio.
</p>
<p>This muxer will write out the total sample count near the start of the first packet
when the output is seekable and the count can be stored in 32 bits.
</p>
<a name="aptx"></a>
<h4 class="subsection">4.29.3 aptx<span class="pull-right"><a class="anchor hidden-xs" href="#aptx" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aptx" aria-hidden="true">TOC</a></span></h4>

<p>aptX (Audio Processing Technology for Bluetooth) audio.
</p>
<a name="aptx_005fhd"></a>
<h4 class="subsection">4.29.4 aptx_hd<span class="pull-right"><a class="anchor hidden-xs" href="#aptx_005fhd" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aptx_005fhd" aria-hidden="true">TOC</a></span></h4>

<p>aptX HD (Audio Processing Technology for Bluetooth) audio.
</p>
<p>Extensions: aptxhd
</p>
<a name="avs2"></a>
<h4 class="subsection">4.29.5 avs2<span class="pull-right"><a class="anchor hidden-xs" href="#avs2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-avs2" aria-hidden="true">TOC</a></span></h4>

<p>AVS2-P2/IEEE1857.4 video.
</p>
<p>Extensions: avs, avs2
</p>
<a name="cavsvideo"></a>
<h4 class="subsection">4.29.6 cavsvideo<span class="pull-right"><a class="anchor hidden-xs" href="#cavsvideo" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-cavsvideo" aria-hidden="true">TOC</a></span></h4>

<p>Chinese AVS (Audio Video Standard) video.
</p>
<p>Extensions: cavs
</p>
<a name="codec2raw"></a>
<h4 class="subsection">4.29.7 codec2raw<span class="pull-right"><a class="anchor hidden-xs" href="#codec2raw" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-codec2raw" aria-hidden="true">TOC</a></span></h4>

<p>Codec 2 audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with the ffmpeg CLI tool <code>-f codec2raw</code>.
</p>
<a name="data"></a>
<h4 class="subsection">4.29.8 data<span class="pull-right"><a class="anchor hidden-xs" href="#data" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-data" aria-hidden="true">TOC</a></span></h4>

<p>Data muxer accepts a single stream with any codec of any type.
The input stream has to be selected using the <code>-map</code> option with the ffmpeg CLI tool.
</p>
<p>No extension is registered so format name has to be supplied e.g. with the ffmpeg CLI tool <code>-f data</code>.
</p>
<a name="dirac"></a>
<h4 class="subsection">4.29.9 dirac<span class="pull-right"><a class="anchor hidden-xs" href="#dirac" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dirac" aria-hidden="true">TOC</a></span></h4>

<p>BBC Dirac video. The Dirac Pro codec is a subset and is standardized as SMPTE VC-2.
</p>
<p>Extensions: drc, vc2
</p>
<a name="dnxhd"></a>
<h4 class="subsection">4.29.10 dnxhd<span class="pull-right"><a class="anchor hidden-xs" href="#dnxhd" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dnxhd" aria-hidden="true">TOC</a></span></h4>

<p>Avid DNxHD video. It is standardized as SMPTE VC-3. Accepts DNxHR streams.
</p>
<p>Extensions: dnxhd, dnxhr
</p>
<a name="dts"></a>
<h4 class="subsection">4.29.11 dts<span class="pull-right"><a class="anchor hidden-xs" href="#dts" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dts" aria-hidden="true">TOC</a></span></h4>

<p>DTS Coherent Acoustics (DCA) audio.
</p>
<a name="eac3"></a>
<h4 class="subsection">4.29.12 eac3<span class="pull-right"><a class="anchor hidden-xs" href="#eac3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-eac3" aria-hidden="true">TOC</a></span></h4>

<p>Dolby Digital Plus, also known as Enhanced AC-3, audio.
</p>
<a name="g722"></a>
<h4 class="subsection">4.29.13 g722<span class="pull-right"><a class="anchor hidden-xs" href="#g722" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-g722" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T G.722 audio.
</p>
<a name="g723_005f1"></a>
<h4 class="subsection">4.29.14 g723_1<span class="pull-right"><a class="anchor hidden-xs" href="#g723_005f1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-g723_005f1" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T G.723.1 audio.
</p>
<p>Extensions: tco, rco
</p>
<a name="g726"></a>
<h4 class="subsection">4.29.15 g726<span class="pull-right"><a class="anchor hidden-xs" href="#g726" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-g726" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T G.726 big-endian (&quot;left-justified&quot;) audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with the ffmpeg CLI tool <code>-f g726</code>.
</p>
<a name="g726le"></a>
<h4 class="subsection">4.29.16 g726le<span class="pull-right"><a class="anchor hidden-xs" href="#g726le" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-g726le" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T G.726 little-endian (&quot;right-justified&quot;) audio.
</p>
<p>No extension is registered so format name has to be supplied e.g. with the ffmpeg CLI tool <code>-f g726le</code>.
</p>
<a name="gsm"></a>
<h4 class="subsection">4.29.17 gsm<span class="pull-right"><a class="anchor hidden-xs" href="#gsm" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gsm" aria-hidden="true">TOC</a></span></h4>

<p>Global System for Mobile Communications audio.
</p>
<a name="h261"></a>
<h4 class="subsection">4.29.18 h261<span class="pull-right"><a class="anchor hidden-xs" href="#h261" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h261" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T H.261 video.
</p>
<a name="h263"></a>
<h4 class="subsection">4.29.19 h263<span class="pull-right"><a class="anchor hidden-xs" href="#h263" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h263" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T H.263 / H.263-1996, H.263+ / H.263-1998 / H.263 version 2 video.
</p>
<a name="h264"></a>
<h4 class="subsection">4.29.20 h264<span class="pull-right"><a class="anchor hidden-xs" href="#h264" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h264" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T H.264 / MPEG-4 Part 10 AVC video. Bitstream shall be converted to Annex B syntax if it&rsquo;s in length-prefixed mode.
</p>
<p>Extensions: h264, 264
</p>
<a name="hevc"></a>
<h4 class="subsection">4.29.21 hevc<span class="pull-right"><a class="anchor hidden-xs" href="#hevc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hevc" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T H.265 / MPEG-H Part 2 HEVC video. Bitstream shall be converted to Annex B syntax if it&rsquo;s in length-prefixed mode.
</p>
<p>Extensions: hevc, h265, 265
</p>
<a name="m4v"></a>
<h4 class="subsection">4.29.22 m4v<span class="pull-right"><a class="anchor hidden-xs" href="#m4v" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-m4v" aria-hidden="true">TOC</a></span></h4>

<p>MPEG-4 Part 2 video.
</p>
<a name="mjpeg"></a>
<h4 class="subsection">4.29.23 mjpeg<span class="pull-right"><a class="anchor hidden-xs" href="#mjpeg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mjpeg" aria-hidden="true">TOC</a></span></h4>

<p>Motion JPEG video.
</p>
<p>Extensions: mjpg, mjpeg
</p>
<a name="mlp"></a>
<h4 class="subsection">4.29.24 mlp<span class="pull-right"><a class="anchor hidden-xs" href="#mlp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mlp" aria-hidden="true">TOC</a></span></h4>

<p>Meridian Lossless Packing, also known as Packed PCM, audio.
</p>
<a name="mp2"></a>
<h4 class="subsection">4.29.25 mp2<span class="pull-right"><a class="anchor hidden-xs" href="#mp2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mp2" aria-hidden="true">TOC</a></span></h4>

<p>MPEG-1 Audio Layer II audio.
</p>
<p>Extensions: mp2, m2a, mpa
</p>
<a name="mpeg1video"></a>
<h4 class="subsection">4.29.26 mpeg1video<span class="pull-right"><a class="anchor hidden-xs" href="#mpeg1video" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpeg1video" aria-hidden="true">TOC</a></span></h4>

<p>MPEG-1 Part 2 video.
</p>
<p>Extensions: mpg, mpeg, m1v
</p>
<a name="mpeg2video"></a>
<h4 class="subsection">4.29.27 mpeg2video<span class="pull-right"><a class="anchor hidden-xs" href="#mpeg2video" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpeg2video" aria-hidden="true">TOC</a></span></h4>

<p>ITU-T H.262 / MPEG-2 Part 2 video.
</p>
<p>Extensions: m2v
</p>
<a name="obu"></a>
<h4 class="subsection">4.29.28 obu<span class="pull-right"><a class="anchor hidden-xs" href="#obu" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-obu" aria-hidden="true">TOC</a></span></h4>

<p>AV1 low overhead Open Bitstream Units muxer. Temporal delimiter OBUs will be inserted in all temporal units of the stream.
</p>
<a name="rawvideo-1"></a>
<h4 class="subsection">4.29.29 rawvideo<span class="pull-right"><a class="anchor hidden-xs" href="#rawvideo-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-rawvideo-1" aria-hidden="true">TOC</a></span></h4>

<p>Raw uncompressed video.
</p>
<p>Extensions: yuv, rgb
</p>
<a name="sbc"></a>
<h4 class="subsection">4.29.30 sbc<span class="pull-right"><a class="anchor hidden-xs" href="#sbc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-sbc" aria-hidden="true">TOC</a></span></h4>

<p>Bluetooth SIG low-complexity subband codec audio.
</p>
<p>Extensions: sbc, msbc
</p>
<a name="truehd"></a>
<h4 class="subsection">4.29.31 truehd<span class="pull-right"><a class="anchor hidden-xs" href="#truehd" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-truehd" aria-hidden="true">TOC</a></span></h4>

<p>Dolby TrueHD audio.
</p>
<p>Extensions: thd
</p>
<a name="vc1"></a>
<h4 class="subsection">4.29.32 vc1<span class="pull-right"><a class="anchor hidden-xs" href="#vc1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vc1" aria-hidden="true">TOC</a></span></h4>

<p>SMPTE 421M / VC-1 video.
</p>
<span id="segment"></span><a name="segment_002c-stream_005fsegment_002c-ssegment"></a>
<h3 class="section">4.30 segment, stream_segment, ssegment<span class="pull-right"><a class="anchor hidden-xs" href="#segment_002c-stream_005fsegment_002c-ssegment" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-segment_002c-stream_005fsegment_002c-ssegment" aria-hidden="true">TOC</a></span></h3>

<p>Basic stream segmenter.
</p>
<p>This muxer outputs streams to a number of separate files of nearly
fixed duration. Output filename pattern can be set in a fashion
similar to <a href="#image2">image2</a>, or by using a <code>strftime</code> template if
the <samp>strftime</samp> option is enabled.
</p>
<p><code>stream_segment</code> is a variant of the muxer used to write to
streaming output formats, i.e. which do not require global headers,
and is recommended for outputting e.g. to MPEG transport stream segments.
<code>ssegment</code> is a shorter alias for <code>stream_segment</code>.
</p>
<p>Every segment starts with a keyframe of the selected reference stream,
which is set through the <samp>reference_stream</samp> option.
</p>
<p>Note that if you want accurate splitting for a video file, you need to
make the input key frames correspond to the exact splitting times
expected by the segmenter, or the segment muxer will start the new
segment with the key frame found next after the specified start
time.
</p>
<p>The segment muxer works best with a single constant frame rate video.
</p>
<p>Optionally it can generate a list of the created segments, by setting
the option <var>segment_list</var>. The list type is specified by the
<var>segment_list_type</var> option. The entry filenames in the segment
list are set by default to the basename of the corresponding segment
files.
</p>
<p>See also the <a href="#hls">hls</a> muxer, which provides a more specific
implementation for HLS segmentation.
</p>
<a name="Options-15"></a>
<h4 class="subsection">4.30.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-15" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-15" aria-hidden="true">TOC</a></span></h4>

<p>The segment muxer supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>increment_tc <var>1|0</var></samp></span></dt>
<dd><p>if set to <code>1</code>, increment timecode between each segment
If this is selected, the input need to have
a timecode in the first video stream. Default value is
<code>0</code>.
</p>
</dd>
<dt><span><samp>reference_stream <var>specifier</var></samp></span></dt>
<dd><p>Set the reference stream, as specified by the string <var>specifier</var>.
If <var>specifier</var> is set to <code>auto</code>, the reference is chosen
automatically. Otherwise it must be a stream specifier (see the &ldquo;Stream
specifiers&rdquo; chapter in the ffmpeg manual) which specifies the
reference stream. The default value is <code>auto</code>.
</p>
</dd>
<dt><span><samp>segment_format <var>format</var></samp></span></dt>
<dd><p>Override the inner container format, by default it is guessed by the filename
extension.
</p>
</dd>
<dt><span><samp>segment_format_options <var>options_list</var></samp></span></dt>
<dd><p>Set output format options using a :-separated list of key=value
parameters. Values containing the <code>:</code> special character must be
escaped.
</p>
</dd>
<dt><span><samp>segment_list <var>name</var></samp></span></dt>
<dd><p>Generate also a listfile named <var>name</var>. If not specified no
listfile is generated.
</p>
</dd>
<dt><span><samp>segment_list_flags <var>flags</var></samp></span></dt>
<dd><p>Set flags affecting the segment list generation.
</p>
<p>It currently supports the following flags:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>cache</samp>&rsquo;</span></dt>
<dd><p>Allow caching (only affects M3U8 list files).
</p>
</dd>
<dt><span>&lsquo;<samp>live</samp>&rsquo;</span></dt>
<dd><p>Allow live-friendly file generation.
</p></dd>
</dl>

</dd>
<dt><span><samp>segment_list_size <var>size</var></samp></span></dt>
<dd><p>Update the list file so that it contains at most <var>size</var>
segments. If 0 the list file will contain all the segments. Default
value is 0.
</p>
</dd>
<dt><span><samp>segment_list_entry_prefix <var>prefix</var></samp></span></dt>
<dd><p>Prepend <var>prefix</var> to each entry. Useful to generate absolute paths.
By default no prefix is applied.
</p>
</dd>
<dt><span><samp>segment_list_type <var>type</var></samp></span></dt>
<dd><p>Select the listing format.
</p>
<p>The following values are recognized:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>flat</samp>&rsquo;</span></dt>
<dd><p>Generate a flat list for the created segments, one segment per line.
</p>
</dd>
<dt><span>&lsquo;<samp>csv, ext</samp>&rsquo;</span></dt>
<dd><p>Generate a list for the created segments, one segment per line,
each line matching the format (comma-separated values):
</p><div class="example">
<pre class="example"><var>segment_filename</var>,<var>segment_start_time</var>,<var>segment_end_time</var>
</pre></div>

<p><var>segment_filename</var> is the name of the output file generated by the
muxer according to the provided pattern. CSV escaping (according to
RFC4180) is applied if required.
</p>
<p><var>segment_start_time</var> and <var>segment_end_time</var> specify
the segment start and end time expressed in seconds.
</p>
<p>A list file with the suffix <code>&quot;.csv&quot;</code> or <code>&quot;.ext&quot;</code> will
auto-select this format.
</p>
<p>&lsquo;<samp>ext</samp>&rsquo; is deprecated in favor or &lsquo;<samp>csv</samp>&rsquo;.
</p>
</dd>
<dt><span>&lsquo;<samp>ffconcat</samp>&rsquo;</span></dt>
<dd><p>Generate an ffconcat file for the created segments. The resulting file
can be read using the FFmpeg <a href="#concat">concat</a> demuxer.
</p>
<p>A list file with the suffix <code>&quot;.ffcat&quot;</code> or <code>&quot;.ffconcat&quot;</code> will
auto-select this format.
</p>
</dd>
<dt><span>&lsquo;<samp>m3u8</samp>&rsquo;</span></dt>
<dd><p>Generate an extended M3U8 file, version 3, compliant with
<a href="http://tools.ietf.org/id/draft-pantos-http-live-streaming">http://tools.ietf.org/id/draft-pantos-http-live-streaming</a>.
</p>
<p>A list file with the suffix <code>&quot;.m3u8&quot;</code> will auto-select this format.
</p></dd>
</dl>

<p>If not specified the type is guessed from the list file name suffix.
</p>
</dd>
<dt><span><samp>segment_time <var>time</var></samp></span></dt>
<dd><p>Set segment duration to <var>time</var>, the value must be a duration
specification. Default value is &quot;2&quot;. See also the
<samp>segment_times</samp> option.
</p>
<p>Note that splitting may not be accurate, unless you force the
reference stream key-frames at the given time. See the introductory
notice and the examples below.
</p>
</dd>
<dt><span><samp>segment_atclocktime <var>1|0</var></samp></span></dt>
<dd><p>If set to &quot;1&quot; split at regular clock time intervals starting from 00:00
o&rsquo;clock. The <var>time</var> value specified in <samp>segment_time</samp> is
used for setting the length of the splitting interval.
</p>
<p>For example with <samp>segment_time</samp> set to &quot;900&quot; this makes it possible
to create files at 12:00 o&rsquo;clock, 12:15, 12:30, etc.
</p>
<p>Default value is &quot;0&quot;.
</p>
</dd>
<dt><span><samp>segment_clocktime_offset <var>duration</var></samp></span></dt>
<dd><p>Delay the segment splitting times with the specified duration when using
<samp>segment_atclocktime</samp>.
</p>
<p>For example with <samp>segment_time</samp> set to &quot;900&quot; and
<samp>segment_clocktime_offset</samp> set to &quot;300&quot; this makes it possible to
create files at 12:05, 12:20, 12:35, etc.
</p>
<p>Default value is &quot;0&quot;.
</p>
</dd>
<dt><span><samp>segment_clocktime_wrap_duration <var>duration</var></samp></span></dt>
<dd><p>Force the segmenter to only start a new segment if a packet reaches the muxer
within the specified duration after the segmenting clock time. This way you
can make the segmenter more resilient to backward local time jumps, such as
leap seconds or transition to standard time from daylight savings time.
</p>
<p>Default is the maximum possible duration which means starting a new segment
regardless of the elapsed time since the last clock time.
</p>
</dd>
<dt><span><samp>segment_time_delta <var>delta</var></samp></span></dt>
<dd><p>Specify the accuracy time when selecting the start time for a
segment, expressed as a duration specification. Default value is &quot;0&quot;.
</p>
<p>When delta is specified a key-frame will start a new segment if its
PTS satisfies the relation:
</p><div class="example">
<pre class="example">PTS &gt;= start_time - time_delta
</pre></div>

<p>This option is useful when splitting video content, which is always
split at GOP boundaries, in case a key frame is found just before the
specified split time.
</p>
<p>In particular may be used in combination with the <samp>ffmpeg</samp> option
<var>force_key_frames</var>. The key frame times specified by
<var>force_key_frames</var> may not be set accurately because of rounding
issues, with the consequence that a key frame time may result set just
before the specified time. For constant frame rate videos a value of
1/(2*<var>frame_rate</var>) should address the worst case mismatch between
the specified time and the time set by <var>force_key_frames</var>.
</p>
</dd>
<dt><span><samp>segment_times <var>times</var></samp></span></dt>
<dd><p>Specify a list of split points. <var>times</var> contains a list of comma
separated duration specifications, in increasing order. See also
the <samp>segment_time</samp> option.
</p>
</dd>
<dt><span><samp>segment_frames <var>frames</var></samp></span></dt>
<dd><p>Specify a list of split video frame numbers. <var>frames</var> contains a
list of comma separated integer numbers, in increasing order.
</p>
<p>This option specifies to start a new segment whenever a reference
stream key frame is found and the sequential number (starting from 0)
of the frame is greater or equal to the next value in the list.
</p>
</dd>
<dt><span><samp>segment_wrap <var>limit</var></samp></span></dt>
<dd><p>Wrap around segment index once it reaches <var>limit</var>.
</p>
</dd>
<dt><span><samp>segment_start_number <var>number</var></samp></span></dt>
<dd><p>Set the sequence number of the first segment. Defaults to <code>0</code>.
</p>
</dd>
<dt><span><samp>strftime <var>1|0</var></samp></span></dt>
<dd><p>Use the <code>strftime</code> function to define the name of the new
segments to write. If this is selected, the output segment name must
contain a <code>strftime</code> function template. Default value is
<code>0</code>.
</p>
</dd>
<dt><span><samp>break_non_keyframes <var>1|0</var></samp></span></dt>
<dd><p>If enabled, allow segments to start on frames other than keyframes. This
improves behavior on some players when the time between keyframes is
inconsistent, but may make things worse on others, and can cause some oddities
during seeking. Defaults to <code>0</code>.
</p>
</dd>
<dt><span><samp>reset_timestamps <var>1|0</var></samp></span></dt>
<dd><p>Reset timestamps at the beginning of each segment, so that each segment
will start with near-zero timestamps. It is meant to ease the playback
of the generated segments. May not work with some combinations of
muxers/codecs. It is set to <code>0</code> by default.
</p>
</dd>
<dt><span><samp>initial_offset <var>offset</var></samp></span></dt>
<dd><p>Specify timestamp offset to apply to the output packet timestamps. The
argument must be a time duration specification, and defaults to 0.
</p>
</dd>
<dt><span><samp>write_empty_segments <var>1|0</var></samp></span></dt>
<dd><p>If enabled, write an empty segment if there are no packets during the period a
segment would usually span. Otherwise, the segment will be filled with the next
packet written. Defaults to <code>0</code>.
</p></dd>
</dl>

<p>Make sure to require a closed GOP when encoding and to set the GOP
size to fit your segment time constraint.
</p>
<a name="Examples-10"></a>
<h4 class="subsection">4.30.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-10" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-10" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Remux the content of file <samp>in.mkv</samp> to a list of segments
<samp>out-000.nut</samp>, <samp>out-001.nut</samp>, etc., and write the list of
generated segments to <samp>out.list</samp>:
<div class="example">
<pre class="example">ffmpeg -i in.mkv -codec hevc -flags +cgop -g 60 -map 0 -f segment -segment_list out.list out%03d.nut
</pre></div>

</li><li> Segment input and set output format options for the output segments:
<div class="example">
<pre class="example">ffmpeg -i in.mkv -f segment -segment_time 10 -segment_format_options movflags=+faststart out%03d.mp4
</pre></div>

</li><li> Segment the input file according to the split points specified by the
<var>segment_times</var> option:
<div class="example">
<pre class="example">ffmpeg -i in.mkv -codec copy -map 0 -f segment -segment_list out.csv -segment_times 1,2,3,5,8,13,21 out%03d.nut
</pre></div>

</li><li> Use the <code>ffmpeg</code> <samp>force_key_frames</samp>
option to force key frames in the input at the specified location, together
with the segment option <samp>segment_time_delta</samp> to account for
possible roundings operated when setting key frame times.
<div class="example">
<pre class="example">ffmpeg -i in.mkv -force_key_frames 1,2,3,5,8,13,21 -codec:v mpeg4 -codec:a pcm_s16le -map 0 \
-f segment -segment_list out.csv -segment_times 1,2,3,5,8,13,21 -segment_time_delta 0.05 out%03d.nut
</pre></div>
<p>In order to force key frames on the input file, transcoding is
required.
</p>
</li><li> Segment the input file by splitting the input file according to the
frame numbers sequence specified with the <samp>segment_frames</samp> option:
<div class="example">
<pre class="example">ffmpeg -i in.mkv -codec copy -map 0 -f segment -segment_list out.csv -segment_frames 100,200,300,500,800 out%03d.nut
</pre></div>

</li><li> Convert the <samp>in.mkv</samp> to TS segments using the <code>libx264</code>
and <code>aac</code> encoders:
<div class="example">
<pre class="example">ffmpeg -i in.mkv -map 0 -codec:v libx264 -codec:a aac -f ssegment -segment_list out.list out%03d.ts
</pre></div>

</li><li> Segment the input file, and create an M3U8 live playlist (can be used
as live HLS source):
<div class="example">
<pre class="example">ffmpeg -re -i in.mkv -codec copy -map 0 -f segment -segment_list playlist.m3u8 \
-segment_list_flags +live -segment_time 10 out%03d.mkv
</pre></div>
</li></ul>

<a name="smoothstreaming"></a>
<h3 class="section">4.31 smoothstreaming<span class="pull-right"><a class="anchor hidden-xs" href="#smoothstreaming" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-smoothstreaming" aria-hidden="true">TOC</a></span></h3>

<p>Smooth Streaming muxer generates a set of files (Manifest, chunks) suitable for serving with conventional web server.
</p>
<dl compact="compact">
<dt><span><samp>window_size</samp></span></dt>
<dd><p>Specify the number of fragments kept in the manifest. Default 0 (keep all).
</p>
</dd>
<dt><span><samp>extra_window_size</samp></span></dt>
<dd><p>Specify the number of fragments kept outside of the manifest before removing from disk. Default 5.
</p>
</dd>
<dt><span><samp>lookahead_count</samp></span></dt>
<dd><p>Specify the number of lookahead fragments. Default 2.
</p>
</dd>
<dt><span><samp>min_frag_duration</samp></span></dt>
<dd><p>Specify the minimum fragment duration (in microseconds). Default 5000000.
</p>
</dd>
<dt><span><samp>remove_at_exit</samp></span></dt>
<dd><p>Specify whether to remove all fragments when finished. Default 0 (do not remove).
</p>
</dd>
</dl>

<span id="streamhash"></span><a name="streamhash-1"></a>
<h3 class="section">4.32 streamhash<span class="pull-right"><a class="anchor hidden-xs" href="#streamhash-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-streamhash-1" aria-hidden="true">TOC</a></span></h3>

<p>Per stream hash testing format.
</p>
<p>This muxer computes and prints a cryptographic hash of all the input frames,
on a per-stream basis. This can be used for equality checks without having
to do a complete binary comparison.
</p>
<p>By default audio frames are converted to signed 16-bit raw audio and
video frames to raw video before computing the hash, but the output
of explicit conversions to other codecs can also be used. Timestamps
are ignored. It uses the SHA-256 cryptographic hash function by default,
but supports several other algorithms.
</p>
<p>The output of the muxer consists of one line per stream of the form:
<var>streamindex</var>,<var>streamtype</var>,<var>algo</var>=<var>hash</var>, where
<var>streamindex</var> is the index of the mapped stream, <var>streamtype</var> is a
single character indicating the type of stream, <var>algo</var> is a short string
representing the hash function used, and <var>hash</var> is a hexadecimal number
representing the computed hash.
</p>
<dl compact="compact">
<dt><span><samp>hash <var>algorithm</var></samp></span></dt>
<dd><p>Use the cryptographic hash function specified by the string <var>algorithm</var>.
Supported values include <code>MD5</code>, <code>murmur3</code>, <code>RIPEMD128</code>,
<code>RIPEMD160</code>, <code>RIPEMD256</code>, <code>RIPEMD320</code>, <code>SHA160</code>,
<code>SHA224</code>, <code>SHA256</code> (default), <code>SHA512/224</code>, <code>SHA512/256</code>,
<code>SHA384</code>, <code>SHA512</code>, <code>CRC32</code> and <code>adler32</code>.
</p>
</dd>
</dl>

<a name="Examples-11"></a>
<h4 class="subsection">4.32.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-11" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-11" aria-hidden="true">TOC</a></span></h4>

<p>To compute the SHA-256 hash of the input converted to raw audio and
video, and store it in the file <samp>out.sha256</samp>:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f streamhash out.sha256
</pre></div>

<p>To print an MD5 hash to stdout use the command:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f streamhash -hash md5 -
</pre></div>

<p>See also the <a href="#hash">hash</a> and <a href="#framehash">framehash</a> muxers.
</p>
<span id="tee"></span><a name="tee-1"></a>
<h3 class="section">4.33 tee<span class="pull-right"><a class="anchor hidden-xs" href="#tee-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-tee-1" aria-hidden="true">TOC</a></span></h3>

<p>The tee muxer can be used to write the same data to several outputs, such as files or streams.
It can be used, for example, to stream a video over a network and save it to disk at the same time.
</p>
<p>It is different from specifying several outputs to the <code>ffmpeg</code>
command-line tool. With the tee muxer, the audio and video data will be encoded only once.
With conventional multiple outputs, multiple encoding operations in parallel are initiated,
which can be a very expensive process. The tee muxer is not useful when using the libavformat API
directly because it is then possible to feed the same packets to several muxers directly.
</p>
<p>Since the tee muxer does not represent any particular output format, ffmpeg cannot auto-select
output streams. So all streams intended for output must be specified using <code>-map</code>. See
the examples below.
</p>
<p>Some encoders may need different options depending on the output format;
the auto-detection of this can not work with the tee muxer, so they need to be explicitly specified.
The main example is the <samp>global_header</samp> flag.
</p>
<p>The slave outputs are specified in the file name given to the muxer,
separated by &rsquo;|&rsquo;. If any of the slave name contains the &rsquo;|&rsquo; separator,
leading or trailing spaces or any special character, those must be
escaped (see <a data-manual="ffmpeg-utils" href="ffmpeg-utils.html#quoting_005fand_005fescaping">(ffmpeg-utils)the &quot;Quoting and escaping&quot;
section in the ffmpeg-utils(1) manual</a>).
</p>
<a name="Options-16"></a>
<h4 class="subsection">4.33.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-16" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-16" aria-hidden="true">TOC</a></span></h4>

<dl compact="compact">
<dt><span><samp>use_fifo <var>bool</var></samp></span></dt>
<dd><p>If set to 1, slave outputs will be processed in separate threads using the <a href="#fifo">fifo</a>
muxer. This allows to compensate for different speed/latency/reliability of
outputs and setup transparent recovery. By default this feature is turned off.
</p>
</dd>
<dt><span><samp>fifo_options</samp></span></dt>
<dd><p>Options to pass to fifo pseudo-muxer instances. See <a href="#fifo">fifo</a>.
</p>
</dd>
</dl>

<p>Muxer options can be specified for each slave by prepending them as a list of
<var>key</var>=<var>value</var> pairs separated by &rsquo;:&rsquo;, between square brackets. If
the options values contain a special character or the &rsquo;:&rsquo; separator, they
must be escaped; note that this is a second level escaping.
</p>
<p>The following special options are also recognized:
</p><dl compact="compact">
<dt><span><samp>f</samp></span></dt>
<dd><p>Specify the format name. Required if it cannot be guessed from the
output URL.
</p>
</dd>
<dt><span><samp>bsfs[/<var>spec</var>]</samp></span></dt>
<dd><p>Specify a list of bitstream filters to apply to the specified
output.
</p>
<p>It is possible to specify to which streams a given bitstream filter
applies, by appending a stream specifier to the option separated by
<code>/</code>. <var>spec</var> must be a stream specifier (see <a href="#Format-stream-specifiers">Format stream specifiers</a>).
</p>
<p>If the stream specifier is not specified, the bitstream filters will be
applied to all streams in the output. This will cause that output operation
to fail if the output contains streams to which the bitstream filter cannot
be applied e.g. <code>h264_mp4toannexb</code> being applied to an output containing an audio stream.
</p>
<p>Options for a bitstream filter must be specified in the form of <code>opt=value</code>.
</p>
<p>Several bitstream filters can be specified, separated by &quot;,&quot;.
</p>
</dd>
<dt><span><samp>use_fifo <var>bool</var></samp></span></dt>
<dd><p>This allows to override tee muxer use_fifo option for individual slave muxer.
</p>
</dd>
<dt><span><samp>fifo_options</samp></span></dt>
<dd><p>This allows to override tee muxer fifo_options for individual slave muxer.
See <a href="#fifo">fifo</a>.
</p>
</dd>
<dt><span><samp>select</samp></span></dt>
<dd><p>Select the streams that should be mapped to the slave output,
specified by a stream specifier. If not specified, this defaults to
all the mapped streams. This will cause that output operation to fail
if the output format does not accept all mapped streams.
</p>
<p>You may use multiple stream specifiers separated by commas (<code>,</code>) e.g.: <code>a:0,v</code>
</p>
</dd>
<dt><span><samp>onfail</samp></span></dt>
<dd><p>Specify behaviour on output failure. This can be set to either <code>abort</code> (which is
default) or <code>ignore</code>. <code>abort</code> will cause whole process to fail in case of failure
on this slave output. <code>ignore</code> will ignore failure on this output, so other outputs
will continue without being affected.
</p></dd>
</dl>

<a name="Examples-12"></a>
<h4 class="subsection">4.33.2 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples-12" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples-12" aria-hidden="true">TOC</a></span></h4>

<ul>
<li> Encode something and both archive it in a WebM file and stream it
as MPEG-TS over UDP:
<div class="example">
<pre class="example">ffmpeg -i ... -c:v libx264 -c:a mp2 -f tee -map 0:v -map 0:a
  &quot;archive-20121107.mkv|[f=mpegts]udp://*********5:1234/&quot;
</pre></div>

</li><li> As above, but continue streaming even if output to local file fails
(for example local drive fills up):
<div class="example">
<pre class="example">ffmpeg -i ... -c:v libx264 -c:a mp2 -f tee -map 0:v -map 0:a
  &quot;[onfail=ignore]archive-20121107.mkv|[f=mpegts]udp://*********5:1234/&quot;
</pre></div>

</li><li> Use <code>ffmpeg</code> to encode the input, and send the output
to three different destinations. The <code>dump_extra</code> bitstream
filter is used to add extradata information to all the output video
keyframes packets, as requested by the MPEG-TS format. The select
option is applied to <samp>out.aac</samp> in order to make it contain only
audio packets.
<div class="example">
<pre class="example">ffmpeg -i ... -map 0 -flags +global_header -c:v libx264 -c:a aac
       -f tee &quot;[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=a]out.aac&quot;
</pre></div>

</li><li> As above, but select only stream <code>a:1</code> for the audio output. Note
that a second level escaping must be performed, as &quot;:&quot; is a special
character used to separate options.
<div class="example">
<pre class="example">ffmpeg -i ... -map 0 -flags +global_header -c:v libx264 -c:a aac
       -f tee &quot;[bsfs/v=dump_extra=freq=keyframe]out.ts|[movflags=+faststart]out.mp4|[select=\'a:1\']out.aac&quot;
</pre></div>
</li></ul>

<a name="webm_005fchunk"></a>
<h3 class="section">4.34 webm_chunk<span class="pull-right"><a class="anchor hidden-xs" href="#webm_005fchunk" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-webm_005fchunk" aria-hidden="true">TOC</a></span></h3>

<p>WebM Live Chunk Muxer.
</p>
<p>This muxer writes out WebM headers and chunks as separate files which can be
consumed by clients that support WebM Live streams via DASH.
</p>
<a name="Options-17"></a>
<h4 class="subsection">4.34.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-17" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-17" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>chunk_start_index</samp></span></dt>
<dd><p>Index of the first chunk (defaults to 0).
</p>
</dd>
<dt><span><samp>header</samp></span></dt>
<dd><p>Filename of the header where the initialization data will be written.
</p>
</dd>
<dt><span><samp>audio_chunk_duration</samp></span></dt>
<dd><p>Duration of each audio chunk in milliseconds (defaults to 5000).
</p></dd>
</dl>

<a name="Example-2"></a>
<h4 class="subsection">4.34.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-2" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-2" aria-hidden="true">TOC</a></span></h4>
<div class="example">
<pre class="example">ffmpeg -f v4l2 -i /dev/video0 \
       -f alsa -i hw:0 \
       -map 0:0 \
       -c:v libvpx-vp9 \
       -s 640x360 -keyint_min 30 -g 30 \
       -f webm_chunk \
       -header webm_live_video_360.hdr \
       -chunk_start_index 1 \
       webm_live_video_360_%d.chk \
       -map 1:0 \
       -c:a libvorbis \
       -b:a 128k \
       -f webm_chunk \
       -header webm_live_audio_128.hdr \
       -chunk_start_index 1 \
       -audio_chunk_duration 1000 \
       webm_live_audio_128_%d.chk
</pre></div>

<a name="webm_005fdash_005fmanifest"></a>
<h3 class="section">4.35 webm_dash_manifest<span class="pull-right"><a class="anchor hidden-xs" href="#webm_005fdash_005fmanifest" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-webm_005fdash_005fmanifest" aria-hidden="true">TOC</a></span></h3>

<p>WebM DASH Manifest muxer.
</p>
<p>This muxer implements the WebM DASH Manifest specification to generate the DASH
manifest XML. It also supports manifest generation for DASH live streams.
</p>
<p>For more information see:
</p>
<ul>
<li> WebM DASH Specification: <a href="https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification">https://sites.google.com/a/webmproject.org/wiki/adaptive-streaming/webm-dash-specification</a>
</li><li> ISO DASH Specification: <a href="http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip">http://standards.iso.org/ittf/PubliclyAvailableStandards/c065274_ISO_IEC_23009-1_2014.zip</a>
</li></ul>

<a name="Options-18"></a>
<h4 class="subsection">4.35.1 Options<span class="pull-right"><a class="anchor hidden-xs" href="#Options-18" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Options-18" aria-hidden="true">TOC</a></span></h4>

<p>This muxer supports the following options:
</p>
<dl compact="compact">
<dt><span><samp>adaptation_sets</samp></span></dt>
<dd><p>This option has the following syntax: &quot;id=x,streams=a,b,c id=y,streams=d,e&quot; where x and y are the
unique identifiers of the adaptation sets and a,b,c,d and e are the indices of the corresponding
audio and video streams. Any number of adaptation sets can be added using this option.
</p>
</dd>
<dt><span><samp>live</samp></span></dt>
<dd><p>Set this to 1 to create a live stream DASH Manifest. Default: 0.
</p>
</dd>
<dt><span><samp>chunk_start_index</samp></span></dt>
<dd><p>Start index of the first chunk. This will go in the &lsquo;<samp>startNumber</samp>&rsquo; attribute
of the &lsquo;<samp>SegmentTemplate</samp>&rsquo; element in the manifest. Default: 0.
</p>
</dd>
<dt><span><samp>chunk_duration_ms</samp></span></dt>
<dd><p>Duration of each chunk in milliseconds. This will go in the &lsquo;<samp>duration</samp>&rsquo;
attribute of the &lsquo;<samp>SegmentTemplate</samp>&rsquo; element in the manifest. Default: 1000.
</p>
</dd>
<dt><span><samp>utc_timing_url</samp></span></dt>
<dd><p>URL of the page that will return the UTC timestamp in ISO format. This will go
in the &lsquo;<samp>value</samp>&rsquo; attribute of the &lsquo;<samp>UTCTiming</samp>&rsquo; element in the manifest.
Default: None.
</p>
</dd>
<dt><span><samp>time_shift_buffer_depth</samp></span></dt>
<dd><p>Smallest time (in seconds) shifting buffer for which any Representation is
guaranteed to be available. This will go in the &lsquo;<samp>timeShiftBufferDepth</samp>&rsquo;
attribute of the &lsquo;<samp>MPD</samp>&rsquo; element. Default: 60.
</p>
</dd>
<dt><span><samp>minimum_update_period</samp></span></dt>
<dd><p>Minimum update period (in seconds) of the manifest. This will go in the
&lsquo;<samp>minimumUpdatePeriod</samp>&rsquo; attribute of the &lsquo;<samp>MPD</samp>&rsquo; element. Default: 0.
</p>
</dd>
</dl>

<a name="Example-3"></a>
<h4 class="subsection">4.35.2 Example<span class="pull-right"><a class="anchor hidden-xs" href="#Example-3" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Example-3" aria-hidden="true">TOC</a></span></h4>
<div class="example">
<pre class="example">ffmpeg -f webm_dash_manifest -i video1.webm \
       -f webm_dash_manifest -i video2.webm \
       -f webm_dash_manifest -i audio1.webm \
       -f webm_dash_manifest -i audio2.webm \
       -map 0 -map 1 -map 2 -map 3 \
       -c copy \
       -f webm_dash_manifest \
       -adaptation_sets &quot;id=0,streams=0,1 id=1,streams=2,3&quot; \
       manifest.xml
</pre></div>

<a name="Metadata-1"></a>
<h2 class="chapter">5 Metadata<span class="pull-right"><a class="anchor hidden-xs" href="#Metadata-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Metadata-1" aria-hidden="true">TOC</a></span></h2>

<p>FFmpeg is able to dump metadata from media files into a simple UTF-8-encoded
INI-like text file and then load it back using the metadata muxer/demuxer.
</p>
<p>The file format is as follows:
</p><ol>
<li> A file consists of a header and a number of metadata tags divided into sections,
each on its own line.

</li><li> The header is a &lsquo;<samp>;FFMETADATA</samp>&rsquo; string, followed by a version number (now 1).

</li><li> Metadata tags are of the form &lsquo;<samp>key=value</samp>&rsquo;

</li><li> Immediately after header follows global metadata

</li><li> After global metadata there may be sections with per-stream/per-chapter
metadata.

</li><li> A section starts with the section name in uppercase (i.e. STREAM or CHAPTER) in
brackets (&lsquo;<samp>[</samp>&rsquo;, &lsquo;<samp>]</samp>&rsquo;) and ends with next section or end of file.

</li><li> At the beginning of a chapter section there may be an optional timebase to be
used for start/end values. It must be in form
&lsquo;<samp>TIMEBASE=<var>num</var>/<var>den</var></samp>&rsquo;, where <var>num</var> and <var>den</var> are
integers. If the timebase is missing then start/end times are assumed to
be in nanoseconds.

<p>Next a chapter section must contain chapter start and end times in form
&lsquo;<samp>START=<var>num</var></samp>&rsquo;, &lsquo;<samp>END=<var>num</var></samp>&rsquo;, where <var>num</var> is a positive
integer.
</p>
</li><li> Empty lines and lines starting with &lsquo;<samp>;</samp>&rsquo; or &lsquo;<samp>#</samp>&rsquo; are ignored.

</li><li> Metadata keys or values containing special characters (&lsquo;<samp>=</samp>&rsquo;, &lsquo;<samp>;</samp>&rsquo;,
&lsquo;<samp>#</samp>&rsquo;, &lsquo;<samp>\</samp>&rsquo; and a newline) must be escaped with a backslash &lsquo;<samp>\</samp>&rsquo;.

</li><li> Note that whitespace in metadata (e.g. &lsquo;<samp>foo = bar</samp>&rsquo;) is considered to be
a part of the tag (in the example above key is &lsquo;<samp>foo </samp>&rsquo;, value is
&lsquo;<samp> bar</samp>&rsquo;).
</li></ol>

<p>A ffmetadata file might look like this:
</p><div class="example">
<pre class="example">;FFMETADATA1
title=bike\\shed
;this is a comment
artist=FFmpeg troll team

[CHAPTER]
TIMEBASE=1/1000
START=0
#chapter ends at 0:01:00
END=60000
title=chapter \#1
[STREAM]
title=multi\
line
</pre></div>

<p>By using the ffmetadata muxer and demuxer it is possible to extract
metadata from an input file to an ffmetadata file, and then transcode
the file into an output file with the edited ffmetadata file.
</p>
<p>Extracting an ffmetadata file with <samp>ffmpeg</samp> goes as follows:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -f ffmetadata FFMETADATAFILE
</pre></div>

<p>Reinserting edited metadata information from the FFMETADATAFILE file can
be done as:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -i FFMETADATAFILE -map_metadata 1 -codec copy OUTPUT
</pre></div>


<a name="See-Also"></a>
<h2 class="chapter">6 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>,
<a href="libavformat.html">libavformat</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">7 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a href="https://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
