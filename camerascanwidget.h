#ifndef CAMERASCANWIDGET_H
#define CAMERASCANWIDGET_H

#include <QWidget>
#include <QLabel>
// #include <QProgressBar>
#include <QVBoxLayout>
#include <QTableWidget>
#include <QMenu>
#include <QTimer>
#include <QPushButton>
#include <QHBoxLayout>
#include "camerascanner.h"

// 前向声明
namespace Ui
{
    class CameraScanWidget;
}

class CameraScanWidget : public QWidget
{
    Q_OBJECT

public:
    explicit CameraScanWidget(QWidget *parent = nullptr);
    ~CameraScanWidget();

    // 获取当前选中的RTSP URL
    QString getSelectedRtspUrl() const;

signals:
    // 当选中摄像头时发出信号
    void cameraSelected(const QString &rtspUrl);

    // 当取消选中摄像头时发出信号
    void cameraDeselected(const QString &rtspUrl);

    // 当摄像头名称更新时发出信号
    void cameraNameUpdated(const QString &rtspUrl, const QString &newName);

protected:
    // 窗口大小调整事件
    void resizeEvent(QResizeEvent *event) override;

    // 事件过滤器，用于临时阻止用户交互
    bool eventFilter(QObject *watched, QEvent *event) override;

private slots:
    // 扫描进度更新
    void onScanProgress(int current, int total);

    // 发现摄像头
    void onCameraFound(const CameraInfo &camera);

    // 扫描完成
    void onScanCompleted();

    // 摄像头列表选择变化
    void onCameraListSelectionChanged();

    // 显示右键菜单
    void showContextMenu(const QPoint &pos);

    // 重新扫描
    void rescan();

    // 处理操作按钮点击
    void onOperationButtonClicked(int state);

    // 处理表格项编辑完成
    void onItemChanged(QTableWidgetItem *item);

private:
    // 初始化UI
    void initUI();

    // 开始扫描
    void startScanning();

    // 从数据库加载摄像头
    void loadCamerasFromDatabase();

    // 添加摄像头到表格
    void addCameraToTable(const CameraInfo &camera);

    // 更新UI状态
    void updateUIState(bool scanning);

    // 更新摄像头状态
    void updateCameraStatus(int row, const QString &status);

    // UI指针
    Ui::CameraScanWidget *ui;

    // 扫描器
    CameraScanner *m_scanner;

    // 扫描状态
    bool m_isScanning;

    // 交互阻止标志
    bool m_blockInteraction = false;

    // 当前扫描到的活跃IP地址列表
    QList<QString> m_activeIpAddresses;

    // 右键菜单
    QMenu *m_contextMenu;

    // 表格列索引
    enum ColumnIndex
    {
        NameColumn = 0,
        IpColumn = 1,
        StatusColumn = 2,
        OperationColumn = 3
    };
};

#endif // CAMERASCANWIDGET_H