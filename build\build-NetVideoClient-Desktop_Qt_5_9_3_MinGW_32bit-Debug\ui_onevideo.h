/********************************************************************************
** Form generated from reading UI file 'onevideo.ui'
**
** Created by: Qt User Interface Compiler version 5.9.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ONEVIDEO_H
#define UI_ONEVIDEO_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_OneVideo
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *nameLabel;
    QSpacerItem *verticalSpacer;

    void setupUi(QFrame *OneVideo)
    {
        if (OneVideo->objectName().isEmpty())
            OneVideo->setObjectName(QStringLiteral("OneVideo"));
        OneVideo->resize(427, 240);
        OneVideo->setStyleSheet(QStringLiteral("QFrame { background: transparent; margin: 3px; }"));
        OneVideo->setMouseTracking(true);
        verticalLayout = new QVBoxLayout(OneVideo);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        nameLabel = new QLabel(OneVideo);
        nameLabel->setObjectName(QStringLiteral("nameLabel"));
        QFont font;
        font.setFamily(QStringLiteral("Microsoft YaHei"));
        font.setPointSize(10);
        font.setBold(true);
        font.setWeight(75);
        nameLabel->setFont(font);
        nameLabel->setStyleSheet(QStringLiteral("color: white; background-color: rgba(0, 0, 0, 150); padding: 2px 8px; border-radius: 3px; border: 1px solid rgba(255, 255, 255, 100);"));
        nameLabel->setAlignment(Qt::AlignCenter);
        nameLabel->setMinimumSize(QSize(0, 25));
        nameLabel->setMaximumSize(QSize(16777215, 25));

        verticalLayout->addWidget(nameLabel);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);


        retranslateUi(OneVideo);

        QMetaObject::connectSlotsByName(OneVideo);
    } // setupUi

    void retranslateUi(QFrame *OneVideo)
    {
        nameLabel->setText(QString());
        Q_UNUSED(OneVideo);
    } // retranslateUi

};

namespace Ui {
    class OneVideo: public Ui_OneVideo {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ONEVIDEO_H
