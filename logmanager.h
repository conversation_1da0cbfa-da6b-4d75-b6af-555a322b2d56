#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QFile>
#include <QTextStream>
#include <QMutex>
#include <QDateTime>
#include <QString>
#include <QStringBuilder>
#include <QThread>
#include <QByteArray>
#include <QVariant>
#include <QThread>

class LogManager : public QObject
{
    Q_OBJECT

public:
    enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Fatal = 4
    };

    static LogManager &instance();

    // 初始化日志系统
    bool init();

    // 设置日志级别
    void setLogLevel(LogLevel level);

    // 获取当前日志级别
    LogLevel getLogLevel() const;

    // 写入日志
    void writeLog(LogLevel level, const QString &message);

    // 关闭日志
    void close();

    // 设置日志文件名格式
    void setLogFileName(const QString &format);

    // 获取日志文件名格式
    QString getLogFileName() const;

private:
    explicit LogManager(QObject *parent = nullptr);
    ~LogManager();

    // 禁止复制
    LogManager(const LogManager &) = delete;
    LogManager &operator=(const LogManager &) = delete;

    // 获取日志级别对应的字符串
    QString levelToString(LogLevel level);

    // 创建日志目录
    bool createLogDir();

    // 创建新的日志文件
    bool createLogFile();

    // 获取当前日志文件名
    QString getCurrentLogFileName() const;

    QFile m_logFile;
    QTextStream m_textStream;
    QMutex m_mutex;
    LogLevel m_logLevel;
    QString m_logDir;
    QString m_currentDate;
    QString m_logFileNameFormat;
};

// 用于流式日志的辅助类
class LogStream
{
public:
    LogStream(LogManager::LogLevel level) : m_level(level) {}
    ~LogStream()
    {
        try
        {
            LogManager::instance().writeLog(m_level, m_buffer);
        }
        catch (...)
        {
            // 忽略异常，防止析构函数抛出异常
        }
    }

    // 基本类型
    LogStream &operator<<(bool value)
    {
        m_buffer.append(value ? "true" : "false");
        return *this;
    }
    LogStream &operator<<(char value)
    {
        m_buffer.append(QString(value));
        return *this;
    }
    LogStream &operator<<(signed short value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(unsigned short value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(signed int value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(unsigned int value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(signed long value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(unsigned long value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(qint64 value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(quint64 value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(float value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(double value)
    {
        m_buffer.append(QString::number(value));
        return *this;
    }
    LogStream &operator<<(const char *value)
    {
        m_buffer.append(QString::fromUtf8(value));
        return *this;
    }

    // Qt类型
    LogStream &operator<<(const QString &value)
    {
        m_buffer.append(value);
        return *this;
    }
    LogStream &operator<<(const QStringRef &value)
    {
        m_buffer.append(value.toString());
        return *this;
    }
    LogStream &operator<<(const QByteArray &value)
    {
        m_buffer.append(QString::fromUtf8(value));
        return *this;
    }
    LogStream &operator<<(const QVariant &value)
    {
        m_buffer.append(value.toString());
        return *this;
    }

    // 特化处理QThread*类型
    LogStream &operator<<(QThread *thread)
    {
        if (thread)
        {
            m_buffer.append(QString("QThread(%1)").arg(reinterpret_cast<quint64>(thread)));
        }
        else
        {
            m_buffer.append("QThread(null)");
        }
        return *this;
    }

    // 特化处理const QThread*类型
    LogStream &operator<<(const QThread *thread)
    {
        if (thread)
        {
            m_buffer.append(QString("QThread(%1)").arg(reinterpret_cast<quint64>(thread)));
        }
        else
        {
            m_buffer.append("QThread(null)");
        }
        return *this;
    }

    // 特化处理QThread::currentThread()
    LogStream &operator<<(QThread &thread)
    {
        m_buffer.append(QString("QThread(%1)").arg(reinterpret_cast<quint64>(&thread)));
        return *this;
    }

    // 特化处理线程ID (QThread::currentThreadId() 返回的类型)
    LogStream &operator<<(Qt::HANDLE threadId)
    {
        m_buffer.append(QString("ThreadID(0x%1)").arg(reinterpret_cast<quint64>(threadId), 0, 16));
        return *this;
    }

    // 指针类型特化
    LogStream &operator<<(const void *value)
    {
        if (value)
        {
            m_buffer.append(QString("0x%1").arg(reinterpret_cast<quint64>(value), 0, 16));
        }
        else
        {
            m_buffer.append("nullptr");
        }
        return *this;
    }

    // 通用模板方法 - 最低优先级
    template <typename T>
    LogStream &operator<<(const T &value)
    {
        try
        {
            m_buffer.append(QString("%1").arg(value));
        }
        catch (...)
        {
            m_buffer.append("[Error formatting value]");
        }
        return *this;
    }

private:
    LogManager::LogLevel m_level;
    QString m_buffer;
};

// 定义宏用于简化日志调用
#define LOG_DEBUG(msg) LogStream(LogManager::Debug) << msg
#define LOG_INFO(msg) LogStream(LogManager::Info) << msg
#define LOG_WARNING(msg) LogStream(LogManager::Warning) << msg
#define LOG_ERROR(msg) LogStream(LogManager::Error) << msg
#define LOG_FATAL(msg) LogStream(LogManager::Fatal) << msg

#endif // LOGMANAGER_H