[2025-08-11 09:05:49.812] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:07:56.387] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:08:16.589] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:38:24.246] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:47:38.431] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 10:36:05.502] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:09:31.057] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:36:52.596] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:40:54.773] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:42:06.423] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:42:06.891] [INFO] 应用程序启动
[2025-08-11 11:42:07.823] [INFO] camera name updated:rtsp://root:LzXa%232023*@192.168.0.178/stream=0->未命名摄像头1
[2025-08-11 11:42:07.824] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0 at row 0
[2025-08-11 11:42:07.829] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:07.831] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:08.324] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:08.326] [INFO] set camera name: 未命名摄像头1
[2025-08-11 11:42:08.326] [INFO] show camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:08.825] [DEBUG] MyThread start running in thread:QThread(70453104)
[2025-08-11 11:42:08.825] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:08.826] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:42:10.108] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 11:42:10.108] [DEBUG] 开始读取视频帧
[2025-08-11 11:42:10.112] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 11:42:11.153] [WARNING] 检测到视频延迟: 时间戳差异=700ms, 预期间隔=50ms
[2025-08-11 11:42:11.162] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 11:42:11.162] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 11:42:14.127] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:42:14.127] [INFO] Checking video 0, pointer: ThreadID(0x26c9aa0)
[2025-08-11 11:42:14.128] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:42:14.128] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:42:14.128] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x4330770)
[2025-08-11 11:42:14.128] [INFO] Current listening state: false
[2025-08-11 11:42:14.128] [INFO] Starting listening
[2025-08-11 11:42:14.128] [INFO] Audio listening started - unmuted
[2025-08-11 11:42:15.142] [DEBUG] 当前帧率: 16.1034 fps, 队列中帧数: 0
[2025-08-11 11:42:18.058] [INFO] Listen toggled via right panel
[2025-08-11 11:42:20.130] [DEBUG] 性能统计: 处理帧数=180, 丢弃帧数=13, 当前队列=0
[2025-08-11 11:42:20.199] [DEBUG] 当前帧率: 19.9723 fps, 队列中帧数: 0
[2025-08-11 11:42:22.479] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:42:22.480] [INFO] Checking video 0, pointer: ThreadID(0x26c9aa0)
[2025-08-11 11:42:22.480] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:42:22.481] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:42:22.481] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x4330770)
[2025-08-11 11:42:22.481] [INFO] Current listening state: true
[2025-08-11 11:42:22.481] [INFO] Stopping listening
[2025-08-11 11:42:28.218] [DEBUG] 当前帧率: 5.98653 fps, 队列中帧数: 0
[2025-08-11 11:45:52.015] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:45:52.496] [INFO] 应用程序启动
[2025-08-11 11:45:53.446] [INFO] camera name updated:rtsp://root:LzXa%232023*@192.168.0.178/stream=0->未命名摄像头1
[2025-08-11 11:45:53.447] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0 at row 0
[2025-08-11 11:45:53.450] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:53.450] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:53.950] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:53.955] [INFO] set camera name: 未命名摄像头1
[2025-08-11 11:45:53.955] [INFO] show camera: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:54.451] [DEBUG] MyThread start running in thread:QThread(*********)
[2025-08-11 11:45:54.451] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:54.451] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@192.168.0.178/stream=0
[2025-08-11 11:45:55.950] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 11:45:55.950] [DEBUG] 开始读取视频帧
[2025-08-11 11:45:55.951] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 11:45:56.991] [WARNING] 检测到视频延迟: 时间戳差异=666ms, 预期间隔=50ms
[2025-08-11 11:45:56.999] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 11:45:57.000] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 11:45:59.776] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:45:59.776] [INFO] Checking video 0, pointer: ThreadID(0x44c9aa0)
[2025-08-11 11:45:59.776] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:45:59.776] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:45:59.776] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x5fd06d0)
[2025-08-11 11:45:59.776] [INFO] Current listening state: false
[2025-08-11 11:45:59.776] [INFO] Starting listening
[2025-08-11 11:45:59.776] [INFO] Audio listening started - unmuted
[2025-08-11 11:46:01.015] [DEBUG] 当前帧率: 15.9953 fps, 队列中帧数: 0
[2025-08-11 11:46:05.955] [DEBUG] 性能统计: 处理帧数=180, 丢弃帧数=12, 当前队列=0
[2025-08-11 11:46:06.029] [DEBUG] 当前帧率: 20.1476 fps, 队列中帧数: 0
[2025-08-11 11:46:11.057] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 11:46:15.968] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:16.120] [DEBUG] 当前帧率: 19.9486 fps, 队列中帧数: 0
[2025-08-11 11:46:21.148] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 11:46:26.009] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:26.210] [DEBUG] 当前帧率: 19.9526 fps, 队列中帧数: 0
[2025-08-11 11:46:31.213] [DEBUG] 当前帧率: 19.988 fps, 队列中帧数: 0
[2025-08-11 11:46:36.027] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:36.237] [DEBUG] 当前帧率: 20.1075 fps, 队列中帧数: 0
[2025-08-11 11:46:41.304] [DEBUG] 当前帧率: 19.9329 fps, 队列中帧数: 0
[2025-08-11 11:46:46.032] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:46.329] [DEBUG] 当前帧率: 20.0995 fps, 队列中帧数: 0
[2025-08-11 11:46:51.334] [DEBUG] 当前帧率: 19.98 fps, 队列中帧数: 0
[2025-08-11 11:46:56.081] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:56.392] [DEBUG] 当前帧率: 19.9684 fps, 队列中帧数: 0
[2025-08-11 11:47:01.351] [INFO] Listen toggled via right panel
[2025-08-11 11:47:01.419] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
