[2025-08-11 09:05:49.812] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:07:56.387] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:08:16.589] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:38:24.246] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 09:47:38.431] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 10:36:05.502] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:09:31.057] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:36:52.596] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:40:54.773] [INFO] 日志系统初始化完成，日志级别：ERROR，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:42:06.423] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:42:06.891] [INFO] 应用程序启动
[2025-08-11 11:42:07.823] [INFO] camera name updated:rtsp://root:LzXa%232023*@*************/stream=0->未命名摄像头1
[2025-08-11 11:42:07.824] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@*************/stream=0 at row 0
[2025-08-11 11:42:07.829] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:07.831] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:08.324] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:08.326] [INFO] set camera name: 未命名摄像头1
[2025-08-11 11:42:08.326] [INFO] show camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:08.825] [DEBUG] MyThread start running in thread:QThread(70453104)
[2025-08-11 11:42:08.825] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:08.826] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:42:10.108] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 11:42:10.108] [DEBUG] 开始读取视频帧
[2025-08-11 11:42:10.112] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 11:42:11.153] [WARNING] 检测到视频延迟: 时间戳差异=700ms, 预期间隔=50ms
[2025-08-11 11:42:11.162] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 11:42:11.162] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 11:42:14.127] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:42:14.127] [INFO] Checking video 0, pointer: ThreadID(0x26c9aa0)
[2025-08-11 11:42:14.128] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:42:14.128] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:42:14.128] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x4330770)
[2025-08-11 11:42:14.128] [INFO] Current listening state: false
[2025-08-11 11:42:14.128] [INFO] Starting listening
[2025-08-11 11:42:14.128] [INFO] Audio listening started - unmuted
[2025-08-11 11:42:15.142] [DEBUG] 当前帧率: 16.1034 fps, 队列中帧数: 0
[2025-08-11 11:42:18.058] [INFO] Listen toggled via right panel
[2025-08-11 11:42:20.130] [DEBUG] 性能统计: 处理帧数=180, 丢弃帧数=13, 当前队列=0
[2025-08-11 11:42:20.199] [DEBUG] 当前帧率: 19.9723 fps, 队列中帧数: 0
[2025-08-11 11:42:22.479] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:42:22.480] [INFO] Checking video 0, pointer: ThreadID(0x26c9aa0)
[2025-08-11 11:42:22.480] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:42:22.481] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:42:22.481] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x4330770)
[2025-08-11 11:42:22.481] [INFO] Current listening state: true
[2025-08-11 11:42:22.481] [INFO] Stopping listening
[2025-08-11 11:42:28.218] [DEBUG] 当前帧率: 5.98653 fps, 队列中帧数: 0
[2025-08-11 11:45:52.015] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 11:45:52.496] [INFO] 应用程序启动
[2025-08-11 11:45:53.446] [INFO] camera name updated:rtsp://root:LzXa%232023*@*************/stream=0->未命名摄像头1
[2025-08-11 11:45:53.447] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@*************/stream=0 at row 0
[2025-08-11 11:45:53.450] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:53.450] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:53.950] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:53.955] [INFO] set camera name: 未命名摄像头1
[2025-08-11 11:45:53.955] [INFO] show camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:54.451] [DEBUG] MyThread start running in thread:QThread(*********)
[2025-08-11 11:45:54.451] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:54.451] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 11:45:55.950] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 11:45:55.950] [DEBUG] 开始读取视频帧
[2025-08-11 11:45:55.951] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 11:45:56.991] [WARNING] 检测到视频延迟: 时间戳差异=666ms, 预期间隔=50ms
[2025-08-11 11:45:56.999] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 11:45:57.000] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 11:45:59.776] [INFO] onListenClicked called, showList size: 1
[2025-08-11 11:45:59.776] [INFO] Checking video 0, pointer: ThreadID(0x44c9aa0)
[2025-08-11 11:45:59.776] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 11:45:59.776] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 11:45:59.776] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x5fd06d0)
[2025-08-11 11:45:59.776] [INFO] Current listening state: false
[2025-08-11 11:45:59.776] [INFO] Starting listening
[2025-08-11 11:45:59.776] [INFO] Audio listening started - unmuted
[2025-08-11 11:46:01.015] [DEBUG] 当前帧率: 15.9953 fps, 队列中帧数: 0
[2025-08-11 11:46:05.955] [DEBUG] 性能统计: 处理帧数=180, 丢弃帧数=12, 当前队列=0
[2025-08-11 11:46:06.029] [DEBUG] 当前帧率: 20.1476 fps, 队列中帧数: 0
[2025-08-11 11:46:11.057] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 11:46:15.968] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:16.120] [DEBUG] 当前帧率: 19.9486 fps, 队列中帧数: 0
[2025-08-11 11:46:21.148] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 11:46:26.009] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:26.210] [DEBUG] 当前帧率: 19.9526 fps, 队列中帧数: 0
[2025-08-11 11:46:31.213] [DEBUG] 当前帧率: 19.988 fps, 队列中帧数: 0
[2025-08-11 11:46:36.027] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:36.237] [DEBUG] 当前帧率: 20.1075 fps, 队列中帧数: 0
[2025-08-11 11:46:41.304] [DEBUG] 当前帧率: 19.9329 fps, 队列中帧数: 0
[2025-08-11 11:46:46.032] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:46.329] [DEBUG] 当前帧率: 20.0995 fps, 队列中帧数: 0
[2025-08-11 11:46:51.334] [DEBUG] 当前帧率: 19.98 fps, 队列中帧数: 0
[2025-08-11 11:46:56.081] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 11:46:56.392] [DEBUG] 当前帧率: 19.9684 fps, 队列中帧数: 0
[2025-08-11 11:47:01.351] [INFO] Listen toggled via right panel
[2025-08-11 11:47:01.419] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:01:10.039] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 12:01:10.556] [INFO] 应用程序启动
[2025-08-11 12:01:11.753] [INFO] camera name updated:rtsp://root:LzXa%232023*@*************/stream=0->未命名摄像头1
[2025-08-11 12:01:11.754] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@*************/stream=0 at row 0
[2025-08-11 12:01:11.756] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:11.757] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:12.248] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:12.250] [INFO] set camera name: 未命名摄像头1
[2025-08-11 12:01:12.250] [INFO] show camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:12.750] [DEBUG] MyThread start running in thread:QThread(*********)
[2025-08-11 12:01:12.750] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:12.751] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:01:14.267] [DEBUG] 找到视频流，索引: 0
[2025-08-11 12:01:14.267] [DEBUG] 找到音频流，索引: 1
[2025-08-11 12:01:14.268] [DEBUG] 音频解码器初始化成功, 音频流索引: 1, 编解码器: opus
[2025-08-11 12:01:14.384] [DEBUG] 音频输出初始化成功，采样率: 48000, 通道数: 2
[2025-08-11 12:01:14.384] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 12:01:14.384] [DEBUG] 开始读取视频帧
[2025-08-11 12:01:14.384] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 12:01:15.303] [WARNING] 检测到视频延迟: 时间戳差异=666ms, 预期间隔=50ms
[2025-08-11 12:01:15.314] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 12:01:15.314] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 12:01:19.428] [DEBUG] 当前帧率: 16.6534 fps, 队列中帧数: 0
[2025-08-11 12:01:20.009] [INFO] onListenClicked called, showList size: 1
[2025-08-11 12:01:20.009] [INFO] Checking video 0, pointer: ThreadID(0x8502660)
[2025-08-11 12:01:20.010] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 12:01:20.010] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 12:01:20.010] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x8631318)
[2025-08-11 12:01:20.010] [INFO] Current listening state: false
[2025-08-11 12:01:20.010] [INFO] Starting listening
[2025-08-11 12:01:20.010] [DEBUG] 音频取消静音
[2025-08-11 12:01:20.010] [INFO] Audio listening started - unmuted
[2025-08-11 12:01:20.026] [DEBUG] 音频帧解码成功，采样率: 48000, 通道数: 2
[2025-08-11 12:01:21.374] [INFO] Listen toggled via right panel
[2025-08-11 12:01:24.418] [DEBUG] 性能统计: 处理帧数=183, 丢弃帧数=12, 当前队列=0
[2025-08-11 12:01:24.436] [DEBUG] 当前帧率: 19.9681 fps, 队列中帧数: 0
[2025-08-11 12:01:29.455] [DEBUG] 当前帧率: 20.1235 fps, 队列中帧数: 0
[2025-08-11 12:01:34.423] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:01:34.518] [DEBUG] 当前帧率: 19.9486 fps, 队列中帧数: 0
[2025-08-11 12:01:39.545] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:01:44.447] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:01:44.626] [DEBUG] 当前帧率: 19.878 fps, 队列中帧数: 0
[2025-08-11 12:01:44.682] [INFO] onListenClicked called, showList size: 1
[2025-08-11 12:01:44.682] [INFO] Checking video 0, pointer: ThreadID(0x8502660)
[2025-08-11 12:01:44.682] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 12:01:44.682] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 12:01:44.682] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0x8631318)
[2025-08-11 12:01:44.682] [INFO] Current listening state: true
[2025-08-11 12:01:44.682] [INFO] Stopping listening
[2025-08-11 12:01:44.682] [INFO] stopListening called
[2025-08-11 12:01:44.682] [INFO] Updated listening state variables
[2025-08-11 12:01:44.682] [INFO] muteBtn is nullptr, skipping icon update
[2025-08-11 12:01:44.682] [INFO] Setting network thread to muted
[2025-08-11 12:01:44.683] [DEBUG] 音频静音
[2025-08-11 12:01:44.683] [INFO] Audio listening stopped - muted
[2025-08-11 12:01:44.683] [INFO] About to show message box
[2025-08-11 12:01:45.793] [INFO] Message box closed
[2025-08-11 12:01:45.793] [INFO] Listen toggled via right panel
[2025-08-11 12:06:32.926] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 12:06:33.449] [INFO] 应用程序启动
[2025-08-11 12:06:34.348] [INFO] camera name updated:rtsp://root:LzXa%232023*@*************/stream=0->未命名摄像头1
[2025-08-11 12:06:34.348] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@*************/stream=0 at row 0
[2025-08-11 12:06:34.350] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:34.351] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:34.847] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:34.850] [INFO] set camera name: 未命名摄像头1
[2025-08-11 12:06:34.850] [INFO] show camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:35.345] [DEBUG] MyThread start running in thread:QThread(169957136)
[2025-08-11 12:06:35.345] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:35.346] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:06:36.792] [DEBUG] 找到视频流，索引: 0
[2025-08-11 12:06:36.792] [DEBUG] 找到音频流，索引: 1
[2025-08-11 12:06:36.793] [DEBUG] 音频解码器初始化成功, 音频流索引: 1, 编解码器: opus
[2025-08-11 12:06:36.793] [DEBUG] 尝试初始化音频输出:
[2025-08-11 12:06:36.793] [DEBUG]   采样率: 48000
[2025-08-11 12:06:36.793] [DEBUG]   通道数: 2
[2025-08-11 12:06:36.793] [DEBUG]   音频格式: 8
[2025-08-11 12:06:36.909] [DEBUG] 音频输出初始化成功，采样率: 48000, 通道数: 2
[2025-08-11 12:06:36.909] [DEBUG] 音频输出状态: 3
[2025-08-11 12:06:36.909] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 0x0, 编解码器: h264
[2025-08-11 12:06:36.909] [DEBUG] 开始读取视频帧
[2025-08-11 12:06:36.909] [DEBUG] 视频帧率: 25 fps, 帧间隔: 40 ms
[2025-08-11 12:06:37.747] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 12:06:37.747] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 12:06:38.738] [INFO] onListenClicked called, showList size: 1
[2025-08-11 12:06:38.739] [INFO] Checking video 0, pointer: ThreadID(0xa0ec3a8)
[2025-08-11 12:06:38.739] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 12:06:38.739] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 12:06:38.740] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0xa215710)
[2025-08-11 12:06:38.740] [INFO] Current listening state: false
[2025-08-11 12:06:38.740] [INFO] Starting listening
[2025-08-11 12:06:38.740] [DEBUG] 音频取消静音
[2025-08-11 12:06:38.740] [INFO] Audio listening started - unmuted
[2025-08-11 12:06:38.746] [DEBUG] 音频输出状态: 3, 错误: 0
[2025-08-11 12:06:38.746] [DEBUG] 音频输出状态不是 ActiveState: 3
[2025-08-11 12:06:38.746] [DEBUG] 音频帧解码成功，采样率: 48000, 通道数: 2, 格式: 8
[2025-08-11 12:06:40.167] [INFO] Listen toggled via right panel
[2025-08-11 12:06:41.920] [DEBUG] 当前帧率: 15.7653 fps, 队列中帧数: 0
[2025-08-11 12:06:46.922] [DEBUG] 性能统计: 处理帧数=166, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:06:46.976] [DEBUG] 当前帧率: 17.4051 fps, 队列中帧数: 0
[2025-08-11 12:06:51.999] [DEBUG] 当前帧率: 16.3249 fps, 队列中帧数: 0
[2025-08-11 12:06:56.937] [DEBUG] 性能统计: 处理帧数=172, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:06:57.001] [DEBUG] 当前帧率: 17.9964 fps, 队列中帧数: 0
[2025-08-11 12:07:02.005] [DEBUG] 当前帧率: 18.389 fps, 队列中帧数: 0
[2025-08-11 12:15:13.836] [INFO] 日志系统初始化完成，日志级别：DEBUG，日志文件：E:/code/git/company/LiveCamera/build/build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release/release/logs/log_2025-08-11.txt
[2025-08-11 12:15:14.335] [INFO] 应用程序启动
[2025-08-11 12:15:15.248] [INFO] camera name updated:rtsp://root:LzXa%232023*@*************/stream=0->未命名摄像头1
[2025-08-11 12:15:15.249] [DEBUG] Adding checkbox for camera: rtsp://root:LzXa%232023*@*************/stream=0 at row 0
[2025-08-11 12:15:15.253] [DEBUG] Setting checkbox checked for camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:15.253] [DEBUG] 加载已勾选摄像头: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:15.750] [DEBUG] 发送已勾选摄像头信号: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:15.752] [INFO] set camera name: 未命名摄像头1
[2025-08-11 12:15:15.752] [INFO] show camera: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:16.255] [DEBUG] MyThread start running in thread:QThread(170432544)
[2025-08-11 12:15:16.256] [DEBUG] 使用FFmpeg处理RTSP流: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:16.260] [DEBUG] 初始化FFmpeg，RTSP URL: rtsp://root:LzXa%232023*@*************/stream=0
[2025-08-11 12:15:17.619] [DEBUG] 找到视频流，索引: 0
[2025-08-11 12:15:17.619] [DEBUG] 找到音频流，索引: 1
[2025-08-11 12:15:17.621] [DEBUG] 音频解码器初始化成功, 音频流索引: 1, 编解码器: opus
[2025-08-11 12:15:17.621] [DEBUG] 尝试初始化音频输出:
[2025-08-11 12:15:17.621] [DEBUG]   采样率: 48000
[2025-08-11 12:15:17.621] [DEBUG]   通道数: 2
[2025-08-11 12:15:17.621] [DEBUG]   音频格式: 8
[2025-08-11 12:15:17.733] [DEBUG] 音频输出初始化成功，采样率: 48000, 通道数: 2
[2025-08-11 12:15:17.733] [DEBUG] 音频输出状态: 3
[2025-08-11 12:15:17.733] [DEBUG] FFmpeg初始化成功, 视频流索引: 0, 分辨率: 1280x720, 编解码器: h264
[2025-08-11 12:15:17.733] [DEBUG] 开始读取视频帧
[2025-08-11 12:15:17.733] [DEBUG] 视频帧率: 20 fps, 帧间隔: 50 ms
[2025-08-11 12:15:18.642] [WARNING] 检测到视频延迟: 时间戳差异=666ms, 预期间隔=50ms
[2025-08-11 12:15:18.649] [DEBUG] 成功创建缩放上下文，尺寸: 1280x720
[2025-08-11 12:15:18.650] [DEBUG] 重新创建QImage，尺寸: 1280x720
[2025-08-11 12:15:20.866] [INFO] onListenClicked called, showList size: 1
[2025-08-11 12:15:20.866] [INFO] Checking video 0, pointer: ThreadID(0xa1c2b30)
[2025-08-11 12:15:20.867] [INFO] Video 0 is valid, checking visibility: true
[2025-08-11 12:15:20.867] [INFO] Video 0 is visible, calling toggleListen
[2025-08-11 12:15:20.867] [INFO] toggleListen called, isPlay: true, networkThread: ThreadID(0xa289820)
[2025-08-11 12:15:20.867] [INFO] Current listening state: false
[2025-08-11 12:15:20.867] [INFO] Starting listening
[2025-08-11 12:15:20.868] [DEBUG] 音频取消静音
[2025-08-11 12:15:20.868] [INFO] Audio listening started - unmuted
[2025-08-11 12:15:20.883] [DEBUG] 音频输出状态: 3, 错误: 0
[2025-08-11 12:15:20.883] [DEBUG] 音频输出状态不是 ActiveState: 3
[2025-08-11 12:15:20.883] [DEBUG] 音频帧解码成功，采样率: 48000, 通道数: 2, 格式: 8
[2025-08-11 12:15:22.785] [DEBUG] 当前帧率: 16.6304 fps, 队列中帧数: 0
[2025-08-11 12:15:23.125] [INFO] Listen toggled via right panel
[2025-08-11 12:15:27.743] [DEBUG] 性能统计: 处理帧数=183, 丢弃帧数=12, 当前队列=0
[2025-08-11 12:15:27.786] [DEBUG] 当前帧率: 19.996 fps, 队列中帧数: 0
[2025-08-11 12:15:32.812] [DEBUG] 当前帧率: 20.0955 fps, 队列中帧数: 0
[2025-08-11 12:15:37.772] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:15:37.871] [DEBUG] 当前帧率: 19.9684 fps, 队列中帧数: 0
[2025-08-11 12:15:42.876] [DEBUG] 当前帧率: 19.984 fps, 队列中帧数: 0
[2025-08-11 12:15:47.797] [DEBUG] 性能统计: 处理帧数=202, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:15:47.897] [DEBUG] 当前帧率: 20.1155 fps, 队列中帧数: 0
[2025-08-11 12:15:52.934] [DEBUG] 当前帧率: 19.8531 fps, 队列中帧数: 0
[2025-08-11 12:15:57.848] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:15:57.955] [DEBUG] 当前帧率: 20.1195 fps, 队列中帧数: 0
[2025-08-11 12:16:02.961] [DEBUG] 当前帧率: 19.976 fps, 队列中帧数: 0
[2025-08-11 12:16:07.853] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:07.982] [DEBUG] 当前帧率: 20.1155 fps, 队列中帧数: 0
[2025-08-11 12:16:12.992] [DEBUG] 当前帧率: 19.9641 fps, 队列中帧数: 0
[2025-08-11 12:16:17.871] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:18.042] [DEBUG] 当前帧率: 20.0079 fps, 队列中帧数: 0
[2025-08-11 12:16:23.077] [DEBUG] 当前帧率: 20.0596 fps, 队列中帧数: 0
[2025-08-11 12:16:27.874] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:28.134] [DEBUG] 当前帧率: 19.9723 fps, 队列中帧数: 0
[2025-08-11 12:16:33.163] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 12:16:37.893] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:38.229] [DEBUG] 当前帧率: 19.9408 fps, 队列中帧数: 0
[2025-08-11 12:16:43.291] [DEBUG] 当前帧率: 19.9526 fps, 队列中帧数: 0
[2025-08-11 12:16:47.911] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:48.318] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:16:53.346] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 12:16:57.926] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:16:58.410] [DEBUG] 当前帧率: 19.9447 fps, 队列中帧数: 0
[2025-08-11 12:17:03.428] [DEBUG] 当前帧率: 19.9283 fps, 队列中帧数: 0
[2025-08-11 12:17:07.939] [DEBUG] 性能统计: 处理帧数=202, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:08.488] [DEBUG] 当前帧率: 19.7668 fps, 队列中帧数: 0
[2025-08-11 12:17:13.498] [DEBUG] 当前帧率: 20.3593 fps, 队列中帧数: 0
[2025-08-11 12:17:17.992] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:18.530] [DEBUG] 当前帧率: 20.0715 fps, 队列中帧数: 0
[2025-08-11 12:17:23.531] [DEBUG] 当前帧率: 19.996 fps, 队列中帧数: 0
[2025-08-11 12:17:28.051] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:28.586] [DEBUG] 当前帧率: 19.9802 fps, 队列中帧数: 0
[2025-08-11 12:17:33.617] [DEBUG] 当前帧率: 20.0795 fps, 队列中帧数: 0
[2025-08-11 12:17:38.078] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:38.679] [DEBUG] 当前帧率: 19.9565 fps, 队列中帧数: 0
[2025-08-11 12:17:43.679] [DEBUG] 当前帧率: 20 fps, 队列中帧数: 0
[2025-08-11 12:17:48.107] [DEBUG] 性能统计: 处理帧数=202, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:48.707] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 12:17:53.767] [DEBUG] 当前帧率: 19.9605 fps, 队列中帧数: 0
[2025-08-11 12:17:58.164] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:17:58.795] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:18:03.862] [DEBUG] 当前帧率: 19.9329 fps, 队列中帧数: 0
[2025-08-11 12:18:08.178] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:08.902] [DEBUG] 当前帧率: 20.0397 fps, 队列中帧数: 0
[2025-08-11 12:18:13.916] [DEBUG] 当前帧率: 19.9442 fps, 队列中帧数: 0
[2025-08-11 12:18:18.179] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:18.959] [DEBUG] 当前帧率: 20.0317 fps, 队列中帧数: 0
[2025-08-11 12:18:23.981] [DEBUG] 当前帧率: 20.1155 fps, 队列中帧数: 0
[2025-08-11 12:18:28.183] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:29.041] [DEBUG] 当前帧率: 19.9605 fps, 队列中帧数: 0
[2025-08-11 12:18:34.068] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:18:38.235] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:39.138] [DEBUG] 当前帧率: 19.925 fps, 队列中帧数: 0
[2025-08-11 12:18:44.142] [DEBUG] 当前帧率: 19.984 fps, 队列中帧数: 0
[2025-08-11 12:18:48.257] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:49.160] [DEBUG] 当前帧率: 20.1316 fps, 队列中帧数: 0
[2025-08-11 12:18:54.224] [DEBUG] 当前帧率: 19.9447 fps, 队列中帧数: 0
[2025-08-11 12:18:58.303] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:18:59.250] [DEBUG] 当前帧率: 20.0995 fps, 队列中帧数: 0
[2025-08-11 12:19:04.254] [DEBUG] 当前帧率: 19.984 fps, 队列中帧数: 0
[2025-08-11 12:19:08.306] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:09.257] [DEBUG] 当前帧率: 19.992 fps, 队列中帧数: 0
[2025-08-11 12:19:14.312] [DEBUG] 当前帧率: 19.9802 fps, 队列中帧数: 0
[2025-08-11 12:19:18.320] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:19.336] [DEBUG] 当前帧率: 20.1035 fps, 队列中帧数: 0
[2025-08-11 12:19:24.401] [DEBUG] 当前帧率: 19.9408 fps, 队列中帧数: 0
[2025-08-11 12:19:28.328] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:29.429] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:19:34.431] [DEBUG] 当前帧率: 19.992 fps, 队列中帧数: 0
[2025-08-11 12:19:38.385] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:39.486] [DEBUG] 当前帧率: 19.9842 fps, 队列中帧数: 0
[2025-08-11 12:19:44.495] [DEBUG] 当前帧率: 19.9641 fps, 队列中帧数: 0
[2025-08-11 12:19:48.414] [DEBUG] 性能统计: 处理帧数=202, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:49.513] [DEBUG] 当前帧率: 20.1275 fps, 队列中帧数: 0
[2025-08-11 12:19:54.577] [DEBUG] 当前帧率: 19.9447 fps, 队列中帧数: 0
[2025-08-11 12:19:58.445] [DEBUG] 性能统计: 处理帧数=199, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:19:59.606] [DEBUG] 当前帧率: 20.0835 fps, 队列中帧数: 0
[2025-08-11 12:20:04.671] [DEBUG] 当前帧率: 19.9408 fps, 队列中帧数: 0
[2025-08-11 12:20:08.450] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:20:09.697] [DEBUG] 当前帧率: 20.0955 fps, 队列中帧数: 0
[2025-08-11 12:20:14.699] [DEBUG] 当前帧率: 19.992 fps, 队列中帧数: 0
[2025-08-11 12:20:18.454] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:20:19.766] [DEBUG] 当前帧率: 19.9329 fps, 队列中帧数: 0
[2025-08-11 12:20:24.785] [DEBUG] 当前帧率: 20.1235 fps, 队列中帧数: 0
[2025-08-11 12:20:28.459] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:20:29.848] [DEBUG] 当前帧率: 19.9486 fps, 队列中帧数: 0
[2025-08-11 12:20:34.878] [DEBUG] 当前帧率: 20.0795 fps, 队列中帧数: 0
[2025-08-11 12:20:38.474] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:20:39.944] [DEBUG] 当前帧率: 19.9368 fps, 队列中帧数: 0
[2025-08-11 12:20:44.972] [DEBUG] 当前帧率: 20.0955 fps, 队列中帧数: 0
[2025-08-11 12:20:48.516] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:20:50.032] [DEBUG] 当前帧率: 19.9605 fps, 队列中帧数: 0
[2025-08-11 12:20:55.061] [DEBUG] 当前帧率: 20.0875 fps, 队列中帧数: 0
[2025-08-11 12:20:58.541] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:00.063] [DEBUG] 当前帧率: 19.996 fps, 队列中帧数: 0
[2025-08-11 12:21:05.122] [DEBUG] 当前帧率: 19.9644 fps, 队列中帧数: 0
[2025-08-11 12:21:08.552] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:10.128] [DEBUG] 当前帧率: 19.976 fps, 队列中帧数: 0
[2025-08-11 12:21:15.148] [DEBUG] 当前帧率: 20.1235 fps, 队列中帧数: 0
[2025-08-11 12:21:18.565] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:20.214] [DEBUG] 当前帧率: 19.9368 fps, 队列中帧数: 0
[2025-08-11 12:21:25.238] [DEBUG] 当前帧率: 20.1075 fps, 队列中帧数: 0
[2025-08-11 12:21:28.592] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:30.304] [DEBUG] 当前帧率: 19.9408 fps, 队列中帧数: 0
[2025-08-11 12:21:35.331] [DEBUG] 当前帧率: 20.0915 fps, 队列中帧数: 0
[2025-08-11 12:21:38.598] [DEBUG] 性能统计: 处理帧数=201, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:40.394] [DEBUG] 当前帧率: 19.9526 fps, 队列中帧数: 0
[2025-08-11 12:21:45.394] [DEBUG] 当前帧率: 20 fps, 队列中帧数: 0
[2025-08-11 12:21:48.604] [DEBUG] 性能统计: 处理帧数=200, 丢弃帧数=0, 当前队列=0
[2025-08-11 12:21:50.419] [DEBUG] 当前帧率: 20.1035 fps, 队列中帧数: 0
