/****************************************************************************
** Meta object code from reading C++ file 'globalsignal.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../globalsignal.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'globalsignal.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_GlobalSignal_t {
    QByteArrayData data[19];
    char stringdata0[189];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_GlobalSignal_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_GlobalSignal_t qt_meta_stringdata_GlobalSignal = {
    {
QT_MOC_LITERAL(0, 0, 12), // "GlobalSignal"
QT_MOC_LITERAL(1, 13, 7), // "exitApp"
QT_MOC_LITERAL(2, 21, 0), // ""
QT_MOC_LITERAL(3, 22, 11), // "ukeyPullOut"
QT_MOC_LITERAL(4, 34, 14), // "rebuildSystray"
QT_MOC_LITERAL(5, 49, 10), // "ClearState"
QT_MOC_LITERAL(6, 60, 11), // "changeState"
QT_MOC_LITERAL(7, 72, 10), // "nMainState"
QT_MOC_LITERAL(8, 83, 9), // "nSubState"
QT_MOC_LITERAL(9, 93, 12), // "changeMainBK"
QT_MOC_LITERAL(10, 106, 6), // "nWidth"
QT_MOC_LITERAL(11, 113, 7), // "nHeight"
QT_MOC_LITERAL(12, 121, 8), // "aboutApp"
QT_MOC_LITERAL(13, 130, 15), // "closeLoadWidget"
QT_MOC_LITERAL(14, 146, 17), // "updateProcessInfo"
QT_MOC_LITERAL(15, 164, 5), // "nScan"
QT_MOC_LITERAL(16, 170, 5), // "nLine"
QT_MOC_LITERAL(17, 176, 5), // "nLoop"
QT_MOC_LITERAL(18, 182, 6) // "nTotal"

    },
    "GlobalSignal\0exitApp\0\0ukeyPullOut\0"
    "rebuildSystray\0ClearState\0changeState\0"
    "nMainState\0nSubState\0changeMainBK\0"
    "nWidth\0nHeight\0aboutApp\0closeLoadWidget\0"
    "updateProcessInfo\0nScan\0nLine\0nLoop\0"
    "nTotal"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_GlobalSignal[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      10,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   64,    2, 0x06 /* Public */,
       3,    1,   65,    2, 0x06 /* Public */,
       4,    0,   68,    2, 0x06 /* Public */,
       5,    0,   69,    2, 0x06 /* Public */,
       6,    2,   70,    2, 0x06 /* Public */,
       6,    1,   75,    2, 0x26 /* Public | MethodCloned */,
       9,    2,   78,    2, 0x06 /* Public */,
      12,    0,   83,    2, 0x06 /* Public */,
      13,    0,   84,    2, 0x06 /* Public */,
      14,    4,   85,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    2,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    7,    8,
    QMetaType::Void, QMetaType::Int,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   10,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::Int, QMetaType::Int,   15,   16,   17,   18,

       0        // eod
};

void GlobalSignal::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        GlobalSignal *_t = static_cast<GlobalSignal *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->exitApp(); break;
        case 1: _t->ukeyPullOut((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->rebuildSystray(); break;
        case 3: _t->ClearState(); break;
        case 4: _t->changeState((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 5: _t->changeState((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 6: _t->changeMainBK((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 7: _t->aboutApp(); break;
        case 8: _t->closeLoadWidget(); break;
        case 9: _t->updateProcessInfo((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< int(*)>(_a[3])),(*reinterpret_cast< int(*)>(_a[4]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (GlobalSignal::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::exitApp)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::ukeyPullOut)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::rebuildSystray)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::ClearState)) {
                *result = 3;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::changeState)) {
                *result = 4;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::changeMainBK)) {
                *result = 6;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::aboutApp)) {
                *result = 7;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::closeLoadWidget)) {
                *result = 8;
                return;
            }
        }
        {
            typedef void (GlobalSignal::*_t)(int , int , int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&GlobalSignal::updateProcessInfo)) {
                *result = 9;
                return;
            }
        }
    }
}

const QMetaObject GlobalSignal::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_GlobalSignal.data,
      qt_meta_data_GlobalSignal,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *GlobalSignal::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *GlobalSignal::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_GlobalSignal.stringdata0))
        return static_cast<void*>(this);
    if (!strcmp(_clname, "Singleton<GlobalSignal>"))
        return static_cast< Singleton<GlobalSignal>*>(this);
    return QObject::qt_metacast(_clname);
}

int GlobalSignal::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void GlobalSignal::exitApp()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void GlobalSignal::ukeyPullOut(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void GlobalSignal::rebuildSystray()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void GlobalSignal::ClearState()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void GlobalSignal::changeState(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 6
void GlobalSignal::changeMainBK(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void GlobalSignal::aboutApp()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}

// SIGNAL 8
void GlobalSignal::closeLoadWidget()
{
    QMetaObject::activate(this, &staticMetaObject, 8, nullptr);
}

// SIGNAL 9
void GlobalSignal::updateProcessInfo(int _t1, int _t2, int _t3, int _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)), const_cast<void*>(reinterpret_cast<const void*>(&_t3)), const_cast<void*>(reinterpret_cast<const void*>(&_t4)) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
