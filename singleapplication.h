﻿/********************************************************
 * 创建者：ljf
 * 创建日期：2020-07-13
 * 描述：用于控制仅有一个应用程序的实例在运行
 *
********************************************************/

#ifndef SINGLEAPPLICATION_H
#define SINGLEAPPLICATION_H

#include <QApplication>

class QLocalServer;

class SingleApplication : public QApplication
{
    Q_OBJECT
public:
    explicit SingleApplication(int argc, char **argv);
    //判断进程是否存在
    bool isRunning();

private slots:
    void newLocalConnection();

private:
    QLocalServer *m_pServer;
    bool m_bRunning;
};

#endif // SINGLEAPPLICATION_H
