# RTSP多视频流性能优化说明

## 问题描述
在同时播放多个RTSP视频流时，第一个和第二个视频流播放正常且流畅，但第三个视频流会出现明显的卡顿和延迟问题。

## 问题原因分析
1. **FFmpeg网络初始化竞争**：每个线程都调用`avformat_network_init()`，导致资源竞争
2. **缓冲区资源竞争**：所有线程使用相同的缓冲区大小，没有考虑多流并发情况
3. **线程优先级相同**：所有视频线程使用相同优先级，导致CPU资源竞争
4. **同时连接抢占网络**：多个视频流同时尝试连接，造成网络拥塞
5. **帧处理策略不当**：没有根据系统负载动态调整帧处理策略

## 优化方案

### 1. 网络初始化优化
- 使用静态变量和互斥锁确保FFmpeg网络功能只初始化一次
- 避免多线程重复初始化导致的资源竞争

### 2. 动态资源分配
- 根据活跃线程数量动态调整缓冲区大小
- 多线程时减少单个线程的缓冲区占用，节省内存
- 调整探测大小和分析时间，平衡性能和准确性

### 3. 线程优先级管理
- 前两个线程使用正常优先级
- 第三个及以后的线程使用低优先级，避免抢占资源
- 动态调整帧缓存大小

### 4. 智能连接延迟
- 第一个视频流立即连接
- 后续视频流根据已有线程数量增加连接延迟
- 避免同时抢占网络带宽

### 5. 智能帧丢弃策略
- 多线程情况下采用更激进的丢帧策略
- 优先保留关键帧，智能丢弃非关键帧
- 根据缓存状态动态调整丢帧阈值

### 6. 优化睡眠策略
- 根据线程数量和缓存状态调整睡眠时间
- 多线程时适当增加睡眠时间，减少CPU竞争

## 代码修改详情

### MyThread类修改
1. 添加静态方法管理全局线程计数
2. 优化initFFmpeg()方法的网络初始化和参数设置
3. 改进cleanupFFmpeg()方法的资源清理
4. 增强handleRtspWithFFmpeg()方法的线程优先级和帧处理逻辑

### OneVideo类修改
1. 在setRtspUrl()方法中添加智能连接延迟
2. 根据活跃线程数量动态调整连接延迟时间

## 预期效果
1. **减少资源竞争**：通过统一的网络初始化和动态资源分配
2. **提高响应性**：通过线程优先级管理和智能延迟
3. **优化内存使用**：通过动态缓冲区调整
4. **改善用户体验**：第三个及后续视频流的播放流畅度显著提升

## 使用建议
1. 建议同时播放的RTSP流数量不超过6个，以确保最佳性能
2. 如需播放更多视频流，可考虑分批加载或使用更强大的硬件
3. 监控系统资源使用情况，必要时调整缓冲区参数

## 测试验证
1. 依次添加多个RTSP视频流
2. 观察第三个视频流的播放流畅度是否改善
3. 检查系统资源使用情况
4. 验证所有视频流的稳定性

## 注意事项
1. 这些优化主要针对多视频流并发播放场景
2. 单个视频流的性能不会受到影响
3. 优化参数可根据实际硬件配置进一步调整
