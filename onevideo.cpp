#include "onevideo.h"
#include "ui_onevideo.h"
#include "iconbutton.h"
#include "mythread.h"
#include "configdialog.h"
#include "fullscreenvideo.h"
#include "configmanager.h"
#include "logmanager.h"

#include <QHBoxLayout>
#include <QDebug>
#include <QPainter>
#include <QLinearGradient>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QTimer>
#include <QImage>
#include <QLabel>
#include <QDateTime>
#include <QFile>
#include <QMessageBox>
#include <QUrl>
#include <QTransform>
#include <QResizeEvent>
#include <QPen>
#include <QDir>
#include <QStandardPaths>

#include <QProcess>
#include <QCoreApplication>
#include <QFileInfo>
#include <QVBoxLayout>

const int OneVideo::WIDTH = 427;  // 16:9比例 (原来是320)
const int OneVideo::HEIGHT = 240; // 保持高度不变

OneVideo::OneVideo(QWidget *parent) : Q<PERSON>rame(parent), ui(new Ui::OneVideo),
									  centralImage()
{
	ui->setupUi(this);
	resize(WIDTH, HEIGHT);

	// 创建主垂直布局
	QVBoxLayout *mainLayout = new QVBoxLayout(this);
	mainLayout->setContentsMargins(0, 0, 0, 0);
	mainLayout->setSpacing(0);

	// 创建名称标签
	// nameLabel = new QLabel(this);
	// nameLabel->setAlignment(Qt::AlignCenter);
	// nameLabel->setStyleSheet("QLabel { color: white; background-color: rgba(0, 0, 0, 0.5); border-radius: 5px; padding: 5px; }");
	// nameLabel->setText("Camera " + QString::number(cameraId));
	// nameLabel->hide(); // 隐藏名称标签

	// 确保nameLabel始终在最上层
	// nameLabel->raise();
	// nameLabel->setFixedHeight(25); // 设置固定高度

	// 隐藏摄像头名称标签但保留代码
	// nameLabel->setVisible(false);

	// 将标签添加到主布局，强制置顶且水平居中
	// mainLayout->addWidget(nameLabel, 0, Qt::AlignHCenter | Qt::AlignTop);

	// 添加视频显示区域的弹性空间
	mainLayout->addStretch(1);

	// 设置布局
	setLayout(mainLayout);

	// 创建工具按钮和IP端口标签
	createToolButtons();
	createIpPortLabel();

	// 不再需要设置边框样式，因为我们在paintEvent中直接绘制边框
	// 设置透明背景
	setAttribute(Qt::WA_TranslucentBackground, true);
	setStyleSheet("QFrame { background: transparent; margin: 3px; }");

	setMouseTracking(true);
	isPlay = false;
	rotationAngle = 0; // 初始化旋转角度
	isMuted = true;	   // 初始化静音状态为静音
	// cameraBtn->setEnabled(false);
	// recordBtn->setEnabled(false);
	// fullScreenBtn->setEnabled(false);
	// rotateImageBtn->setEnabled(false); // 初始禁用旋转按钮
	// muteBtn->setEnabled(false); // 初始禁用静音按钮
	networkThread = NULL;

	// 创建黑色图像
	centralImage = QImage(WIDTH, HEIGHT, QImage::Format_RGB32);
	centralImage.fill(Qt::black);

	// 创建定时器，每秒更新一次时间
	QTimer *timer = new QTimer(this);
	connect(timer, SIGNAL(timeout()), this, SLOT(updateTimeDisplay()));
	timer->start(1000); // 每1000毫秒（1秒）触发一次

	// 初始更新时间显示
	updateTimeDisplay();

	// 初始化录制相关变量
	isRecording = false;
	recordingTimer = new QTimer(this);
	connect(recordingTimer, SIGNAL(timeout()), this, SLOT(updateRecordingTime()));
	recordingDuration = 0;

	// 初始化窃听相关变量
	isListening = false;

	// 创建录制指示器
	recordingIndicator = new QLabel(this);
	recordingIndicator->setStyleSheet("QLabel { color: red; background-color: rgba(0,0,0,100); padding: 2px; border-radius: 2px; }");
	recordingIndicator->setAlignment(Qt::AlignCenter);
	recordingIndicator->hide();
}

OneVideo::~OneVideo()
{
	delete ui;
	// 如果正在录制，停止录制
	if (isRecording)
	{
		stopRecording();
	}

	// 清理nameLabel
	// if (nameLabel)
	// {
	// 	delete nameLabel;
	// 	nameLabel = nullptr;
	// }

	if (networkThread != NULL)
	{
		// 使用safeStop方法安全停止线程
		networkThread->safeStop(2000); // 设置等待超时时间为2秒

		// 如果线程仍在运行，强制终止
		if (networkThread->isThreadRunning())
		{
			LOG_INFO("thread can't end normally in destructor, force terminate");
			networkThread->terminate();
			networkThread->wait(1000);
		}

		networkThread->deleteLater();
		networkThread = NULL;
	}
}

void OneVideo::createToolButtons()
{
	// 创建按钮但不设置位置，位置将在resizeEvent中设置
	// cameraBtn = new IconButton(this);
	// cameraBtn->setIcon(QIcon(":/images/camera.png"));
	// connect(cameraBtn, SIGNAL(clicked(bool)), SLOT(cameraBtnSlot()));

	// recordBtn = new IconButton(this);
	// recordBtn->setIcon(QIcon(":/images/record.png"));
	// connect(recordBtn, SIGNAL(clicked(bool)), SLOT(recordBtnSlot()));

	// fullScreenBtn = new IconButton(this);
	// fullScreenBtn->setIcon(QIcon(":/images/fullscreen.png"));
	// connect(fullScreenBtn, SIGNAL(clicked(bool)), SLOT(fullScreenBtnSlot()));

	// 添加旋转按钮
	// rotateImageBtn = new IconButton(this);
	// rotateImageBtn->setIcon(QIcon(":/images/rotate.png")); // 需要添加旋转图标
	// connect(rotateImageBtn, SIGNAL(clicked(bool)), SLOT(rotateImageBtnSlot()));

	// 添加静音按钮
	// muteBtn = new IconButton(this);
	// muteBtn->setIcon(QIcon(":/images/mute.png")); // 默认显示静音图标
	// connect(muteBtn, SIGNAL(clicked(bool)), SLOT(muteButtonSlot()));

	// 初始时隐藏所有按钮，只在鼠标移入时显示
	// cameraBtn->hide();
	// recordBtn->hide();
	// fullScreenBtn->hide();
	// rotateImageBtn->hide();
}

void OneVideo::createIpPortLabel()
{
	ipPortLabel = new QLabel(this);
	ipPortLabel->setFont(QFont("Microsoft YaHei", 10, 1));		  // 使用微软雅黑字体
	ipPortLabel->setMargin(1);									  // 最小边距
	ipPortLabel->resize(150, 22);								  // 标准高度，只显示时间
	ipPortLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter); // 右对齐，垂直居中

	// 设置半透明背景和白色文字
	ipPortLabel->setStyleSheet("color: white; background-color: rgba(0, 0, 0, 100); padding: 1px 2px;");

	// 确保标签显示在最上层
	ipPortLabel->setAttribute(Qt::WA_TransparentForMouseEvents); // 鼠标事件穿透
	ipPortLabel->raise();

	// 确保标签始终显示在顶层
	ipPortLabel->raise();
}

void OneVideo::cameraBtnSlot()
{
	// 获取当前时间文本
	QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");

	// 创建一个与视频内容相同的图像副本
	QImage screenshot = centralImage.copy();

	// 在图像上绘制时间标签
	QPainter painter(&screenshot);

	// 设置更大的字体和颜色
	painter.setFont(QFont("Consolas", 14, QFont::Bold)); // 增大字体到14号，并设置为粗体
	painter.setPen(Qt::white);

	// 创建更大的半透明黑色背景
	QRect textRect(screenshot.width() - 200, 5, 195, 30); // 调整矩形大小以适应更大的字体
	painter.fillRect(textRect, QColor(0, 0, 0, 150));	  // 稍微增加背景不透明度

	// 绘制时间文本
	painter.drawText(textRect, Qt::AlignRight | Qt::AlignVCenter, currentTime);

	// 获取应用程序所在目录
	QString appDir = QCoreApplication::applicationDirPath();

	// 创建截图保存目录
	QString screenshotDir = appDir + "/Screenshots";
	QDir dir(screenshotDir);
	if (!dir.exists())
	{
		dir.mkpath(".");
	}

	// 保存截图
	QString fileName = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
	QString fullPath = screenshotDir + "/" + fileName + ".jpg";
	bool saved = screenshot.save(fullPath, "JPEG");

	// 使用简单的消息框，避免引入额外的布局类
	QMessageBox msgBox;
	msgBox.setWindowTitle("截图提示");
	msgBox.setFixedSize(280, 150);

	// 设置样式表
	msgBox.setStyleSheet(
		"QMessageBox {"
		"  background-color: white;"
		"}"
		"QLabel {"
		"  color: #333333;"
		"  font-size: 12px;"
		"}"
		"QPushButton {"
		"  background-color: #0078D7;"
		"  color: white;"
		"  border: none;"
		"  padding: 6px 16px;"
		"  border-radius: 3px;"
		"  min-width: 80px;"
		"}"
		"QPushButton:hover {"
		"  background-color: #1683D8;"
		"}");

	// 设置图标和文本
	msgBox.setIcon(QMessageBox::Information);
	msgBox.setText("截图成功");
	msgBox.setInformativeText("已保存为: " + fullPath);

	// 设置按钮
	msgBox.setStandardButtons(QMessageBox::Ok);
	msgBox.button(QMessageBox::Ok)->setText("确定");

	// 显示对话框
	msgBox.exec();
}

void OneVideo::recordBtnSlot()
{
	// 如果没有播放视频，不执行任何操作
	if (!isPlay || !networkThread)
	{
		return;
	}

	// 切换录制状态
	if (!isRecording)
	{
		// 开始录制
		startRecording();
	}
	else
	{
		// 停止录制
		stopRecording();
	}
}

// 开始录制
void OneVideo::startRecording()
{
	// 添加调试信息
	LOG_INFO("startRecording enter URL: " << rtspUrl);

	// 获取应用程序所在目录
	QString appDir = QCoreApplication::applicationDirPath();

	// 创建视频保存目录
	QString videoDir = appDir + "/Videos";
	QDir dir(videoDir);
	if (!dir.exists())
	{
		dir.mkpath(".");
	}
	// 创建录制文件路径
	QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
	recordingFilePath = videoDir + "/" + timestamp + ".mp4";
	// 检查是否有有效的RTSP URL
	if (rtspUrl.isEmpty())
	{
		QMessageBox::warning(this, "录制错误", "没有有效的视频源，无法开始录制。");
		return;
	}
	// 使用FFmpeg进行录制
	QProcess *ffmpegProcess = new QProcess(this);
	// 从配置管理器获取最大录制时长（单位：秒）
	int maxDuration = ConfigManager::instance().getMaxRecordingDuration();

	// 设置FFmpeg命令 - 提高视频质量，减少失真
	QStringList arguments;
	arguments << "-y"				   // 覆盖输出文件
			  << "-rtbufsize" << "15M" // 增加读取缓冲区大小，提高稳定性
			  << "-i" << rtspUrl	   // 输入RTSP流
			  // 暂时移除视频滤镜，测试是否是滤镜导致问题
			  << "-vf" << "drawtext=fontsize=24:fontcolor=white:box=1:boxcolor=black@0.5:x=w-text_w-10:y=10:text='%{localtime}'"
			  << "-c:v" << "libx265"	  // 使用H.265编码器
			  << "-preset" << "ultrafast" // 使用最快的编码预设，减少CPU负担
			  << "-b:v" << "1024k"		  // 设置适中的视频比特率
			  << "-r" << "20"			  // 设置适中的帧率
			  << "-c:a" << "aac"		  // 音频编码为AAC
			  << "-b:a" << "8k"			  // 降低音频比特率
			  //<< "-strict" << "experimental"		  // 允许实验性编码器
			  << "-t" << QString::number(maxDuration) // 最大录制时长（从配置中获取）
			  << recordingFilePath;
	LOG_INFO("ffmpeg cmd: " << arguments.join(" "));
	LOG_INFO("file path: " << recordingFilePath);
	LOG_INFO("max duration: " << maxDuration << " seconds");

	// 启动FFmpeg进程
	QString ffmpegPath = QCoreApplication::applicationDirPath() + "/ffmpeg.exe";

	// 检查ffmpeg.exe是否存在，如果不存在则尝试使用系统PATH中的ffmpeg
	if (!QFile::exists(ffmpegPath))
	{
		LOG_INFO("应用程序目录下未找到ffmpeg.exe，将尝试使用系统PATH中的ffmpeg");
		ffmpegPath = "ffmpeg";
	}
	else
	{
		LOG_INFO("使用应用程序目录下的ffmpeg.exe: " << ffmpegPath);
	}

	ffmpegProcess->start(ffmpegPath, arguments);

	// 检查进程是否成功启动
	if (!ffmpegProcess->waitForStarted(3000))
	{
		QMessageBox::warning(this, "录制错误", "无法启动录制进程，请确保FFmpeg已安装。");
		LOG_INFO("FFmpeg start failed: " << ffmpegProcess->errorString());
		ffmpegProcess->deleteLater();
		return;
	}

	// 保存进程指针
	ffmpegProcess->setProperty("recordingPath", recordingFilePath);
	ffmpegProcess->setProperty("manualStop", false); // 初始化为非手动停止

	// 连接进程结束信号
	connect(ffmpegProcess, static_cast<void (QProcess::*)(int, QProcess::ExitStatus)>(&QProcess::finished),
			this, [this, ffmpegProcess](int exitCode, QProcess::ExitStatus exitStatus)
			{
			LOG_INFO("FFmpeg process end, exit code: " << exitCode << ", status: " << exitStatus);
			
			// 如果正在录制，则标记录制已结束，但不调用stopRecording()以避免重复显示对话框
			if (isRecording) {
				// 停止计时器
				recordingTimer->stop();
				
				// 更新状态
				isRecording = false;
				
				// 恢复录制按钮图标
				recordBtn->setIcon(QIcon(":/images/record.png"));
				
				// 隐藏录制指示器
				recordingIndicator->hide();
				
				// 检查是否是手动停止的录制
				bool isManualStop = ffmpegProcess->property("manualStop").toBool();
				
				// 只有在非手动停止（即自动达到时长限制停止）的情况下才显示提示框
				if (!isManualStop) {
					// 显示录制完成消息，与手动停止时相同
					QMessageBox msgBox;
					msgBox.setWindowTitle("录制完成");
					msgBox.setFixedSize(280, 150);
					msgBox.setStyleSheet(
						"QMessageBox {"
						"  background-color: white;"
						"}"
						"QLabel {"
						"  color: #333333;"
						"  font-size: 12px;"
						"}"
						"QPushButton {"
						"  background-color: #0078D7;"
						"  color: white;"
						"  border: none;"
						"  padding: 6px 16px;"
						"  border-radius: 3px;"
						"  min-width: 80px;"
						"}"
						"QPushButton:hover {"
						"  background-color: #1683D8;"
						"}");
					msgBox.setText("视频录制完成");
					
					// 使用配置的maxDuration作为实际录制时长，而不是使用计时器计数的时间
					int maxDuration = ConfigManager::instance().getMaxRecordingDuration();
					
					// 格式化录制时间
					int hours = maxDuration / 3600;
					int minutes = (maxDuration % 3600) / 60;
					int seconds = maxDuration % 60;
					QString durationText;
					if (hours > 0)
					{
						durationText = QString("%1:%2:%3")
									   .arg(hours, 2, 10, QChar('0'))
									   .arg(minutes, 2, 10, QChar('0'))
									   .arg(seconds, 2, 10, QChar('0'));
					}
					else
					{
						durationText = QString("%1:%2")
									   .arg(minutes, 2, 10, QChar('0'))
									   .arg(seconds, 2, 10, QChar('0'));
					}
					
					QString recordingPath = ffmpegProcess->property("recordingPath").toString();
					if (recordingPath.isEmpty()) {
						recordingPath = recordingFilePath;
					}
					
					msgBox.setInformativeText(QString("视频已保存至:\n%1\n录制时长: %2").arg(recordingPath).arg(durationText));
					msgBox.setIcon(QMessageBox::Information);
					msgBox.setStandardButtons(QMessageBox::Ok);
					msgBox.button(QMessageBox::Ok)->setText("确定");
					msgBox.exec();
					
					LOG_INFO("video recording finished (auto stop):" << recordingPath << "duration:" << durationText);
				}
			}
			
			ffmpegProcess->deleteLater(); });

	// 连接错误信号
	connect(ffmpegProcess, &QProcess::errorOccurred, this, [this, ffmpegProcess](QProcess::ProcessError error)
			{
		LOG_ERROR("FFmpeg process error:" << error << ffmpegProcess->errorString());
		
		if (isRecording) {
			QMessageBox::warning(this, "录制错误", "录制过程中发生错误: " + ffmpegProcess->errorString());
			
			// 直接处理录制结束，而不是调用stopRecording()
			// 停止计时器
			recordingTimer->stop();
			
			// 更新状态
			isRecording = false;
			
			// 恢复录制按钮图标
			recordBtn->setIcon(QIcon(":/images/record.png"));
			
			// 隐藏录制指示器
			recordingIndicator->hide();
		}
		
		ffmpegProcess->deleteLater(); });

	// 将进程保存为属性，以便后续停止录制
	this->setProperty("ffmpegProcess", QVariant::fromValue(ffmpegProcess));

	// 设置录制状态
	isRecording = true;
	recordingDuration = 0;

	// 更改录制按钮图标
	recordBtn->setIcon(QIcon(":/images/pause.png"));

	// 显示录制指示器
	updateRecordingIndicator();
	recordingIndicator->show();

	// 启动录制计时器
	recordingTimer->start(1000); // 每秒更新一次

	LOG_INFO("start recording video:" << recordingFilePath);
}

// 停止录制
void OneVideo::stopRecording()
{
	if (!isRecording)
	{
		return;
	}

	// 停止计时器
	recordingTimer->stop();

	// 停止FFmpeg进程
	QProcess *ffmpegProcess = this->property("ffmpegProcess").value<QProcess *>();
	if (ffmpegProcess && ffmpegProcess->state() == QProcess::Running)
	{
		LOG_INFO("terminate FFmpeg process");

		// 标记这是手动停止的录制
		ffmpegProcess->setProperty("manualStop", true);

		// 向FFmpeg发送q命令以优雅地结束录制
		ffmpegProcess->write("q");

		// 等待进程结束
		if (!ffmpegProcess->waitForFinished(3000))
		{
			LOG_ERROR("FFmpeg process can't end normally, force terminate");
			ffmpegProcess->terminate();
			if (!ffmpegProcess->waitForFinished(2000))
			{
				ffmpegProcess->kill();
			}
		}
	}

	// 更新状态
	isRecording = false;

	// 恢复录制按钮图标
	recordBtn->setIcon(QIcon(":/images/record.png"));

	// 隐藏录制指示器
	recordingIndicator->hide();

	// 显示录制完成消息
	QMessageBox msgBox;
	msgBox.setWindowTitle("录制完成");
	msgBox.setFixedSize(280, 150);
	msgBox.setStyleSheet(
		"QMessageBox {"
		"  background-color: white;"
		"}"
		"QLabel {"
		"  color: #333333;"
		"  font-size: 12px;"
		"}"
		"QPushButton {"
		"  background-color: #0078D7;"
		"  color: white;"
		"  border: none;"
		"  padding: 6px 16px;"
		"  border-radius: 3px;"
		"  min-width: 80px;"
		"}"
		"QPushButton:hover {"
		"  background-color: #1683D8;"
		"}");
	msgBox.setText("视频录制完成");

	// 使用实际录制的时间而不是计时器计数
	// 对于手动停止，使用当前的recordingDuration，但确保不超过配置的最大时长
	int maxDuration = ConfigManager::instance().getMaxRecordingDuration();
	int actualDuration = qMin(recordingDuration, maxDuration);

	// 格式化录制时间
	int hours = actualDuration / 3600;
	int minutes = (actualDuration % 3600) / 60;
	int seconds = actualDuration % 60;
	QString durationText;
	if (hours > 0)
	{
		durationText = QString("%1:%2:%3")
						   .arg(hours, 2, 10, QChar('0'))
						   .arg(minutes, 2, 10, QChar('0'))
						   .arg(seconds, 2, 10, QChar('0'));
	}
	else
	{
		durationText = QString("%1:%2")
						   .arg(minutes, 2, 10, QChar('0'))
						   .arg(seconds, 2, 10, QChar('0'));
	}

	msgBox.setInformativeText(QString("视频已保存至:\n%1\n录制时长: %2").arg(recordingFilePath).arg(durationText));
	msgBox.setIcon(QMessageBox::Information);
	msgBox.setStandardButtons(QMessageBox::Ok);
	msgBox.button(QMessageBox::Ok)->setText("确定");
	msgBox.exec();

	LOG_INFO("video recording finished:" << recordingFilePath << "duration:" << durationText);
}

// 更新录制时间
void OneVideo::updateRecordingTime()
{
	if (!isRecording)
	{
		return;
	}

	// 增加录制时长
	recordingDuration++;

	// 更新录制指示器
	updateRecordingIndicator();
}

// 更新录制指示器
void OneVideo::updateRecordingIndicator()
{
	// 格式化录制时间
	int hours = recordingDuration / 3600;
	int minutes = (recordingDuration % 3600) / 60;
	int seconds = recordingDuration % 60;

	QString timeText;
	if (hours > 0)
	{
		timeText = QString("● REC %1:%2:%3")
					   .arg(hours, 2, 10, QChar('0'))
					   .arg(minutes, 2, 10, QChar('0'))
					   .arg(seconds, 2, 10, QChar('0'));
	}
	else
	{
		timeText = QString("● REC %1:%2")
					   .arg(minutes, 2, 10, QChar('0'))
					   .arg(seconds, 2, 10, QChar('0'));
	}

	recordingIndicator->setText(timeText);
	recordingIndicator->adjustSize();

	// 计算视频显示区域的左上角坐标，与paintEvent方法中的计算保持一致
	// 计算nameLabel占用的空间
	// int labelOffset = 0;
	// if (nameLabel && nameLabel->isVisible())
	// {
	// 	labelOffset = nameLabel->height() + 5; // 标签高度加上一点额外空间
	// }

	// 计算视频显示区域，与paintEvent保持一致
	double idealHeight = width() * 9.0 / 16.0;
	// double idealWidth = (height() - labelOffset) * 16.0 / 9.0;
	double idealWidth = height() * 16.0 / 9.0; // 使用0替代labelOffset

	int frameX = 0;
	// int frameY = labelOffset; // 从标签下方开始绘制视频
	int frameY = 0; // 从顶部开始，不使用labelOffset
	int frameWidth = width();
	// int frameHeight = height() - labelOffset;
	int frameHeight = height(); // 使用完整高度

	// 调整外框以保持16:9的比例
	if (width() / frameHeight > 16.0 / 9.0)
	{
		// 宽度过大，以高度为准
		frameWidth = idealWidth;
		frameX = (width() - frameWidth) / 2;
	}
	else
	{
		// 高度过大，以宽度为准
		frameHeight = idealHeight;
		// frameY = labelOffset + ((height() - labelOffset) - frameHeight) / 2;
		frameY = (height() - frameHeight) / 2;
	}

	// 根据旋转角度计算视频的显示区域
	bool isVertical = (rotationAngle == 90 || rotationAngle == 270);
	int videoX, videoY, videoWidth, videoHeight;

	if (isVertical)
	{
		// 竖屏模式 (9:16) - 在横屏外框内居中显示
		// 计算在保持比例的情况下，视频的最大尺寸
		double videoRatio = 9.0 / 16.0; // 竖屏视频的宽高比

		// 首先尝试以高度为基准
		videoHeight = frameHeight;
		videoWidth = videoHeight * videoRatio;

		// 如果宽度超出了外框，则以宽度为基准
		if (videoWidth > frameWidth)
		{
			videoWidth = frameWidth;
			videoHeight = videoWidth / videoRatio;
		}

		// 计算视频在外框内的位置（居中）
		videoX = frameX + (frameWidth - videoWidth) / 2;
		videoY = frameY + (frameHeight - videoHeight) / 2;
	}
	else
	{
		// 横屏模式 (16:9) - 填满整个外框
		videoX = frameX;
		videoY = frameY;
		videoWidth = frameWidth;
		videoHeight = frameHeight;
	}

	// 放置在视频区域的左上角，添加小边距
	recordingIndicator->move(videoX + 10, videoY + 10);
	recordingIndicator->raise();
}

void OneVideo::fullScreenBtnSlot()
{
	// 先取消发送消息到小窗口
	disconnect(networkThread, SIGNAL(transmitData(QImage)),
			   this, SLOT(updateImage(QImage)));

	// 发送消息到全屏窗口
	FullScreenVideo fullScreenVideo(this);
	connect(networkThread, SIGNAL(transmitData(QImage)),
			&fullScreenVideo, SLOT(updateImage(QImage)));
	fullScreenVideo.exec();

	// 恢复发送消息到小窗口
	connect(networkThread, SIGNAL(transmitData(QImage)),
			this, SLOT(updateImage(QImage)));

	// 更新标签位置
	QTimer::singleShot(100, this, SLOT(updateLabelPositions()));
}

void OneVideo::updateImage(QImage image)
{
	if (isPlay)
	{
		// 保存原始图像
		centralImage = image;

		// 如果当前有旋转角度，则应用旋转
		if (rotationAngle != 0)
		{
			QTransform transform;
			transform.rotate(rotationAngle);
			centralImage = centralImage.transformed(transform);
		}

		// 添加调试信息
		if (isRecording)
		{
			LOG_DEBUG("start recording video, rtsp URL: " << rtspUrl << ", recording duration: " << recordingDuration);
		}

		update();
	}
}

void OneVideo::disconnectSlot()
{
	// 不显示消息框，避免在后台线程中显示UI元素
	LOG_WARNING("连接断开: " << (rtspUrl.isEmpty() ? "未知URL" : rtspUrl));

	if (networkThread != NULL)
	{
		// 使用safeStop方法安全停止线程
		networkThread->safeStop(500); // 只等待0.5秒

		// 如果线程仍在运行，强制终止
		if (networkThread->isThreadRunning())
		{
			LOG_WARNING("Thread still running after safeStop, force terminating");
			networkThread->terminate();
			networkThread->wait(500);
		}

		// 立即删除线程，不要使用deleteLater
		delete networkThread;
		networkThread = NULL;
	}

	// 更新UI状态
	// cameraBtn->setEnabled(false);
	// recordBtn->setEnabled(false);
	// fullScreenBtn->setEnabled(false);
	// rotateImageBtn->setEnabled(false);
	// muteBtn->setEnabled(false);

	// 恢复为黑色图像
	centralImage = QImage(WIDTH, HEIGHT, QImage::Format_RGB32);
	centralImage.fill(Qt::black);
	update();
	isPlay = false;

	// 发射断开连接信号
	emit disconnected();
}

void OneVideo::closeEvent(QCloseEvent *event)
{
	QFrame::closeEvent(event);
	emit closeSignal(this);
}

void OneVideo::paintEvent(QPaintEvent *event)
{
	QFrame::paintEvent(event);
	QPainter p(this);

	if (!centralImage.isNull())
	{
		// 计算nameLabel占用的空间，确保视频不会绘制在这个区域
		// int labelOffset = 0;
		// if (nameLabel && nameLabel->isVisible())
		// {
		// 	labelOffset = nameLabel->height() + 5; // 标签高度加上一点额外空间
		// }

		// 外框始终保持横屏状态(16:9)，但避开标签区域
		// 计算外框的显示区域
		double idealHeight = width() * 9.0 / 16.0;
		// double idealWidth = (height() - labelOffset) * 16.0 / 9.0;
		double idealWidth = height() * 16.0 / 9.0;

		int frameX = 0;
		// int frameY = labelOffset; // 从标签下方开始绘制视频
		int frameY = 0; // 从标签下方开始绘制视频
		int frameWidth = width();
		// int frameHeight = height() - labelOffset;
		int frameHeight = height();

		// 调整外框以保持16:9的比例
		if (width() / frameHeight > 16.0 / 9.0)
		{
			// 宽度过大，以高度为准
			frameWidth = idealWidth;
			frameX = (width() - frameWidth) / 2;
		}
		else
		{
			// 高度过大，以宽度为准
			frameHeight = idealHeight;
			// frameY = labelOffset + ((height() - labelOffset) - frameHeight) / 2;
			frameY = (height() - frameHeight) / 2;
		}

		// 首先绘制黑色背景填充整个外框区域
		p.fillRect(frameX, frameY, frameWidth, frameHeight, Qt::black);

		// 根据旋转角度计算视频的显示区域
		bool isVertical = (rotationAngle == 90 || rotationAngle == 270);
		int videoX, videoY, videoWidth, videoHeight;

		if (isVertical)
		{
			// 竖屏模式 (9:16) - 在横屏外框内居中显示
			// 计算在保持比例的情况下，视频的最大尺寸
			double videoRatio = 9.0 / 16.0; // 竖屏视频的宽高比

			// 首先尝试以高度为基准
			videoHeight = frameHeight;
			videoWidth = videoHeight * videoRatio;

			// 如果宽度超出了外框，则以宽度为基准
			if (videoWidth > frameWidth)
			{
				videoWidth = frameWidth;
				videoHeight = videoWidth / videoRatio;
			}

			// 计算视频在外框内的位置（居中）
			videoX = frameX + (frameWidth - videoWidth) / 2;
			videoY = frameY + (frameHeight - videoHeight) / 2;
		}
		else
		{
			// 横屏模式 (16:9) - 填满整个外框
			videoX = frameX;
			videoY = frameY;
			videoWidth = frameWidth;
			videoHeight = frameHeight;
		}

		// 绘制视频内容
		p.drawImage(QRect(videoX, videoY, videoWidth, videoHeight), centralImage);

		// 绘制外框边界
		p.setPen(QPen(QColor("#00AAFF"), 2));
		p.drawRect(frameX, frameY, frameWidth, frameHeight);
	}
}

void OneVideo::mouseMoveEvent(QMouseEvent *event)
{
	// 计算nameLabel占用的空间
	// int labelOffset = 0;
	// if (nameLabel && nameLabel->isVisible())
	// {
	// 	labelOffset = nameLabel->height() + 5;
	// }

	// 外框始终保持横屏状态(16:9)，与paintEvent保持一致
	double idealHeight = width() * 9.0 / 16.0;
	// double idealWidth = (height() - labelOffset) * 16.0 / 9.0;
	double idealWidth = height() * 16.0 / 9.0; // 使用0替代labelOffset

	int frameX = 0;
	// int frameY = labelOffset; // 从标签下方开始
	int frameY = 0; // 从顶部开始，不使用labelOffset
	int frameWidth = width();
	// int frameHeight = height() - labelOffset;
	int frameHeight = height(); // 使用完整高度

	// 调整外框以保持16:9的比例
	if (width() / frameHeight > 16.0 / 9.0)
	{
		// 宽度过大，以高度为准
		frameWidth = idealWidth;
		frameX = (width() - frameWidth) / 2;
	}
	else
	{
		// 高度过大，以宽度为准
		frameHeight = idealHeight;
		// frameY = labelOffset + ((height() - labelOffset) - frameHeight) / 2;
		frameY = (height() - frameHeight) / 2; // 使用0替代labelOffset
	}

	// 检查鼠标是否在外框区域内
	QRect frameRect(frameX, frameY, frameWidth, frameHeight);
	if (frameRect.contains(event->pos()))
	{
		if (!isButtonsShow)
		{
			isButtonsShow = true;
			// 不显示关闭按钮和播放按钮
			// closeBtn->show();
			// playBtn->show();
			// cameraBtn->show();
			// recordBtn->show();
			// fullScreenBtn->show();
			// rotateImageBtn->show();
			// muteBtn->hide(); // 显示静音按钮
		}
	}
	else
	{
		if (isButtonsShow)
		{
			// closeBtn->hide();
			// playBtn->hide();
			// cameraBtn->hide();
			// recordBtn->hide();
			// fullScreenBtn->hide();
			// rotateImageBtn->hide();
			// muteBtn->hide(); // 隐藏静音按钮
			isButtonsShow = false;
		}
	}
}

void OneVideo::leaveEvent(QEvent *event)
{
	Q_UNUSED(event);
	if (isButtonsShow)
	{
		// closeBtn->hide();
		// playBtn->hide();
		// cameraBtn->hide();
		// recordBtn->hide();
		// fullScreenBtn->hide();
		// rotateImageBtn->hide();
		// muteBtn->hide(); // 隐藏静音按钮
		isButtonsShow = false;
	}
}

void OneVideo::enterEvent(QEvent *event)
{
	Q_UNUSED(event);
	// 鼠标进入窗口时的处理逻辑
	// 这里不需要立即显示按钮，因为mouseMoveEvent会处理
	// 当鼠标移动到视频区域时才会显示按钮
}

// 实现旋转图像槽函数
void OneVideo::rotateImageBtnSlot()
{
	// 逆时针旋转90度 (角度递增90)
	rotationAngle = (rotationAngle + 90) % 360;

	// 重新应用旋转到当前图像
	if (isPlay && !centralImage.isNull())
	{
		// 先获取原始视频帧进行旋转，避免重复旋转导致的质量损失
		if (networkThread && networkThread->getLastFrame().isNull() == false)
		{
			QImage originalImage = networkThread->getLastFrame();
			QTransform transform;
			transform.rotate(rotationAngle);
			centralImage = originalImage.transformed(transform);
			update();
		}
	}
}

// 实现静音按钮槽函数
void OneVideo::muteButtonSlot()
{
	isMuted = !isMuted; // 切换静音状态

	// 根据静音状态切换图标
	if (isMuted)
	{
		muteBtn->setIcon(QIcon(":/images/mute.png"));
		// 如果有网络线程，设置音频静音
		if (networkThread != NULL && networkThread->isThreadRunning())
		{
			networkThread->setMuted(true);
			LOG_INFO("video muted");
		}
	}
	else
	{
		muteBtn->setIcon(QIcon(":/images/unmute.png"));
		// 如果有网络线程，取消音频静音
		if (networkThread != NULL && networkThread->isThreadRunning())
		{
			networkThread->setMuted(false);
			LOG_INFO("video unmuted");
		}
	}
}

// 处理窗口大小变化事件
void OneVideo::resizeEvent(QResizeEvent *event)
{
	QFrame::resizeEvent(event);

	// 计算nameLabel占用的空间
	// int labelOffset = 0;
	// if (nameLabel && nameLabel->isVisible())
	// {
	// 	labelOffset = nameLabel->height() + 5; // 标签高度加上一点额外空间
	// }

	// 外框始终保持横屏状态(16:9)，但避开标签区域
	// 计算外框的显示区域
	double idealHeight = width() * 9.0 / 16.0;
	// double idealWidth = (height() - labelOffset) * 16.0 / 9.0;
	double idealWidth = height() * 16.0 / 9.0;

	int frameX = 0;
	// int frameY = labelOffset; // 从标签下方开始绘制视频
	int frameY = 0; // 从标签下方开始绘制视频
	int frameWidth = width();
	// int frameHeight = height() - labelOffset;
	int frameHeight = height();

	// 调整外框以保持16:9的比例
	if (width() / frameHeight > 16.0 / 9.0)
	{
		// 宽度过大，以高度为准
		frameWidth = idealWidth;
		frameX = (width() - frameWidth) / 2;
	}
	else
	{
		// 高度过大，以宽度为准
		frameHeight = idealHeight;
		// frameY = labelOffset + ((height() - labelOffset) - frameHeight) / 2;
		frameY = (height() - frameHeight) / 2;
	}

	// 根据旋转角度计算视频的显示区域
	bool isVertical = (rotationAngle == 90 || rotationAngle == 270);
	int videoX, videoY, videoWidth, videoHeight;

	if (isVertical)
	{
		// 竖屏模式 (9:16) - 在横屏外框内居中显示
		// 计算在保持比例的情况下，视频的最大尺寸
		double videoRatio = 9.0 / 16.0; // 竖屏视频的宽高比

		// 首先尝试以高度为基准
		videoHeight = frameHeight;
		videoWidth = videoHeight * videoRatio;

		// 如果宽度超出了外框，则以宽度为基准
		if (videoWidth > frameWidth)
		{
			videoWidth = frameWidth;
			videoHeight = videoWidth / videoRatio;
		}

		// 计算视频在外框内的位置（居中）
		videoX = frameX + (frameWidth - videoWidth) / 2;
		videoY = frameY + (frameHeight - videoHeight) / 2;
	}
	else
	{
		// 横屏模式 (16:9) - 填满整个外框
		videoX = frameX;
		videoY = frameY;
		videoWidth = frameWidth;
		videoHeight = frameHeight;
	}

	// 使用updateLabelPositions方法更新所有标签位置
	updateLabelPositions();

	// 更新工具按钮布局 - 移动到外框内的右下角
	// QList<IconButton *> buttons = findChildren<IconButton *>();
	// int buttonSpacing = 10;	  // 按钮之间的间距
	// int buttonSize = 30;	  // 按钮大小
	// int totalButtonCount = 0; // 实际有效的按钮数量

	// 计算实际有效的按钮数量
	// for (auto btn : buttons)
	// {
	// 	if (btn != closeBtn && btn != playBtn)
	// 	{ // 不包括关闭按钮和播放按钮
	// 		totalButtonCount++;
	// 	}
	// }

	// int totalWidth = totalButtonCount * buttonSize + (totalButtonCount - 1) * buttonSpacing;

	// 计算按钮放置的起始位置 - 外框内的右下角
	// int buttonX = frameX + frameWidth - totalWidth - 10;  // 距离右边缘10像素
	// int buttonY = frameY + frameHeight - buttonSize - 10; // 距离底部边缘10像素

	// 放置按钮
	// int currentIndex = 0;
	// for (auto btn : buttons)
	// {
	// 	if (btn != closeBtn && btn != playBtn)
	// 	{										 // 不包括关闭按钮和播放按钮
	// 		btn->resize(buttonSize, buttonSize); // 设置按钮大小
	// 		btn->move(buttonX + currentIndex * (buttonSize + buttonSpacing), buttonY);
	// 		btn->raise(); // 确保按钮显示在最上层
	// 		currentIndex++;
	// 	}
	// }
}

// 处理上下文菜单事件
void OneVideo::contextMenuEvent(QContextMenuEvent *event)
{
	// 这里可以实现右键菜单功能
	// 如果不需要自定义右键菜单，可以调用父类的实现
	QFrame::contextMenuEvent(event);
}

// 设置RTSP URL并自动连接
void OneVideo::setRtspUrl(const QString &url)
{
	rtspUrl = url;

	// 如果当前正在播放，先停止
	if (isPlay)
	{
		stopNetwork();
	}

	// 使用定时器延迟连接，给摄像头一些准备时间
	QTimer *connectionTimer = new QTimer(this);
	connectionTimer->setSingleShot(true);
	connect(connectionTimer, &QTimer::timeout, this, [this, connectionTimer]()
			{
		// 使用新的startNetwork方法开始播放
		startNetwork();
		
		// 设置超时处理
		QTimer *watchdogTimer = new QTimer(this);
		watchdogTimer->setSingleShot(true);
		connect(watchdogTimer, &QTimer::timeout, this, [this, watchdogTimer]() {
			// 如果超时且线程仍然存在但没有成功连接
			if (networkThread && !isPlay) {
				LOG_INFO("connect timeout, terminate thread");
				disconnectSlot();
				watchdogTimer->deleteLater();
			}
		});
		
		watchdogTimer->start(10000); // 10秒连接超时
		
		// 删除定时器
		connectionTimer->deleteLater(); });
	connectionTimer->start(500); // 从2000减少到500毫秒，减少延迟时间
}

// 设置摄像头名称
void OneVideo::setCameraName(const QString &name)
{
	cameraName = name;
	updateTimeDisplay(); // 立即更新显示
}

// 更新时间显示
void OneVideo::updateTimeDisplay()
{
	// 获取当前时间并格式化（使用更紧凑的格式）
	QString currentTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");

	// 更新时间标签显示
	ipPortLabel->setText(currentTime);

	// 更新摄像头名称标签
	// if (nameLabel && !cameraName.isEmpty())
	// {
	// 	// 显示摄像头名称
	// 	nameLabel->setText(cameraName);
	// 	nameLabel->adjustSize();

	// 	// 确保标签显示在顶层
	// 	nameLabel->raise();

	// 	// 确保标签可见
	// 	nameLabel->show();
	// }

	// 重新计算标签位置，确保时间标签保持在正确位置
	updateLabelPositions();
}

// 新增方法：更新所有标签位置
void OneVideo::updateLabelPositions()
{
	// 计算nameLabel占用的空间
	//	int labelOffset = 0;
	//	if (nameLabel && nameLabel->isVisible())
	//	{
	//		labelOffset = nameLabel->height() + 5; // 标签高度加上一点额外空间
	//	}

	// 计算视频框的位置和大小，与其他方法保持一致
	double idealHeight = width() * 9.0 / 16.0;
	// double idealWidth = (height() - labelOffset) * 16.0 / 9.0;
	double idealWidth = height() * 16.0 / 9.0;

	int frameX = 0;
	// int frameY = labelOffset; // 从标签下方开始
	int frameY = 0; // 从标签下方开始
	int frameWidth = width();
	// int frameHeight = height() - labelOffset;
	int frameHeight = height();

	// 调整外框以保持16:9的比例
	if (width() / frameHeight > 16.0 / 9.0)
	{
		// 宽度过大，以高度为准
		frameWidth = idealWidth;
		frameX = (width() - frameWidth) / 2;
	}
	else
	{
		// 高度过大，以宽度为准
		frameHeight = idealHeight;
		// frameY = labelOffset + ((height() - labelOffset) - frameHeight) / 2;
		frameY = (height() - frameHeight) / 2;
	}

	// 更新时间标签位置
	if (ipPortLabel)
	{
		// 设置标签宽度为固定值
		ipPortLabel->resize(150, 22);

		// 放置在视频外框内部的右上角，考虑到视频区域的实际位置
		ipPortLabel->move(frameX + frameWidth - ipPortLabel->width() - 5, frameY + 5);

		// 确保时间标签始终显示在视频内，不超出视频边界
		if (ipPortLabel->x() < frameX)
			ipPortLabel->move(frameX + 5, ipPortLabel->y());
		if (ipPortLabel->y() < frameY)
			ipPortLabel->move(ipPortLabel->x(), frameY + 5);

		// 确保显示在顶层，但在摄像头名称标签之下
		ipPortLabel->raise();
	}

	// 确保摄像头名称标签在最上层
	// if (nameLabel && nameLabel->isVisible())
	// {
	// 	nameLabel->raise();
	// }
}

// 实现stopNetwork方法
void OneVideo::stopNetwork()
{
	if (networkThread != NULL)
	{
		// 如果正在录制，先停止录制
		if (isRecording)
		{
			stopRecording();
		}

		// 使用安全停止方法
		if (networkThread->safeStop(1000)) // 等待最多1秒
		{
			LOG_DEBUG("Network thread stopped safely");
		}
		else
		{
			LOG_WARNING("Network thread could not be stopped safely, forcing termination");
			networkThread->terminate();
			networkThread->wait(500);
		}

		// 不在这里删除线程，留给析构函数处理
		isPlay = false;

		// 恢复为黑色图像
		centralImage = QImage(WIDTH, HEIGHT, QImage::Format_RGB32);
		centralImage.fill(Qt::black);
		update();
	}
}

// 实现startNetwork方法
void OneVideo::startNetwork()
{
	// 如果已经有线程在运行，先停止
	if (networkThread != NULL)
	{
		stopNetwork();
		networkThread->deleteLater();
		networkThread = NULL;
	}

	if (!rtspUrl.isEmpty())
	{
		networkThread = new MyThread(rtspUrl);
		connect(networkThread, SIGNAL(disconnectSlot()), SLOT(disconnectSlot()));
		connect(networkThread, SIGNAL(transmitData(QImage)), SLOT(updateImage(QImage)));
		networkThread->start();

		// cameraBtn->setEnabled(true);
		// recordBtn->setEnabled(true);
		// fullScreenBtn->setEnabled(true);
		// rotateImageBtn->setEnabled(true);
		// muteBtn->setEnabled(true);
		isPlay = true;

		// 设置初始静音状态
		networkThread->setMuted(isMuted);
	}
}

void OneVideo::takeScreenshot()
{
	cameraBtnSlot();
}

void OneVideo::toggleRecording()
{
	recordBtnSlot();
}

void OneVideo::toggleListen()
{
	// 如果没有播放视频，不执行任何操作
	if (!isPlay || !networkThread)
	{
		QMessageBox::information(this, "窃听", "当前没有可用的视频源");
		return;
	}

	// 切换窃听状态
	if (!isListening)
	{
		// 开始窃听
		startListening();
	}
	else
	{
		// 停止窃听
		stopListening();
	}
}

void OneVideo::startListening()
{
	// 开启音频播放（取消静音）
	isMuted = false;
	isListening = true;

	// 更新静音按钮图标
	muteBtn->setIcon(QIcon(":/images/unmute.png"));

	// 如果有网络线程，取消音频静音
	if (networkThread != NULL && networkThread->isThreadRunning())
	{
		networkThread->setMuted(false);
		LOG_INFO("Audio listening started - unmuted");
	}

	QMessageBox::information(this, "窃听", "音频窃听已开启");
}

void OneVideo::stopListening()
{
	// 关闭音频播放（设置静音）
	isMuted = true;
	isListening = false;

	// 更新静音按钮图标
	muteBtn->setIcon(QIcon(":/images/mute.png"));

	// 如果有网络线程，设置音频静音
	if (networkThread != NULL && networkThread->isThreadRunning())
	{
		networkThread->setMuted(true);
		LOG_INFO("Audio listening stopped - muted");
	}

	QMessageBox::information(this, "窃听", "音频窃听已关闭");
}
