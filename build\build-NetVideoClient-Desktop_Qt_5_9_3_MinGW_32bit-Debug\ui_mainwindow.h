/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.9.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QWidget *bannerWidget;
    QHBoxLayout *bannerLayout;
    QSpacerItem *horizontalSpacer;
    QLabel *titleLabel;
    QSpacerItem *horizontalSpacer_2;
    QSplitter *mainSplitter;
    QWidget *leftWidget;
    QWidget *centerWidget;
    QVBoxLayout *centerLayout;
    QLabel *monitorTitleLabel;
    QWidget *displayModeBar;
    QHBoxLayout *modeLayout;
    QSpacerItem *horizontalSpacer_3;
    QSpacerItem *horizontalSpacer_4;
    QWidget *mainContent;
    QWidget *rightWidget;
    QVBoxLayout *rightLayout;
    QSpacerItem *verticalSpacer_2;
    QPushButton *eavesdropButton;
    QPushButton *listenButton;
    QPushButton *screenshotButton;
    QPushButton *recordButton;
    QSpacerItem *verticalSpacer;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QStringLiteral("MainWindow"));
        MainWindow->resize(1200, 800);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QStringLiteral("centralwidget"));
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        bannerWidget = new QWidget(centralwidget);
        bannerWidget->setObjectName(QStringLiteral("bannerWidget"));
        bannerWidget->setMinimumSize(QSize(0, 40));
        bannerWidget->setMaximumSize(QSize(16777215, 40));
        bannerLayout = new QHBoxLayout(bannerWidget);
        bannerLayout->setSpacing(0);
        bannerLayout->setObjectName(QStringLiteral("bannerLayout"));
        bannerLayout->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        bannerLayout->addItem(horizontalSpacer);

        titleLabel = new QLabel(bannerWidget);
        titleLabel->setObjectName(QStringLiteral("titleLabel"));
        titleLabel->setAlignment(Qt::AlignCenter);

        bannerLayout->addWidget(titleLabel);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        bannerLayout->addItem(horizontalSpacer_2);


        verticalLayout->addWidget(bannerWidget);

        mainSplitter = new QSplitter(centralwidget);
        mainSplitter->setObjectName(QStringLiteral("mainSplitter"));
        mainSplitter->setOrientation(Qt::Horizontal);
        leftWidget = new QWidget(mainSplitter);
        leftWidget->setObjectName(QStringLiteral("leftWidget"));
        leftWidget->setMinimumSize(QSize(350, 0));
        mainSplitter->addWidget(leftWidget);
        centerWidget = new QWidget(mainSplitter);
        centerWidget->setObjectName(QStringLiteral("centerWidget"));
        centerLayout = new QVBoxLayout(centerWidget);
        centerLayout->setSpacing(0);
        centerLayout->setObjectName(QStringLiteral("centerLayout"));
        centerLayout->setContentsMargins(0, 0, 0, 0);
        monitorTitleLabel = new QLabel(centerWidget);
        monitorTitleLabel->setObjectName(QStringLiteral("monitorTitleLabel"));
        monitorTitleLabel->setMinimumSize(QSize(0, 30));
        monitorTitleLabel->setMaximumSize(QSize(16777215, 30));
        monitorTitleLabel->setAlignment(Qt::AlignCenter);

        centerLayout->addWidget(monitorTitleLabel);

        displayModeBar = new QWidget(centerWidget);
        displayModeBar->setObjectName(QStringLiteral("displayModeBar"));
        displayModeBar->setMinimumSize(QSize(0, 50));
        displayModeBar->setMaximumSize(QSize(16777215, 50));
        modeLayout = new QHBoxLayout(displayModeBar);
        modeLayout->setSpacing(5);
        modeLayout->setObjectName(QStringLiteral("modeLayout"));
        modeLayout->setContentsMargins(5, -1, 5, -1);
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        modeLayout->addItem(horizontalSpacer_3);

        horizontalSpacer_4 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        modeLayout->addItem(horizontalSpacer_4);


        centerLayout->addWidget(displayModeBar);

        mainContent = new QWidget(centerWidget);
        mainContent->setObjectName(QStringLiteral("mainContent"));
        mainContent->setStyleSheet(QStringLiteral("background: transparent;"));

        centerLayout->addWidget(mainContent);

        mainSplitter->addWidget(centerWidget);
        rightWidget = new QWidget(mainSplitter);
        rightWidget->setObjectName(QStringLiteral("rightWidget"));
        rightWidget->setMinimumSize(QSize(200, 0));
        rightWidget->setMaximumSize(QSize(200, 16777215));
        rightLayout = new QVBoxLayout(rightWidget);
        rightLayout->setSpacing(15);
        rightLayout->setObjectName(QStringLiteral("rightLayout"));
        rightLayout->setContentsMargins(15, 30, 15, 30);
        verticalSpacer_2 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        rightLayout->addItem(verticalSpacer_2);

        eavesdropButton = new QPushButton(rightWidget);
        eavesdropButton->setObjectName(QStringLiteral("eavesdropButton"));
        eavesdropButton->setMinimumSize(QSize(170, 80));
        eavesdropButton->setMaximumSize(QSize(170, 80));
        eavesdropButton->setStyleSheet(QLatin1String("QPushButton { \n"
"    background-color: #003366; \n"
"    color: white; \n"
"    border: 2px solid #00FFFF; \n"
"    border-radius: 10px; \n"
"    font-weight: bold; \n"
"    font-size: 16px; \n"
"} \n"
"QPushButton:hover { \n"
"    background-color: #004080; \n"
"    border-color: #00FFFF; \n"
"} \n"
"QPushButton:pressed { \n"
"    background-color: #002040; \n"
"}"));

        rightLayout->addWidget(eavesdropButton);

        listenButton = new QPushButton(rightWidget);
        listenButton->setObjectName(QStringLiteral("listenButton"));
        listenButton->setMinimumSize(QSize(170, 80));
        listenButton->setMaximumSize(QSize(170, 80));
        listenButton->setStyleSheet(QLatin1String("QPushButton { \n"
"    background-color: #003366; \n"
"    color: white; \n"
"    border: 2px solid #00FFFF; \n"
"    border-radius: 10px; \n"
"    font-weight: bold; \n"
"    font-size: 16px; \n"
"} \n"
"QPushButton:hover { \n"
"    background-color: #004080; \n"
"    border-color: #00FFFF; \n"
"} \n"
"QPushButton:pressed { \n"
"    background-color: #002040; \n"
"}"));

        rightLayout->addWidget(listenButton);

        screenshotButton = new QPushButton(rightWidget);
        screenshotButton->setObjectName(QStringLiteral("screenshotButton"));
        screenshotButton->setMinimumSize(QSize(170, 80));
        screenshotButton->setMaximumSize(QSize(170, 80));
        screenshotButton->setStyleSheet(QLatin1String("QPushButton { \n"
"    background-color: #003366; \n"
"    color: white; \n"
"    border: 2px solid #00FFFF; \n"
"    border-radius: 10px; \n"
"    font-weight: bold; \n"
"    font-size: 16px; \n"
"} \n"
"QPushButton:hover { \n"
"    background-color: #004080; \n"
"    border-color: #00FFFF; \n"
"} \n"
"QPushButton:pressed { \n"
"    background-color: #002040; \n"
"}"));

        rightLayout->addWidget(screenshotButton);

        recordButton = new QPushButton(rightWidget);
        recordButton->setObjectName(QStringLiteral("recordButton"));
        recordButton->setMinimumSize(QSize(170, 80));
        recordButton->setMaximumSize(QSize(170, 80));
        recordButton->setStyleSheet(QLatin1String("QPushButton { \n"
"    background-color: #003366; \n"
"    color: white; \n"
"    border: 2px solid #00FFFF; \n"
"    border-radius: 10px; \n"
"    font-weight: bold; \n"
"    font-size: 16px; \n"
"} \n"
"QPushButton:hover { \n"
"    background-color: #004080; \n"
"    border-color: #00FFFF; \n"
"} \n"
"QPushButton:pressed { \n"
"    background-color: #002040; \n"
"}"));

        rightLayout->addWidget(recordButton);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        rightLayout->addItem(verticalSpacer);

        mainSplitter->addWidget(rightWidget);

        verticalLayout->addWidget(mainSplitter);

        MainWindow->setCentralWidget(centralwidget);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName(QStringLiteral("statusbar"));
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QApplication::translate("MainWindow", "MainWindow", Q_NULLPTR));
        titleLabel->setText(QApplication::translate("MainWindow", "\346\221\204\345\203\217\345\244\264\347\233\221\346\216\247\347\263\273\347\273\237", Q_NULLPTR));
        monitorTitleLabel->setText(QApplication::translate("MainWindow", "\347\233\221\346\216\247\347\224\273\351\235\242", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        eavesdropButton->setToolTip(QApplication::translate("MainWindow", "\347\252\203\350\247\206\345\212\237\350\203\275", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        eavesdropButton->setText(QApplication::translate("MainWindow", "\347\252\203\350\247\206", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        listenButton->setToolTip(QApplication::translate("MainWindow", "\347\252\203\345\220\254\345\212\237\350\203\275", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        listenButton->setText(QApplication::translate("MainWindow", "\347\252\203\345\220\254", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        screenshotButton->setToolTip(QApplication::translate("MainWindow", "\346\210\252\345\261\217\345\212\237\350\203\275", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        screenshotButton->setText(QApplication::translate("MainWindow", "\346\210\252\345\261\217", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        recordButton->setToolTip(QApplication::translate("MainWindow", "\345\275\225\345\203\217\345\212\237\350\203\275", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        recordButton->setText(QApplication::translate("MainWindow", "\345\275\225\345\203\217", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
