<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CameraScanWidget</class>
 <widget class="QWidget" name="CameraScanWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>600</height>
   </rect>
  </property>
  <property name="styleSheet">
   <string notr="true">CameraScanWidget { background-image: url(:/images/list_bg.png); background-position: center; background-repeat: repeat; background-color: #001F3F; }</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="text">
      <string>摄像头配置</string>
     </property>
     <property name="styleSheet">
      <string notr="true">QLabel { color: #FFFFFF; background-color: #003366; padding: 5px; font-weight: bold; font-size: 14px; }</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="progressLayout">
     <property name="spacing">
      <number>10</number>
     </property>
     <property name="leftMargin">
      <number>9</number>
     </property>
     <property name="topMargin">
      <number>5</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <property name="bottomMargin">
      <number>5</number>
     </property>
     <item>
      <widget class="QLabel" name="statusLabel">
       <property name="text">
        <string>准备扫描...</string>
       </property>
       <property name="styleSheet">
        <string notr="true">QLabel { color: #00FFFF; font-weight: bold; padding: 3px; font-size: 12px; }</string>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>1</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="rescanButton">
       <property name="minimumSize">
        <size>
         <width>85</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>85</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>重新扫描</string>
       </property>
       <property name="toolTip">
        <string>重新扫描网络中的摄像头</string>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton { background-color: #003366; color: #FFFFFF; border: 1px solid #00FFFF; border-radius: 3px; padding: 4px; font-size: 12px; font-weight: bold; } 
QPushButton:hover { background-color: #004080; } 
QPushButton:pressed { background-color: #002040; }</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="tableContainer" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget { background-color: transparent; }</string>
     </property>
     <layout class="QHBoxLayout" name="tableLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>9</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>9</number>
      </property>
      <property name="bottomMargin">
       <number>9</number>
      </property>
      <item>
       <widget class="QTableWidget" name="cameraTableWidget">
        <property name="styleSheet">
         <string notr="true">QTableWidget { border: none; background-color: rgba(0, 30, 60, 150); } 
QHeaderView::section { background-color: #003366; color: #00FFFF; border: none; padding: 4px; font-weight: bold; font-size: 13px; } 
QTableWidget::item { border-bottom: 1px solid rgba(0, 255, 255, 40); padding: 2px; color: white; font-size: 12px; background-color: transparent; }
QTableWidget::item:selected { background-color: rgba(0, 128, 255, 120); color: white; }
QTableWidget::item:focus { outline: none; }
QLineEdit { background-color: #001F3F; color: white; selection-background-color: #0078D7; selection-color: white; }</string>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="selectionMode">
         <enum>QAbstractItemView::SingleSelection</enum>
        </property>
        <property name="editTriggers">
         <set>QAbstractItemView::DoubleClicked|QAbstractItemView::EditKeyPressed|QAbstractItemView::SelectedClicked</set>
        </property>
        <property name="contextMenuPolicy">
         <enum>Qt::CustomContextMenu</enum>
        </property>
        <property name="horizontalScrollBarPolicy">
         <enum>Qt::ScrollBarAlwaysOff</enum>
        </property>
        <property name="alternatingRowColors">
         <bool>false</bool>
        </property>
        <property name="showGrid">
         <bool>false</bool>
        </property>
        <attribute name="horizontalHeaderStretchLastSection">
         <bool>false</bool>
        </attribute>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 