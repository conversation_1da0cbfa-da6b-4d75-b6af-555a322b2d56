#include "logmanager.h"
#include "configmanager.h"
#include <QCoreApplication>
#include <QDir>
#include <QMutexLocker>
#include <QDebug>
#include <QStandardPaths>
#include <QFileInfo>
#include <QSettings>

// 静态标志，防止递归调用
static bool g_isLogging = false;
static bool g_isInitializing = false;

LogManager::LogManager(QObject *parent) : QObject(parent), m_logLevel(Debug), m_logFileNameFormat("log_%date%.txt")
{
    // 不在构造函数中执行任何可能阻塞的操作
    // 日志目录和文件将在init()方法中创建
}

LogManager::~LogManager()
{
    // 简单关闭文件，不执行其他操作
    if (m_logFile.isOpen())
    {
        m_logFile.close();
    }
}

LogManager &LogManager::instance()
{
    static LogManager instance;
    return instance;
}

bool LogManager::init()
{
    // 防止重复初始化或递归初始化
    if (g_isInitializing)
    {
        qDebug() << "LogManager::init() - 递归初始化检测";
        return false;
    }

    static bool initialized = false;
    if (initialized)
    {
        return true;
    }

    g_isInitializing = true;

    try
    {
        // 设置日志目录
        m_logDir = QCoreApplication::applicationDirPath() + "/logs";

        // 如果应用目录不可写，则使用用户文档目录
        QFileInfo appDirInfo(QCoreApplication::applicationDirPath());
        if (!appDirInfo.isWritable())
        {
            m_logDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LiveCamera/logs";
        }

        // 创建日志目录
        QDir dir;
        if (!dir.exists(m_logDir))
        {
            if (!dir.mkpath(m_logDir))
            {
                qDebug() << "LogManager::init() - 无法创建日志目录:" << m_logDir;
                g_isInitializing = false;
                return false;
            }
        }

        // 从config.ini读取设置
        try
        {
            QString configPath = QCoreApplication::applicationDirPath() + "/config.ini";
            QSettings settings(configPath, QSettings::IniFormat);

            // 读取日志级别
            int level = settings.value("Log/LogLevel", 0).toInt();
            m_logLevel = static_cast<LogLevel>(level);

            // 读取日志文件名格式
            m_logFileNameFormat = settings.value("Log/LogFileName", "log_%date%.txt").toString();

            qDebug() << "LogManager::init() - 从配置文件加载设置 - 日志级别:" << level << "日志文件名格式:" << m_logFileNameFormat;
        }
        catch (...)
        {
            qDebug() << "LogManager::init() - 加载配置失败，使用默认设置";
            m_logLevel = Debug;                     // 默认级别
            m_logFileNameFormat = "log_%date%.txt"; // 默认文件名格式
        }

        // 创建日志文件
        m_currentDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        QString logFileName = getCurrentLogFileName();

        m_logFile.setFileName(logFileName);
        if (!m_logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
        {
            qDebug() << "LogManager::init() - 无法打开日志文件:" << logFileName;
            g_isInitializing = false;
            return false;
        }

        // 设置文本流
        m_textStream.setDevice(&m_logFile);
        m_textStream.setCodec("UTF-8");

        // 写入初始日志
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        QString logMessage = QString("[%1] [INFO] 日志系统初始化完成，日志级别：%2，日志文件：%3")
                                 .arg(timestamp)
                                 .arg(levelToString(m_logLevel))
                                 .arg(logFileName);
        m_textStream << logMessage << "\n";
        m_textStream.flush();
        qDebug() << logMessage;

        // 标记为已初始化
        initialized = true;
        g_isInitializing = false;
        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "LogManager::init() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "LogManager::init() - 未知异常";
    }

    g_isInitializing = false;
    return false;
}

void LogManager::setLogLevel(LogLevel level)
{
    QMutexLocker locker(&m_mutex);
    m_logLevel = level;

    // 直接保存日志级别到配置文件
    try
    {
        QString configPath = QCoreApplication::applicationDirPath() + "/config.ini";
        QSettings settings(configPath, QSettings::IniFormat);
        settings.setValue("Log/LogLevel", static_cast<int>(level));
        settings.sync();

        // 记录日志级别变更
        QString logMessage = QString("日志级别已设置为: %1").arg(levelToString(level));
        writeLog(Info, logMessage);
    }
    catch (...)
    {
        qDebug() << "LogManager::setLogLevel - 保存日志级别失败";
    }
}

LogManager::LogLevel LogManager::getLogLevel() const
{
    return m_logLevel;
}

void LogManager::setLogFileName(const QString &format)
{
    QMutexLocker locker(&m_mutex);
    m_logFileNameFormat = format;

    // 保存到配置文件
    try
    {
        QString configPath = QCoreApplication::applicationDirPath() + "/config.ini";
        QSettings settings(configPath, QSettings::IniFormat);
        settings.setValue("Log/LogFileName", format);
        settings.sync();

        // 记录变更
        QString logMessage = QString("日志文件名格式已设置为: %1").arg(format);
        writeLog(Info, logMessage);
    }
    catch (...)
    {
        qDebug() << "LogManager::setLogFileName - 保存日志文件名格式失败";
    }
}

QString LogManager::getLogFileName() const
{
    return m_logFileNameFormat;
}

QString LogManager::getCurrentLogFileName() const
{
    // 替换日期占位符
    QString fileName = m_logFileNameFormat;
    fileName.replace("%date%", m_currentDate);

    // 返回完整路径
    return QString("%1/%2").arg(m_logDir).arg(fileName);
}

void LogManager::writeLog(LogLevel level, const QString &message)
{
    // 防止递归调用
    if (g_isLogging)
    {
        return;
    }

    // 检查日志级别
    if (level < m_logLevel)
    {
        return;
    }

    // 检查日志文件是否打开
    if (!m_logFile.isOpen())
    {
        return;
    }

    g_isLogging = true;

    try
    {
        QMutexLocker locker(&m_mutex);

        // 检查日期是否变化，如果变化则创建新的日志文件
        QString currentDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        if (currentDate != m_currentDate)
        {
            // 关闭旧文件
            if (m_logFile.isOpen())
            {
                m_logFile.close();
            }

            // 创建新文件
            m_currentDate = currentDate;
            QString logFileName = getCurrentLogFileName();

            m_logFile.setFileName(logFileName);
            if (!m_logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text))
            {
                g_isLogging = false;
                return;
            }

            // 重新设置文本流
            m_textStream.setDevice(&m_logFile);
            m_textStream.setCodec("UTF-8");
        }

        // 格式化日志消息
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
        QString logMessage = QString("[%1] [%2] %3")
                                 .arg(timestamp)
                                 .arg(levelToString(level))
                                 .arg(message);

        // 写入日志
        m_textStream << logMessage << "\n";
        m_textStream.flush();

        // 同时输出到控制台
        qDebug() << logMessage;
    }
    catch (...)
    {
        // 忽略所有异常
    }

    g_isLogging = false;
}

void LogManager::close()
{
    QMutexLocker locker(&m_mutex);
    if (m_logFile.isOpen())
    {
        m_logFile.close();
    }
}

QString LogManager::levelToString(LogLevel level)
{
    switch (level)
    {
    case Debug:
        return "DEBUG";
    case Info:
        return "INFO";
    case Warning:
        return "WARNING";
    case Error:
        return "ERROR";
    case Fatal:
        return "FATAL";
    default:
        return "UNKNOWN";
    }
}

bool LogManager::createLogDir()
{
    // 此方法不再使用，保留为兼容
    return true;
}

bool LogManager::createLogFile()
{
    // 此方法不再使用，保留为兼容
    return true;
}
