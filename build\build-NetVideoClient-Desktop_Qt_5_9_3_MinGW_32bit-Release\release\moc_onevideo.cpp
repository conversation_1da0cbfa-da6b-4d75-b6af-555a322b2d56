/****************************************************************************
** Meta object code from reading C++ file 'onevideo.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../onevideo.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'onevideo.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_OneVideo_t {
    QByteArrayData data[17];
    char stringdata0[221];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_OneVideo_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_OneVideo_t qt_meta_stringdata_OneVideo = {
    {
QT_MOC_LITERAL(0, 0, 8), // "OneVideo"
QT_MOC_LITERAL(1, 9, 11), // "closeSignal"
QT_MOC_LITERAL(2, 21, 0), // ""
QT_MOC_LITERAL(3, 22, 9), // "OneVideo*"
QT_MOC_LITERAL(4, 32, 3), // "who"
QT_MOC_LITERAL(5, 36, 12), // "disconnected"
QT_MOC_LITERAL(6, 49, 13), // "cameraBtnSlot"
QT_MOC_LITERAL(7, 63, 13), // "recordBtnSlot"
QT_MOC_LITERAL(8, 77, 17), // "fullScreenBtnSlot"
QT_MOC_LITERAL(9, 95, 18), // "rotateImageBtnSlot"
QT_MOC_LITERAL(10, 114, 14), // "muteButtonSlot"
QT_MOC_LITERAL(11, 129, 11), // "updateImage"
QT_MOC_LITERAL(12, 141, 5), // "image"
QT_MOC_LITERAL(13, 147, 14), // "disconnectSlot"
QT_MOC_LITERAL(14, 162, 17), // "updateTimeDisplay"
QT_MOC_LITERAL(15, 180, 19), // "updateRecordingTime"
QT_MOC_LITERAL(16, 200, 20) // "updateLabelPositions"

    },
    "OneVideo\0closeSignal\0\0OneVideo*\0who\0"
    "disconnected\0cameraBtnSlot\0recordBtnSlot\0"
    "fullScreenBtnSlot\0rotateImageBtnSlot\0"
    "muteButtonSlot\0updateImage\0image\0"
    "disconnectSlot\0updateTimeDisplay\0"
    "updateRecordingTime\0updateLabelPositions"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_OneVideo[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x06 /* Public */,
       5,    0,   77,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,   78,    2, 0x08 /* Private */,
       7,    0,   79,    2, 0x08 /* Private */,
       8,    0,   80,    2, 0x08 /* Private */,
       9,    0,   81,    2, 0x08 /* Private */,
      10,    0,   82,    2, 0x08 /* Private */,
      11,    1,   83,    2, 0x08 /* Private */,
      13,    0,   86,    2, 0x08 /* Private */,
      14,    0,   87,    2, 0x08 /* Private */,
      15,    0,   88,    2, 0x08 /* Private */,
      16,    0,   89,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QImage,   12,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void OneVideo::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        OneVideo *_t = static_cast<OneVideo *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->closeSignal((*reinterpret_cast< OneVideo*(*)>(_a[1]))); break;
        case 1: _t->disconnected(); break;
        case 2: _t->cameraBtnSlot(); break;
        case 3: _t->recordBtnSlot(); break;
        case 4: _t->fullScreenBtnSlot(); break;
        case 5: _t->rotateImageBtnSlot(); break;
        case 6: _t->muteButtonSlot(); break;
        case 7: _t->updateImage((*reinterpret_cast< QImage(*)>(_a[1]))); break;
        case 8: _t->disconnectSlot(); break;
        case 9: _t->updateTimeDisplay(); break;
        case 10: _t->updateRecordingTime(); break;
        case 11: _t->updateLabelPositions(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 0:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< OneVideo* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (OneVideo::*_t)(OneVideo * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&OneVideo::closeSignal)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (OneVideo::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&OneVideo::disconnected)) {
                *result = 1;
                return;
            }
        }
    }
}

const QMetaObject OneVideo::staticMetaObject = {
    { &QFrame::staticMetaObject, qt_meta_stringdata_OneVideo.data,
      qt_meta_data_OneVideo,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *OneVideo::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *OneVideo::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_OneVideo.stringdata0))
        return static_cast<void*>(this);
    return QFrame::qt_metacast(_clname);
}

int OneVideo::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QFrame::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void OneVideo::closeSignal(OneVideo * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void OneVideo::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
