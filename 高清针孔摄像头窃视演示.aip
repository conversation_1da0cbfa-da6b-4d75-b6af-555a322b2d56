<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<DOCUMENT Type="Advanced Installer" CreateVersion="19.7" version="19.7" Modules="enterprise" RootPath="." Language="zh" Id="{7CC0F84F-9E1D-4BFA-A3B3-2E36F27166AE}">
  <COMPONENT cid="caphyon.advinst.msicomp.ProjectOptionsComponent">
    <ROW Name="HiddenItems" Value="AppXProductDetailsComponent;AppXDependenciesComponent;AppXAppDetailsComponent;AppXVisualAssetsComponent;AppXCapabilitiesComponent;AppXAppDeclarationsComponent;AppXUriRulesComponent;SccmComponent;ActSyncAppComponent;CPLAppletComponent;AutorunComponent;GameUxComponent;SilverlightSlnComponent;SharePointSlnComponent;FixupComponent;MsiXDiffComponent;MsixManifestEditorComponent"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiPropsComponent">
    <ROW Property="AI_BITMAP_DISPLAY_MODE" Value="0"/>
    <ROW Property="AI_CURRENT_YEAR" Value="2025" ValueLocId="-"/>
    <ROW Property="AI_EXTERNALUIUNINSTALLERNAME" MultiBuildValue="DefaultBuild:aiui"/>
    <ROW Property="AI_FINDEXE_TITLE" Value="选择 [|ProductName] 安装包" ValueLocId="AI.Property.FindExeTitle"/>
    <ROW Property="AI_PACKAGING_TOOL" Value="Advanced Installer 19.7 build b6fe2030" ValueLocId="-"/>
    <ROW Property="AI_PRODUCTNAME_ARP" Value="高清针孔摄像头窃视演示"/>
    <ROW Property="AI_ThemeStyle" Value="aero" MultiBuildValue="DefaultBuild:classic" MsiKey="AI_ThemeStyle"/>
    <ROW Property="AI_UNINSTALLER" Value="msiexec.exe"/>
    <ROW Property="ALLUSERS" Value="1"/>
    <ROW Property="ARPCOMMENTS" Value="此安装程序数据库包含了安装 [|ProductName] 所需的逻辑和数据。" ValueLocId="*"/>
    <ROW Property="ARPNOREPAIR" Value="1" MultiBuildValue="DefaultBuild:1"/>
    <ROW Property="ARPPRODUCTICON" Value="app.exe" Type="8"/>
    <ROW Property="ARPSYSTEMCOMPONENT" Value="1"/>
    <ROW Property="Manufacturer" Value="XX_QMYS"/>
    <ROW Property="ProductCode" Value="2052:{8D135E89-7B7A-4BFE-B2FF-A4A5143664ED} " Type="16"/>
    <ROW Property="ProductLanguage" Value="1033"/>
    <ROW Property="ProductName" Value="高清针孔摄像头窃视演示"/>
    <ROW Property="ProductVersion" Value="1.0.0"/>
    <ROW Property="SecureCustomProperties" Value="OLDPRODUCTS;AI_NEWERPRODUCTFOUND;AI_SETUPEXEPATH;SETUPEXEDIR"/>
    <ROW Property="UpgradeCode" Value="{164360B9-83FE-4462-AD36-EE99629202E5}"/>
    <ROW Property="WindowsType9X" MultiBuildValue="DefaultBuild:Windows 9x/ME" ValueLocId="-"/>
    <ROW Property="WindowsType9XDisplay" MultiBuildValue="DefaultBuild:Windows 9x/ME" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT40" MultiBuildValue="DefaultBuild:Windows NT 4.0" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT40Display" MultiBuildValue="DefaultBuild:Windows NT 4.0" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT50" MultiBuildValue="DefaultBuild:Windows 2000" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT50Display" MultiBuildValue="DefaultBuild:Windows 2000" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT5X" MultiBuildValue="DefaultBuild:Windows XP/2003" ValueLocId="-"/>
    <ROW Property="WindowsTypeNT5XDisplay" MultiBuildValue="DefaultBuild:Windows XP/2003" ValueLocId="-"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiDirsComponent">
    <ROW Directory="APPDIR" Directory_Parent="TARGETDIR" DefaultDir="APPDIR:." IsPseudoRoot="1"/>
    <ROW Directory="SHORTCUTDIR" Directory_Parent="TARGETDIR" DefaultDir="SHORTC~1|SHORTCUTDIR" IsPseudoRoot="1"/>
    <ROW Directory="Screenshots_Dir" Directory_Parent="APPDIR" DefaultDir="SCREEN~1|Screenshots"/>
    <ROW Directory="TARGETDIR" DefaultDir="SourceDir"/>
    <ROW Directory="Videos_Dir" Directory_Parent="APPDIR" DefaultDir="Videos"/>
    <ROW Directory="audio_Dir" Directory_Parent="APPDIR" DefaultDir="audio"/>
    <ROW Directory="bearer_Dir" Directory_Parent="APPDIR" DefaultDir="bearer"/>
    <ROW Directory="iconengines_Dir" Directory_Parent="APPDIR" DefaultDir="ICONEN~1|iconengines"/>
    <ROW Directory="imageformats_Dir" Directory_Parent="APPDIR" DefaultDir="IMAGEF~1|imageformats"/>
    <ROW Directory="logs_Dir" Directory_Parent="APPDIR" DefaultDir="logs"/>
    <ROW Directory="mediaservice_Dir" Directory_Parent="APPDIR" DefaultDir="MEDIAS~1|mediaservice"/>
    <ROW Directory="platforms_Dir" Directory_Parent="APPDIR" DefaultDir="PLATFO~1|platforms"/>
    <ROW Directory="playlistformats_Dir" Directory_Parent="APPDIR" DefaultDir="PLAYLI~1|playlistformats"/>
    <ROW Directory="sqldrivers_Dir" Directory_Parent="APPDIR" DefaultDir="SQLDRI~1|sqldrivers"/>
    <ROW Directory="translations_Dir" Directory_Parent="APPDIR" DefaultDir="TRANSL~1|translations"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCompsComponent">
    <ROW Component="AI_CustomARPName" ComponentId="{880083E2-DD5A-4502-81AA-8AEAAD453524}" Directory_="APPDIR" Attributes="4" KeyPath="DisplayName" Options="1"/>
    <ROW Component="AI_ExePath" ComponentId="{7D10A180-29AB-4FA1-A211-37E22C3F0A4D}" Directory_="APPDIR" Attributes="4" KeyPath="AI_ExePath"/>
    <ROW Component="APPDIR" ComponentId="{8DBDBCF2-F243-4DC4-A6D1-1FD18C98789C}" Directory_="APPDIR" Attributes="0"/>
    <ROW Component="AuthCheck.dll" ComponentId="{590D264C-7817-47C5-AE36-8C497FA8C173}" Directory_="APPDIR" Attributes="0" KeyPath="AuthCheck.dll"/>
    <ROW Component="CameraVideo.exe" ComponentId="{FA3BEC94-3C0E-44BF-9DA9-4F3ED8778E38}" Directory_="APPDIR" Attributes="0" KeyPath="CameraVideo.exe"/>
    <ROW Component="D3Dcompiler_47.dll" ComponentId="{A1B64B52-D39F-4DB0-AE6A-F138C6495FFF}" Directory_="APPDIR" Attributes="0" KeyPath="D3Dcompiler_47.dll"/>
    <ROW Component="ProductInformation" ComponentId="{7CE97CA6-6046-4D0D-81C1-C875C99EB874}" Directory_="APPDIR" Attributes="4" KeyPath="Version"/>
    <ROW Component="Qt5Core.dll" ComponentId="{23E70BE5-6A9B-4D5A-A272-27F87A775C9E}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Core.dll"/>
    <ROW Component="Qt5Gui.dll" ComponentId="{68938B2F-4AB1-4180-B98A-2C3793B6CE01}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Gui.dll"/>
    <ROW Component="Qt5Multimedia.dll" ComponentId="{800F2441-C4A1-4DC2-BCFA-BBB89F5D57B3}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Multimedia.dll"/>
    <ROW Component="Qt5Network.dll" ComponentId="{6CDCD786-FBB2-488B-BD49-B1F4E5CCC48E}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Network.dll"/>
    <ROW Component="Qt5Sql.dll" ComponentId="{922E5BFC-C4CB-4165-BECF-B10B5DE7C63F}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Sql.dll"/>
    <ROW Component="Qt5Svg.dll" ComponentId="{17848321-5351-47F7-BC9F-D00BAC6FF298}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Svg.dll"/>
    <ROW Component="Qt5Widgets.dll" ComponentId="{616D2120-F645-4779-981C-9CE725D5B854}" Directory_="APPDIR" Attributes="0" KeyPath="Qt5Widgets.dll"/>
    <ROW Component="SHORTCUTDIR" ComponentId="{146DC12A-012C-4FC2-A810-9C5DEAD7B83E}" Directory_="SHORTCUTDIR" Attributes="0"/>
    <ROW Component="Screenshots" ComponentId="{ACA34E1C-F90C-437E-BDB2-F02EB542634B}" Directory_="Screenshots_Dir" Attributes="0"/>
    <ROW Component="Videos" ComponentId="{F32A7340-6B64-4F14-9F66-7B07520D0518}" Directory_="Videos_Dir" Attributes="0"/>
    <ROW Component="avcodec59.dll" ComponentId="{85ACEDFC-C1BA-4DED-B9C6-36C96259D6F5}" Directory_="APPDIR" Attributes="0" KeyPath="avcodec59.dll"/>
    <ROW Component="avdevice59.dll" ComponentId="{E1A98AB2-71D9-4FB2-A7C2-FA03FAE40E5F}" Directory_="APPDIR" Attributes="0" KeyPath="avdevice59.dll"/>
    <ROW Component="avfilter8.dll" ComponentId="{C56FDE30-EF41-45C5-AA7F-C57E97668B0A}" Directory_="APPDIR" Attributes="0" KeyPath="avfilter8.dll"/>
    <ROW Component="avformat59.dll" ComponentId="{710D2847-4094-4D1F-A527-91960AAD450D}" Directory_="APPDIR" Attributes="0" KeyPath="avformat59.dll"/>
    <ROW Component="avutil57.dll" ComponentId="{72636C3A-19BE-4335-83AB-73CE1289B408}" Directory_="APPDIR" Attributes="0" KeyPath="avutil57.dll"/>
    <ROW Component="cameras.db" ComponentId="{72ED2F5C-EEEF-491E-8ECC-F37DB086C15A}" Directory_="APPDIR" Attributes="0" KeyPath="cameras.db" Type="0"/>
    <ROW Component="dsengine.dll" ComponentId="{B748C72A-A21C-45CE-921B-315AC3FB0CFB}" Directory_="mediaservice_Dir" Attributes="0" KeyPath="dsengine.dll"/>
    <ROW Component="ffmpeg.exe" ComponentId="{FDD96E08-7620-4CF6-8082-DE7F2AEC675E}" Directory_="APPDIR" Attributes="0" KeyPath="ffmpeg.exe"/>
    <ROW Component="libEGL.dll" ComponentId="{6A70B224-53D4-4E8D-9AFC-2803A0AA116D}" Directory_="APPDIR" Attributes="0" KeyPath="libEGL.dll"/>
    <ROW Component="libGLESV2.dll" ComponentId="{005E7050-F70A-4D49-A675-5C586697CA1C}" Directory_="APPDIR" Attributes="0" KeyPath="libGLESV2.dll"/>
    <ROW Component="libgcc_s_dw21.dll" ComponentId="{E340158B-8865-4BB6-8161-CE7A196D3C3C}" Directory_="APPDIR" Attributes="0" KeyPath="libgcc_s_dw21.dll"/>
    <ROW Component="libgcc_s_seh1.dll" ComponentId="{4B49FDA2-98DE-4102-8F11-84765B89034B}" Directory_="APPDIR" Attributes="256" KeyPath="libgcc_s_seh1.dll"/>
    <ROW Component="libstdc6.dll" ComponentId="{3F80EECA-29D1-482B-B48F-F84D3597F993}" Directory_="APPDIR" Attributes="0" KeyPath="libstdc6.dll"/>
    <ROW Component="libwinpthread1.dll" ComponentId="{8C8B19D7-4FB3-4315-8E02-8E86B29A2BAE}" Directory_="APPDIR" Attributes="0" KeyPath="libwinpthread1.dll"/>
    <ROW Component="logs" ComponentId="{93AC2A14-56FD-4E16-8FEA-0B498C6BA318}" Directory_="logs_Dir" Attributes="0"/>
    <ROW Component="opengl32sw.dll" ComponentId="{90505841-72A4-47C2-808B-47FCE6A3FB7B}" Directory_="APPDIR" Attributes="0" KeyPath="opengl32sw.dll"/>
    <ROW Component="postproc56.dll" ComponentId="{7B1B595F-0121-4876-9D7C-0DC982C36D59}" Directory_="APPDIR" Attributes="0" KeyPath="postproc56.dll"/>
    <ROW Component="qgenericbearer.dll" ComponentId="{47877D73-00E5-45CE-84B8-C7EC2C360E65}" Directory_="bearer_Dir" Attributes="0" KeyPath="qgenericbearer.dll"/>
    <ROW Component="qgif.dll" ComponentId="{9D142EAA-6904-467C-A8D8-68E9B444CD4C}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qgif.dll"/>
    <ROW Component="qicns.dll" ComponentId="{C32B0979-2E0E-4EBA-A0B5-68DDADFFED73}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qicns.dll"/>
    <ROW Component="qico.dll" ComponentId="{78B19CBD-94A3-4BE8-817E-87ACDCF85190}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qico.dll"/>
    <ROW Component="qjpeg.dll" ComponentId="{9B16E579-8761-4AC8-838B-87D108097F4A}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qjpeg.dll"/>
    <ROW Component="qnativewifibearer.dll" ComponentId="{928EE563-6A83-4330-ADDB-446627EDA1E8}" Directory_="bearer_Dir" Attributes="0" KeyPath="qnativewifibearer.dll"/>
    <ROW Component="qsqlite.dll" ComponentId="{B7AF92EE-A32C-4D5C-8E9B-C55505551C0B}" Directory_="sqldrivers_Dir" Attributes="0" KeyPath="qsqlite.dll"/>
    <ROW Component="qsqlmysql.dll" ComponentId="{FF92E3D4-7896-494C-9D13-7BDAD67C3387}" Directory_="sqldrivers_Dir" Attributes="0" KeyPath="qsqlmysql.dll"/>
    <ROW Component="qsqlodbc.dll" ComponentId="{D1DBF892-9470-43C8-A404-1F577D174230}" Directory_="sqldrivers_Dir" Attributes="0" KeyPath="qsqlodbc.dll"/>
    <ROW Component="qsqlpsql.dll" ComponentId="{95CFA164-8063-4CBD-9464-C43770639FDE}" Directory_="sqldrivers_Dir" Attributes="0" KeyPath="qsqlpsql.dll"/>
    <ROW Component="qsvg.dll" ComponentId="{C7FECA64-D91E-4A82-94E6-5214743D954D}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qsvg.dll"/>
    <ROW Component="qsvgicon.dll" ComponentId="{95A1F686-6E18-4582-AA10-B8F804A94B14}" Directory_="iconengines_Dir" Attributes="0" KeyPath="qsvgicon.dll"/>
    <ROW Component="qt_bg.qm" ComponentId="{E9B8110E-D78B-4047-8B3E-E0BF3A16105E}" Directory_="translations_Dir" Attributes="0" KeyPath="qt_bg.qm" Type="0"/>
    <ROW Component="qtaudio_windows.dll" ComponentId="{4B551351-9395-477B-83BA-FCC2823B2005}" Directory_="audio_Dir" Attributes="0" KeyPath="qtaudio_windows.dll"/>
    <ROW Component="qtga.dll" ComponentId="{B731CB52-9E8C-417F-88C4-B32E51FB3513}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qtga.dll"/>
    <ROW Component="qtiff.dll" ComponentId="{B2F3FFA0-E6B7-44D7-8E2A-B03B9E2CB2C6}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qtiff.dll"/>
    <ROW Component="qtmedia_audioengine.dll" ComponentId="{99072AC7-2463-4F3D-97AD-01E7515964EC}" Directory_="mediaservice_Dir" Attributes="0" KeyPath="qtmedia_audioengine.dll"/>
    <ROW Component="qtmultimedia_m3u.dll" ComponentId="{240DDE82-E7D2-47F2-9557-BBFBB79D67CB}" Directory_="playlistformats_Dir" Attributes="0" KeyPath="qtmultimedia_m3u.dll"/>
    <ROW Component="qwbmp.dll" ComponentId="{0ECA9C28-EBAE-4014-9AFC-55562E065487}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qwbmp.dll"/>
    <ROW Component="qwebp.dll" ComponentId="{4F3777CE-6D1C-4F54-A93E-F69D700D0D94}" Directory_="imageformats_Dir" Attributes="0" KeyPath="qwebp.dll"/>
    <ROW Component="qwindows.dll" ComponentId="{24DD3EE1-477F-4D8F-A33B-5C54DE22C486}" Directory_="platforms_Dir" Attributes="0" KeyPath="qwindows.dll"/>
    <ROW Component="secretU.dll" ComponentId="{5B246904-FCE2-4EED-9AF1-FFA715309F67}" Directory_="APPDIR" Attributes="0" KeyPath="secretU.dll"/>
    <ROW Component="sqlite3.dll" ComponentId="{476AC588-3E64-415F-9AA3-4C4CB10F83A4}" Directory_="APPDIR" Attributes="0" KeyPath="sqlite3.dll"/>
    <ROW Component="swresample4.dll" ComponentId="{2FAC406C-14BE-460A-AB00-4B446235E3A9}" Directory_="APPDIR" Attributes="0" KeyPath="swresample4.dll"/>
    <ROW Component="swscale6.dll" ComponentId="{********-505B-4647-8FC5-6B96264068BF}" Directory_="APPDIR" Attributes="0" KeyPath="swscale6.dll"/>
    <ROW Component="zf_KeyCheck.dll" ComponentId="{7B24DE94-2F85-46D6-83D5-C53BFEFF7BC3}" Directory_="APPDIR" Attributes="0" KeyPath="zf_KeyCheck.dll"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFeatsComponent">
    <ROW Feature="MainFeature" Title="MainFeature" Description="Description" Display="1" Level="1" Directory_="APPDIR" Attributes="0"/>
    <ATTRIBUTE name="CurrentFeature" value="MainFeature"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFilesComponent">
    <ROW File="AuthCheck.dll" Component_="AuthCheck.dll" FileName="AUTHCH~1.DLL|AuthCheck.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\AuthCheck.dll" SelfReg="false"/>
    <ROW File="avcodec59.dll" Component_="avcodec59.dll" FileName="AVCODE~1.DLL|avcodec-59.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\avcodec-59.dll" SelfReg="false"/>
    <ROW File="avdevice59.dll" Component_="avdevice59.dll" FileName="AVDEVI~1.DLL|avdevice-59.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\avdevice-59.dll" SelfReg="false"/>
    <ROW File="avfilter8.dll" Component_="avfilter8.dll" FileName="AVFILT~1.DLL|avfilter-8.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\avfilter-8.dll" SelfReg="false"/>
    <ROW File="avformat59.dll" Component_="avformat59.dll" FileName="AVFORM~1.DLL|avformat-59.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\avformat-59.dll" SelfReg="false"/>
    <ROW File="avutil57.dll" Component_="avutil57.dll" FileName="AVUTIL~1.DLL|avutil-57.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\avutil-57.dll" SelfReg="false"/>
    <ROW File="cameras.db" Component_="cameras.db" FileName="cameras.db" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\cameras.db" SelfReg="false"/>
    <ROW File="CameraVideo.exe" Component_="CameraVideo.exe" FileName="CAMERA~1.EXE|CameraVideo.exe" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\CameraVideo.exe" SelfReg="false" DigSign="true"/>
    <ROW File="Conf.db" Component_="cameras.db" FileName="Conf.db" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Conf.db" SelfReg="false"/>
    <ROW File="config.ini" Component_="cameras.db" FileName="config.ini" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\config.ini" SelfReg="false"/>
    <ROW File="D3Dcompiler_47.dll" Component_="D3Dcompiler_47.dll" FileName="D3DCOM~1.DLL|D3Dcompiler_47.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\D3Dcompiler_47.dll" SelfReg="false"/>
    <ROW File="ffmpeg.exe" Component_="ffmpeg.exe" FileName="ffmpeg.exe" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\ffmpeg.exe" SelfReg="false" DigSign="true"/>
    <ROW File="libEGL.dll" Component_="libEGL.dll" FileName="libEGL.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libEGL.dll" SelfReg="false"/>
    <ROW File="libgcc_s_dw21.dll" Component_="libgcc_s_dw21.dll" FileName="LIBGCC~1.DLL|libgcc_s_dw2-1.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libgcc_s_dw2-1.dll" SelfReg="false"/>
    <ROW File="libgcc_s_seh1.dll" Component_="libgcc_s_seh1.dll" FileName="LIBGCC~2.DLL|libgcc_s_seh-1.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libgcc_s_seh-1.dll" SelfReg="false"/>
    <ROW File="libGLESV2.dll" Component_="libGLESV2.dll" FileName="LIBGLE~1.DLL|libGLESV2.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libGLESV2.dll" SelfReg="false"/>
    <ROW File="libstdc6.dll" Component_="libstdc6.dll" FileName="LIBSTD~1.DLL|libstdc++-6.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libstdc++-6.dll" SelfReg="false"/>
    <ROW File="libwinpthread1.dll" Component_="libwinpthread1.dll" FileName="LIBWIN~1.DLL|libwinpthread-1.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\libwinpthread-1.dll" SelfReg="false"/>
    <ROW File="opengl32sw.dll" Component_="opengl32sw.dll" FileName="OPENGL~1.DLL|opengl32sw.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\opengl32sw.dll" SelfReg="false"/>
    <ROW File="postproc56.dll" Component_="postproc56.dll" FileName="POSTPR~1.DLL|postproc-56.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\postproc-56.dll" SelfReg="false"/>
    <ROW File="Qt5Core.dll" Component_="Qt5Core.dll" FileName="Qt5Core.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Core.dll" SelfReg="false"/>
    <ROW File="Qt5Gui.dll" Component_="Qt5Gui.dll" FileName="Qt5Gui.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Gui.dll" SelfReg="false"/>
    <ROW File="Qt5Multimedia.dll" Component_="Qt5Multimedia.dll" FileName="QT5MUL~1.DLL|Qt5Multimedia.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Multimedia.dll" SelfReg="false"/>
    <ROW File="Qt5Network.dll" Component_="Qt5Network.dll" FileName="QT5NET~1.DLL|Qt5Network.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Network.dll" SelfReg="false"/>
    <ROW File="Qt5Sql.dll" Component_="Qt5Sql.dll" FileName="Qt5Sql.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Sql.dll" SelfReg="false"/>
    <ROW File="Qt5Svg.dll" Component_="Qt5Svg.dll" FileName="Qt5Svg.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Svg.dll" SelfReg="false"/>
    <ROW File="Qt5Widgets.dll" Component_="Qt5Widgets.dll" FileName="QT5WID~1.DLL|Qt5Widgets.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\Qt5Widgets.dll" SelfReg="false"/>
    <ROW File="secretU.dll" Component_="secretU.dll" FileName="secretU.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\secretU.dll" SelfReg="false"/>
    <ROW File="sqlite3.dll" Component_="sqlite3.dll" FileName="sqlite3.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\sqlite3.dll" SelfReg="false"/>
    <ROW File="swresample4.dll" Component_="swresample4.dll" FileName="SWRESA~1.DLL|swresample-4.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\swresample-4.dll" SelfReg="false"/>
    <ROW File="swscale6.dll" Component_="swscale6.dll" FileName="SWSCAL~1.DLL|swscale-6.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\swscale-6.dll" SelfReg="false"/>
    <ROW File="zf_KeyCheck.dll" Component_="zf_KeyCheck.dll" FileName="ZF_KEY~1.DLL|zf_KeyCheck.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\zf_KeyCheck.dll" SelfReg="false"/>
    <ROW File="qtaudio_windows.dll" Component_="qtaudio_windows.dll" FileName="QTAUDI~1.DLL|qtaudio_windows.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\audio\qtaudio_windows.dll" SelfReg="false"/>
    <ROW File="qgenericbearer.dll" Component_="qgenericbearer.dll" FileName="QGENER~1.DLL|qgenericbearer.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\bearer\qgenericbearer.dll" SelfReg="false"/>
    <ROW File="qnativewifibearer.dll" Component_="qnativewifibearer.dll" FileName="QNATIV~1.DLL|qnativewifibearer.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\bearer\qnativewifibearer.dll" SelfReg="false"/>
    <ROW File="qsvgicon.dll" Component_="qsvgicon.dll" FileName="qsvgicon.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\iconengines\qsvgicon.dll" SelfReg="false"/>
    <ROW File="qgif.dll" Component_="qgif.dll" FileName="qgif.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qgif.dll" SelfReg="false"/>
    <ROW File="qicns.dll" Component_="qicns.dll" FileName="qicns.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qicns.dll" SelfReg="false"/>
    <ROW File="qico.dll" Component_="qico.dll" FileName="qico.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qico.dll" SelfReg="false"/>
    <ROW File="qjpeg.dll" Component_="qjpeg.dll" FileName="qjpeg.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qjpeg.dll" SelfReg="false"/>
    <ROW File="qsvg.dll" Component_="qsvg.dll" FileName="qsvg.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qsvg.dll" SelfReg="false"/>
    <ROW File="qtga.dll" Component_="qtga.dll" FileName="qtga.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qtga.dll" SelfReg="false"/>
    <ROW File="qtiff.dll" Component_="qtiff.dll" FileName="qtiff.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qtiff.dll" SelfReg="false"/>
    <ROW File="qwbmp.dll" Component_="qwbmp.dll" FileName="qwbmp.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qwbmp.dll" SelfReg="false"/>
    <ROW File="qwebp.dll" Component_="qwebp.dll" FileName="qwebp.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\imageformats\qwebp.dll" SelfReg="false"/>
    <ROW File="dsengine.dll" Component_="dsengine.dll" FileName="dsengine.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\mediaservice\dsengine.dll" SelfReg="false"/>
    <ROW File="qtmedia_audioengine.dll" Component_="qtmedia_audioengine.dll" FileName="QTMEDI~1.DLL|qtmedia_audioengine.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\mediaservice\qtmedia_audioengine.dll" SelfReg="false"/>
    <ROW File="qwindows.dll" Component_="qwindows.dll" FileName="qwindows.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\platforms\qwindows.dll" SelfReg="false"/>
    <ROW File="qtmultimedia_m3u.dll" Component_="qtmultimedia_m3u.dll" FileName="QTMULT~1.DLL|qtmultimedia_m3u.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\playlistformats\qtmultimedia_m3u.dll" SelfReg="false"/>
    <ROW File="qsqlite.dll" Component_="qsqlite.dll" FileName="qsqlite.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\sqldrivers\qsqlite.dll" SelfReg="false"/>
    <ROW File="qsqlmysql.dll" Component_="qsqlmysql.dll" FileName="QSQLMY~1.DLL|qsqlmysql.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\sqldrivers\qsqlmysql.dll" SelfReg="false"/>
    <ROW File="qsqlodbc.dll" Component_="qsqlodbc.dll" FileName="qsqlodbc.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\sqldrivers\qsqlodbc.dll" SelfReg="false"/>
    <ROW File="qsqlpsql.dll" Component_="qsqlpsql.dll" FileName="qsqlpsql.dll" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\sqldrivers\qsqlpsql.dll" SelfReg="false"/>
    <ROW File="qt_bg.qm" Component_="qt_bg.qm" FileName="qt_bg.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_bg.qm" SelfReg="false"/>
    <ROW File="qt_ca.qm" Component_="qt_bg.qm" FileName="qt_ca.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_ca.qm" SelfReg="false"/>
    <ROW File="qt_cs.qm" Component_="qt_bg.qm" FileName="qt_cs.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_cs.qm" SelfReg="false"/>
    <ROW File="qt_da.qm" Component_="qt_bg.qm" FileName="qt_da.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_da.qm" SelfReg="false"/>
    <ROW File="qt_de.qm" Component_="qt_bg.qm" FileName="qt_de.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_de.qm" SelfReg="false"/>
    <ROW File="qt_en.qm" Component_="qt_bg.qm" FileName="qt_en.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_en.qm" SelfReg="false"/>
    <ROW File="qt_es.qm" Component_="qt_bg.qm" FileName="qt_es.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_es.qm" SelfReg="false"/>
    <ROW File="qt_fi.qm" Component_="qt_bg.qm" FileName="qt_fi.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_fi.qm" SelfReg="false"/>
    <ROW File="qt_fr.qm" Component_="qt_bg.qm" FileName="qt_fr.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_fr.qm" SelfReg="false"/>
    <ROW File="qt_gd.qm" Component_="qt_bg.qm" FileName="qt_gd.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_gd.qm" SelfReg="false"/>
    <ROW File="qt_he.qm" Component_="qt_bg.qm" FileName="qt_he.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_he.qm" SelfReg="false"/>
    <ROW File="qt_hu.qm" Component_="qt_bg.qm" FileName="qt_hu.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_hu.qm" SelfReg="false"/>
    <ROW File="qt_it.qm" Component_="qt_bg.qm" FileName="qt_it.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_it.qm" SelfReg="false"/>
    <ROW File="qt_ja.qm" Component_="qt_bg.qm" FileName="qt_ja.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_ja.qm" SelfReg="false"/>
    <ROW File="qt_ko.qm" Component_="qt_bg.qm" FileName="qt_ko.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_ko.qm" SelfReg="false"/>
    <ROW File="qt_lv.qm" Component_="qt_bg.qm" FileName="qt_lv.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_lv.qm" SelfReg="false"/>
    <ROW File="qt_pl.qm" Component_="qt_bg.qm" FileName="qt_pl.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_pl.qm" SelfReg="false"/>
    <ROW File="qt_ru.qm" Component_="qt_bg.qm" FileName="qt_ru.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_ru.qm" SelfReg="false"/>
    <ROW File="qt_sk.qm" Component_="qt_bg.qm" FileName="qt_sk.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_sk.qm" SelfReg="false"/>
    <ROW File="qt_uk.qm" Component_="qt_bg.qm" FileName="qt_uk.qm" Attributes="0" SourcePath="build\build-NetVideoClient-Desktop_Qt_5_9_3_MinGW_32bit-Release\release\translations\qt_uk.qm" SelfReg="false"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.BootstrOptComponent">
    <ROW BootstrOptKey="GlobalOptions" DownloadFolder="[AppDataFolder][|Manufacturer]\[|ProductName]\prerequisites" Options="2"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.BuildComponent">
    <ROW BuildKey="DefaultBuild" BuildName="DefaultBuild" BuildOrder="1" BuildType="0" Languages="zh" InstallationType="4" CabsLocation="1" PackageType="1" FilesInsideExe="true" ExtractionFolder="[AppDataFolder][|Manufacturer]\[|ProductName] [|ProductVersion]\install" ExtUI="true" UseLargeSchema="true"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.DictionaryComponent">
    <ROW Path="&lt;AI_DICTS&gt;ui.ail"/>
    <ROW Path="&lt;AI_DICTS&gt;ui_zh.ail"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.FragmentComponent">
    <ROW Fragment="CommonUI.aip" Path="&lt;AI_FRAGS&gt;CommonUI.aip"/>
    <ROW Fragment="FolderDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\FolderDlg.aip"/>
    <ROW Fragment="MaintenanceTypeDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\MaintenanceTypeDlg.aip"/>
    <ROW Fragment="MaintenanceWelcomeDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\MaintenanceWelcomeDlg.aip"/>
    <ROW Fragment="SequenceDialogs.aip" Path="&lt;AI_THEMES&gt;classic\fragments\SequenceDialogs.aip"/>
    <ROW Fragment="Sequences.aip" Path="&lt;AI_FRAGS&gt;Sequences.aip"/>
    <ROW Fragment="StaticUIStrings.aip" Path="&lt;AI_FRAGS&gt;StaticUIStrings.aip"/>
    <ROW Fragment="Themes.aip" Path="&lt;AI_FRAGS&gt;Themes.aip"/>
    <ROW Fragment="UI.aip" Path="&lt;AI_THEMES&gt;classic\fragments\UI.aip"/>
    <ROW Fragment="Validation.aip" Path="&lt;AI_FRAGS&gt;Validation.aip"/>
    <ROW Fragment="VerifyRemoveDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\VerifyRemoveDlg.aip"/>
    <ROW Fragment="VerifyRepairDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\VerifyRepairDlg.aip"/>
    <ROW Fragment="WelcomeDlg.aip" Path="&lt;AI_THEMES&gt;classic\fragments\WelcomeDlg.aip"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiActionTextComponent">
    <ROW Action="AI_DeleteLzma" Description="正在删除从档案中提取的文件" DescriptionLocId="ActionText.Description.AI_DeleteLzma" TemplateLocId="-"/>
    <ROW Action="AI_DeleteRLzma" Description="正在删除从档案中提取的文件" DescriptionLocId="ActionText.Description.AI_DeleteLzma" TemplateLocId="-"/>
    <ROW Action="AI_ExtractFiles" Description="正在从存档中提取文件" DescriptionLocId="ActionText.Description.AI_ExtractLzma" TemplateLocId="-"/>
    <ROW Action="AI_ExtractLzma" Description="正在从存档中提取文件" DescriptionLocId="ActionText.Description.AI_ExtractLzma" TemplateLocId="-"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiAppSearchComponent">
    <ROW Property="AI_SETUPEXEPATH" Signature_="AI_EXE_PATH_LM" Builds="DefaultBuild"/>
    <ROW Property="AI_SETUPEXEPATH" Signature_="AI_EXE_PATH_CU" Builds="DefaultBuild"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiBinaryComponent">
    <ROW Name="ExternalUICleaner.dll" SourcePath="&lt;AI_CUSTACTS&gt;ExternalUICleaner.dll"/>
    <ROW Name="Prereq.dll" SourcePath="&lt;AI_CUSTACTS&gt;Prereq.dll"/>
    <ROW Name="aicustact.dll" SourcePath="&lt;AI_CUSTACTS&gt;aicustact.dll"/>
    <ROW Name="lzmaextractor.dll" SourcePath="&lt;AI_CUSTACTS&gt;lzmaextractor.dll"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiControlEventComponent">
    <ROW Dialog_="WelcomeDlg" Control_="Next" Event="NewDialog" Argument="FolderDlg" Condition="AI_INSTALL" Ordering="1"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_INSTALL" Ordering="197"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Back" Event="NewDialog" Argument="FolderDlg" Condition="AI_INSTALL" Ordering="201"/>
    <ROW Dialog_="FolderDlg" Control_="Next" Event="NewDialog" Argument="VerifyReadyDlg" Condition="AI_INSTALL" Ordering="201"/>
    <ROW Dialog_="FolderDlg" Control_="Back" Event="NewDialog" Argument="WelcomeDlg" Condition="AI_INSTALL" Ordering="1"/>
    <ROW Dialog_="MaintenanceWelcomeDlg" Control_="Next" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT" Ordering="99"/>
    <ROW Dialog_="CustomizeDlg" Control_="Next" Event="NewDialog" Argument="VerifyReadyDlg" Condition="AI_MAINT" Ordering="101"/>
    <ROW Dialog_="CustomizeDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT" Ordering="1"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_MAINT" Ordering="198"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Back" Event="NewDialog" Argument="CustomizeDlg" Condition="AI_MAINT" Ordering="202"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="ChangeButton" Event="NewDialog" Argument="CustomizeDlg" Condition="AI_MAINT" Ordering="501"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceWelcomeDlg" Condition="AI_MAINT" Ordering="1"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="RemoveButton" Event="NewDialog" Argument="VerifyRemoveDlg" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="601"/>
    <ROW Dialog_="VerifyRemoveDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="1"/>
    <ROW Dialog_="MaintenanceTypeDlg" Control_="RepairButton" Event="NewDialog" Argument="VerifyRepairDlg" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="601"/>
    <ROW Dialog_="VerifyRepairDlg" Control_="Back" Event="NewDialog" Argument="MaintenanceTypeDlg" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="1"/>
    <ROW Dialog_="VerifyRepairDlg" Control_="Repair" Event="EndDialog" Argument="Return" Condition="AI_MAINT AND InstallMode=&quot;Repair&quot;" Ordering="399" Options="1"/>
    <ROW Dialog_="VerifyRemoveDlg" Control_="Remove" Event="EndDialog" Argument="Return" Condition="AI_MAINT AND InstallMode=&quot;Remove&quot;" Ordering="299" Options="1"/>
    <ROW Dialog_="PatchWelcomeDlg" Control_="Next" Event="NewDialog" Argument="VerifyReadyDlg" Condition="AI_PATCH" Ordering="201"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_PATCH" Ordering="199"/>
    <ROW Dialog_="VerifyReadyDlg" Control_="Back" Event="NewDialog" Argument="PatchWelcomeDlg" Condition="AI_PATCH" Ordering="203"/>
    <ROW Dialog_="ResumeDlg" Control_="Install" Event="EndDialog" Argument="Return" Condition="AI_RESUME" Ordering="299"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCreateFolderComponent">
    <ROW Directory_="APPDIR" Component_="APPDIR" ManualDelete="true"/>
    <ROW Directory_="logs_Dir" Component_="logs" ManualDelete="false"/>
    <ROW Directory_="Screenshots_Dir" Component_="Screenshots" ManualDelete="false"/>
    <ROW Directory_="Videos_Dir" Component_="Videos" ManualDelete="false"/>
    <ROW Directory_="SHORTCUTDIR" Component_="SHORTCUTDIR" ManualDelete="false"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiCustActComponent">
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Type="51" Source="AI_SETUPEXEPATH_ORIGINAL" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_DATA_SETTER" Type="51" Source="CustomActionData" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_DOWNGRADE" Type="19" Target="4010"/>
    <ROW Action="AI_DeleteCadLzma" Type="51" Source="AI_DeleteLzma" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_DeleteLzma" Type="1025" Source="lzmaextractor.dll" Target="DeleteLZMAFiles"/>
    <ROW Action="AI_DeleteRCadLzma" Type="51" Source="AI_DeleteRLzma" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_DeleteRLzma" Type="1281" Source="lzmaextractor.dll" Target="DeleteLZMAFiles"/>
    <ROW Action="AI_DoRemoveExternalUIStub" Type="3585" Source="ExternalUICleaner.dll" Target="DoRemoveExternalUIStub" WithoutSeq="true"/>
    <ROW Action="AI_DpiContentScale" Type="1" Source="aicustact.dll" Target="DpiContentScale"/>
    <ROW Action="AI_EnableDebugLog" Type="321" Source="aicustact.dll" Target="EnableDebugLog"/>
    <ROW Action="AI_ExtractCadLzma" Type="51" Source="AI_ExtractLzma" Target="[AI_SETUPEXEPATH]"/>
    <ROW Action="AI_ExtractFiles" Type="1" Source="Prereq.dll" Target="ExtractSourceFiles" AdditionalSeq="AI_DATA_SETTER"/>
    <ROW Action="AI_ExtractLzma" Type="1025" Source="lzmaextractor.dll" Target="ExtractLZMAFiles"/>
    <ROW Action="AI_FindExeLzma" Type="1" Source="lzmaextractor.dll" Target="FindEXE"/>
    <ROW Action="AI_GetArpIconPath" Type="1" Source="aicustact.dll" Target="GetArpIconPath"/>
    <ROW Action="AI_InstallModeCheck" Type="1" Source="aicustact.dll" Target="UpdateInstallMode" WithoutSeq="true"/>
    <ROW Action="AI_PREPARE_UPGRADE" Type="65" Source="aicustact.dll" Target="PrepareUpgrade"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Type="51" Source="AI_SETUPEXEPATH" Target="[AI_SETUPEXEPATH_ORIGINAL]"/>
    <ROW Action="AI_RESTORE_LOCATION" Type="65" Source="aicustact.dll" Target="RestoreLocation"/>
    <ROW Action="AI_RemoveExternalUIStub" Type="1" Source="ExternalUICleaner.dll" Target="RemoveExternalUIStub"/>
    <ROW Action="AI_ResolveKnownFolders" Type="1" Source="aicustact.dll" Target="AI_ResolveKnownFolders"/>
    <ROW Action="AI_SHOW_LOG" Type="65" Source="aicustact.dll" Target="LaunchLogFile" WithoutSeq="true"/>
    <ROW Action="AI_STORE_LOCATION" Type="51" Source="ARPINSTALLLOCATION" Target="[APPDIR]"/>
    <ROW Action="SET_APPDIR" Type="307" Source="APPDIR" Target="[ProgramFilesFolder][Manufacturer]\[ProductName]" MultiBuildTarget="DefaultBuild:[WindowsVolume][Manufacturer]\[ProductName]"/>
    <ROW Action="SET_SHORTCUTDIR" Type="307" Source="SHORTCUTDIR" Target="[ProgramMenuFolder][ProductName]"/>
    <ROW Action="SET_TARGETDIR_TO_APPDIR" Type="51" Source="TARGETDIR" Target="[APPDIR]"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiFeatCompsComponent">
    <ROW Feature_="MainFeature" Component_="APPDIR"/>
    <ROW Feature_="MainFeature" Component_="ProductInformation"/>
    <ROW Feature_="MainFeature" Component_="AuthCheck.dll"/>
    <ROW Feature_="MainFeature" Component_="avcodec59.dll"/>
    <ROW Feature_="MainFeature" Component_="avdevice59.dll"/>
    <ROW Feature_="MainFeature" Component_="avfilter8.dll"/>
    <ROW Feature_="MainFeature" Component_="avformat59.dll"/>
    <ROW Feature_="MainFeature" Component_="avutil57.dll"/>
    <ROW Feature_="MainFeature" Component_="cameras.db"/>
    <ROW Feature_="MainFeature" Component_="CameraVideo.exe"/>
    <ROW Feature_="MainFeature" Component_="D3Dcompiler_47.dll"/>
    <ROW Feature_="MainFeature" Component_="ffmpeg.exe"/>
    <ROW Feature_="MainFeature" Component_="libEGL.dll"/>
    <ROW Feature_="MainFeature" Component_="libgcc_s_dw21.dll"/>
    <ROW Feature_="MainFeature" Component_="libgcc_s_seh1.dll"/>
    <ROW Feature_="MainFeature" Component_="libGLESV2.dll"/>
    <ROW Feature_="MainFeature" Component_="libstdc6.dll"/>
    <ROW Feature_="MainFeature" Component_="libwinpthread1.dll"/>
    <ROW Feature_="MainFeature" Component_="opengl32sw.dll"/>
    <ROW Feature_="MainFeature" Component_="postproc56.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Core.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Gui.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Multimedia.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Network.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Sql.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Svg.dll"/>
    <ROW Feature_="MainFeature" Component_="Qt5Widgets.dll"/>
    <ROW Feature_="MainFeature" Component_="secretU.dll"/>
    <ROW Feature_="MainFeature" Component_="sqlite3.dll"/>
    <ROW Feature_="MainFeature" Component_="swresample4.dll"/>
    <ROW Feature_="MainFeature" Component_="swscale6.dll"/>
    <ROW Feature_="MainFeature" Component_="zf_KeyCheck.dll"/>
    <ROW Feature_="MainFeature" Component_="qtaudio_windows.dll"/>
    <ROW Feature_="MainFeature" Component_="qgenericbearer.dll"/>
    <ROW Feature_="MainFeature" Component_="qnativewifibearer.dll"/>
    <ROW Feature_="MainFeature" Component_="qsvgicon.dll"/>
    <ROW Feature_="MainFeature" Component_="qgif.dll"/>
    <ROW Feature_="MainFeature" Component_="qicns.dll"/>
    <ROW Feature_="MainFeature" Component_="qico.dll"/>
    <ROW Feature_="MainFeature" Component_="qjpeg.dll"/>
    <ROW Feature_="MainFeature" Component_="qsvg.dll"/>
    <ROW Feature_="MainFeature" Component_="qtga.dll"/>
    <ROW Feature_="MainFeature" Component_="qtiff.dll"/>
    <ROW Feature_="MainFeature" Component_="qwbmp.dll"/>
    <ROW Feature_="MainFeature" Component_="qwebp.dll"/>
    <ROW Feature_="MainFeature" Component_="logs"/>
    <ROW Feature_="MainFeature" Component_="dsengine.dll"/>
    <ROW Feature_="MainFeature" Component_="qtmedia_audioengine.dll"/>
    <ROW Feature_="MainFeature" Component_="AI_ExePath"/>
    <ROW Feature_="MainFeature" Component_="AI_CustomARPName"/>
    <ROW Feature_="MainFeature" Component_="qwindows.dll"/>
    <ROW Feature_="MainFeature" Component_="qtmultimedia_m3u.dll"/>
    <ROW Feature_="MainFeature" Component_="Screenshots"/>
    <ROW Feature_="MainFeature" Component_="Videos"/>
    <ROW Feature_="MainFeature" Component_="qsqlite.dll"/>
    <ROW Feature_="MainFeature" Component_="qsqlmysql.dll"/>
    <ROW Feature_="MainFeature" Component_="qsqlodbc.dll"/>
    <ROW Feature_="MainFeature" Component_="qsqlpsql.dll"/>
    <ROW Feature_="MainFeature" Component_="qt_bg.qm"/>
    <ROW Feature_="MainFeature" Component_="SHORTCUTDIR"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiIconsComponent">
    <ROW Name="app.exe" SourcePath="images\app.ico" Index="0"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiInstExSeqComponent">
    <ROW Action="AI_DOWNGRADE" Condition="AI_NEWERPRODUCTFOUND AND (UILevel &lt;&gt; 5)" Sequence="210"/>
    <ROW Action="AI_RESTORE_LOCATION" Condition="APPDIR=&quot;&quot;" Sequence="749"/>
    <ROW Action="AI_STORE_LOCATION" Condition="(Not Installed) OR REINSTALL" Sequence="1502"/>
    <ROW Action="AI_PREPARE_UPGRADE" Condition="AI_UPGRADE=&quot;No&quot; AND (Not Installed)" Sequence="1397"/>
    <ROW Action="AI_ResolveKnownFolders" Sequence="52"/>
    <ROW Action="AI_EnableDebugLog" Sequence="51"/>
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Sequence="99" Builds="DefaultBuild"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Condition="AI_SETUPEXEPATH_ORIGINAL" Sequence="101" Builds="DefaultBuild"/>
    <ROW Action="AI_DeleteCadLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="199" Builds="DefaultBuild"/>
    <ROW Action="AI_DeleteRCadLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="198" Builds="DefaultBuild"/>
    <ROW Action="AI_ExtractCadLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="197" Builds="DefaultBuild"/>
    <ROW Action="AI_FindExeLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="196" Builds="DefaultBuild"/>
    <ROW Action="AI_ExtractLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="1549" Builds="DefaultBuild"/>
    <ROW Action="AI_DeleteRLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="1548" Builds="DefaultBuild"/>
    <ROW Action="AI_DeleteLzma" Condition="SETUPEXEDIR=&quot;&quot; AND Installed AND (REMOVE&lt;&gt;&quot;ALL&quot;) AND (AI_INSTALL_MODE&lt;&gt;&quot;Remove&quot;) AND (NOT PATCH)" Sequence="6599" Builds="DefaultBuild"/>
    <ROW Action="AI_ExtractFiles" Sequence="1399" Builds="DefaultBuild"/>
    <ROW Action="AI_DATA_SETTER" Sequence="1398"/>
    <ROW Action="AI_GetArpIconPath" Sequence="1401"/>
    <ROW Action="AI_RemoveExternalUIStub" Condition="(REMOVE=&quot;ALL&quot;) AND ((VersionNT &gt; 500) OR((VersionNT = 500) AND (ServicePackLevel &gt;= 4)))" Sequence="1501"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiInstallUISequenceComponent">
    <ROW Action="AI_RESTORE_LOCATION" Condition="APPDIR=&quot;&quot;" Sequence="749"/>
    <ROW Action="AI_ResolveKnownFolders" Sequence="53"/>
    <ROW Action="AI_DpiContentScale" Sequence="52"/>
    <ROW Action="AI_EnableDebugLog" Sequence="51"/>
    <ROW Action="AI_BACKUP_AI_SETUPEXEPATH" Sequence="99"/>
    <ROW Action="AI_RESTORE_AI_SETUPEXEPATH" Condition="AI_SETUPEXEPATH_ORIGINAL" Sequence="101"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiLaunchConditionsComponent">
    <ROW Condition="((VersionNT &lt;&gt; 501) AND (VersionNT &lt;&gt; 502))" Description="[ProductName] 无法安装在 [WindowsTypeNT5XDisplay] ." DescriptionLocId="AI.LaunchCondition.NoNT5X" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="(VersionNT &lt;&gt; 400)" Description="[ProductName] 无法安装在 [WindowsTypeNT40Display] ." DescriptionLocId="AI.LaunchCondition.NoNT40" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="(VersionNT &lt;&gt; 500)" Description="[ProductName] 无法安装在 [WindowsTypeNT50Display] ." DescriptionLocId="AI.LaunchCondition.NoNT50" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="SETUPEXEDIR OR (REMOVE=&quot;ALL&quot;)" Description="此包只能从引导器运行。" DescriptionLocId="AI.LaunchCondition.RequireBootstrapper" IsPredefined="true" Builds="DefaultBuild"/>
    <ROW Condition="VersionNT" Description="[ProductName] 无法安装在 [WindowsType9XDisplay] ." DescriptionLocId="AI.LaunchCondition.No9X" IsPredefined="true" Builds="DefaultBuild"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiRegLocatorComponent">
    <ROW Signature_="AI_EXE_PATH_CU" Root="1" Key="Software\Caphyon\Advanced Installer\LZMA\[ProductCode]\[ProductVersion]" Name="AI_ExePath" Type="2"/>
    <ROW Signature_="AI_EXE_PATH_LM" Root="2" Key="Software\Caphyon\Advanced Installer\LZMA\[ProductCode]\[ProductVersion]" Name="AI_ExePath" Type="2"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiRegsComponent">
    <ROW Registry="AI_ExePath" Root="-1" Key="Software\Caphyon\Advanced Installer\LZMA\[ProductCode]\[ProductVersion]" Name="AI_ExePath" Value="[AI_SETUPEXEPATH]" Component_="AI_ExePath"/>
    <ROW Registry="AdvancedInstaller" Root="-1" Key="Software\Caphyon\Advanced Installer" Name="\"/>
    <ROW Registry="Caphyon" Root="-1" Key="Software\Caphyon" Name="\"/>
    <ROW Registry="Comments" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="Comments" Value="[ARPCOMMENTS]" Component_="AI_CustomARPName"/>
    <ROW Registry="Contact" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="Contact" Value="[ARPCONTACT]" Component_="AI_CustomARPName"/>
    <ROW Registry="CurrentVersion" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion" Name="\"/>
    <ROW Registry="DisplayIcon" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="DisplayIcon" Value="[ARP_ICON_PATH]" Component_="AI_CustomARPName"/>
    <ROW Registry="DisplayName" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="DisplayName" Value="[AI_PRODUCTNAME_ARP]" Component_="AI_CustomARPName"/>
    <ROW Registry="DisplayVersion" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="DisplayVersion" Value="[ProductVersion]" Component_="AI_CustomARPName"/>
    <ROW Registry="EstimatedSize" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="EstimatedSize" Value="#[AI_ARP_SIZE]" Component_="AI_CustomARPName" VirtualValue="#"/>
    <ROW Registry="HelpLink" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="HelpLink" Value="[ARPHELPLINK]" Component_="AI_CustomARPName"/>
    <ROW Registry="HelpTelephone" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="HelpTelephone" Value="[ARPHELPTELEPHONE]" Component_="AI_CustomARPName"/>
    <ROW Registry="InstallLocation" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="InstallLocation" Value="[APPDIR]" Component_="AI_CustomARPName"/>
    <ROW Registry="LZMA" Root="-1" Key="Software\Caphyon\Advanced Installer\LZMA" Name="\"/>
    <ROW Registry="Manufacturer" Root="-1" Key="Software\[Manufacturer]" Name="\"/>
    <ROW Registry="Microsoft" Root="-1" Key="Software\Microsoft" Name="\"/>
    <ROW Registry="ModifyPath" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="ModifyPath" Value="[AI_UNINSTALLER] /i [ProductCode] AI_UNINSTALLER_CTP=1" Component_="AI_CustomARPName"/>
    <ROW Registry="NoRepair" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="NoRepair" Value="#1" Component_="AI_CustomARPName" VirtualValue="#"/>
    <ROW Registry="Path" Root="-1" Key="Software\[Manufacturer]\[ProductName]" Name="Path" Value="[APPDIR]" Component_="ProductInformation"/>
    <ROW Registry="ProductCode" Root="-1" Key="Software\Caphyon\Advanced Installer\LZMA\[ProductCode]" Name="\"/>
    <ROW Registry="ProductName" Root="-1" Key="Software\[Manufacturer]\[ProductName]" Name="\"/>
    <ROW Registry="ProductNameProductVersion" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="\"/>
    <ROW Registry="ProductVersion" Root="-1" Key="Software\Caphyon\Advanced Installer\LZMA\[ProductCode]\[ProductVersion]" Name="\"/>
    <ROW Registry="Publisher" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="Publisher" Value="[Manufacturer]" Component_="AI_CustomARPName"/>
    <ROW Registry="Readme" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="Readme" Value="[ARPREADME]" Component_="AI_CustomARPName"/>
    <ROW Registry="Software" Root="-1" Key="Software" Name="\"/>
    <ROW Registry="URLInfoAbout" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="URLInfoAbout" Value="[ARPURLINFOABOUT]" Component_="AI_CustomARPName"/>
    <ROW Registry="URLUpdateInfo" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="URLUpdateInfo" Value="[ARPURLUPDATEINFO]" Component_="AI_CustomARPName"/>
    <ROW Registry="Uninstall" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall" Name="\"/>
    <ROW Registry="UninstallPath" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="UninstallPath" Value="[AI_UNINSTALLER] /x [ProductCode] AI_UNINSTALLER_CTP=1" Component_="AI_CustomARPName"/>
    <ROW Registry="UninstallString" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="UninstallString" Value="[AI_UNINSTALLER] /x [ProductCode] AI_UNINSTALLER_CTP=1" Component_="AI_CustomARPName"/>
    <ROW Registry="Version" Root="-1" Key="Software\[Manufacturer]\[ProductName]" Name="Version" Value="[ProductVersion]" Component_="ProductInformation"/>
    <ROW Registry="VersionMajor" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="VersionMajor" Value="#1" Component_="AI_CustomARPName" VirtualValue="#"/>
    <ROW Registry="VersionMinor" Root="-1" Key="Software\Microsoft\Windows\CurrentVersion\Uninstall\[ProductName] [ProductVersion]" Name="VersionMinor" Value="#0" Component_="AI_CustomARPName" VirtualValue="#"/>
    <ROW Registry="Windows" Root="-1" Key="Software\Microsoft\Windows" Name="\"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiShortsComponent">
    <ROW Shortcut="CameraVideo" Directory_="SHORTCUTDIR" Name="高清针孔摄像~1|高清针孔摄像头窃视演示" Component_="CameraVideo.exe" Target="[#CameraVideo.exe]" Hotkey="0" IconIndex="0" ShowCmd="1" WkDir="APPDIR"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiThemeComponent">
    <ATTRIBUTE name="UsedTheme" value="classic"/>
  </COMPONENT>
  <COMPONENT cid="caphyon.advinst.msicomp.MsiUpgradeComponent">
    <ROW UpgradeCode="[|UpgradeCode]" VersionMin="0.0.1" VersionMax="[|ProductVersion]" Attributes="257" ActionProperty="OLDPRODUCTS"/>
    <ROW UpgradeCode="[|UpgradeCode]" VersionMin="[|ProductVersion]" Attributes="2" ActionProperty="AI_NEWERPRODUCTFOUND"/>
  </COMPONENT>
</DOCUMENT>
