<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OneVideo</class>
 <widget class="QFrame" name="OneVideo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>427</width>
    <height>240</height>
   </rect>
  </property>
  <property name="styleSheet">
   <string notr="true">QFrame { background: transparent; margin: 3px; }</string>
  </property>
  <property name="mouseTracking">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
    <!--  <item>
  <widget class="QLabel" name="nameLabel">
     <property name="font">
      <font>
       <family>Microsoft YaHei</family>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: white; background-color: rgba(0, 0, 0, 150); padding: 2px 8px; border-radius: 3px; border: 1px solid rgba(255, 255, 255, 100);</string>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>25</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>25</height>
      </size>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item> -->
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>