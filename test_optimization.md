# RTSP多视频流优化测试指南

## 测试前准备
1. 确保有至少3个可用的RTSP视频源
2. 记录测试前的系统资源使用情况（CPU、内存）
3. 准备计时工具来测量视频流启动时间

## 测试步骤

### 测试1：基础功能验证
1. 启动应用程序
2. 添加第一个RTSP视频流，记录启动时间和播放质量
3. 添加第二个RTSP视频流，记录启动时间和播放质量
4. 添加第三个RTSP视频流，重点观察：
   - 启动延迟时间（应该比前两个稍长）
   - 播放流畅度（应该比优化前明显改善）
   - 是否出现卡顿现象

### 测试2：资源使用验证
1. 同时播放3个视频流
2. 观察系统资源使用情况：
   - CPU使用率是否合理分配
   - 内存使用是否优化
   - 网络带宽使用是否均匀

### 测试3：稳定性测试
1. 长时间播放多个视频流（建议30分钟以上）
2. 观察是否出现：
   - 内存泄漏
   - 视频流断连
   - 画面花屏或卡顿

### 测试4：动态添加测试
1. 先播放1个视频流
2. 间隔30秒依次添加更多视频流
3. 观察每个新增视频流的启动表现

## 预期结果

### 优化前的问题
- 第三个视频流启动慢
- 播放卡顿严重
- CPU使用不均匀
- 可能出现花屏

### 优化后的改善
- 第三个视频流虽然有延迟启动，但播放流畅
- 系统资源使用更加均匀
- 减少花屏和卡顿现象
- 整体稳定性提升

## 关键观察点

### 1. 连接延迟
- 第一个视频流：立即连接
- 第二个视频流：500ms延迟
- 第三个视频流：1500ms延迟
- 第四个视频流：2500ms延迟

### 2. 线程优先级
- 前两个线程：正常优先级
- 第三个及以后：低优先级

### 3. 缓冲区调整
- 单线程：256KB缓冲区
- 多线程：128KB缓冲区

### 4. 帧处理策略
- 多线程时更激进的丢帧
- 智能保留关键帧

## 日志监控
在测试过程中，注意观察日志输出中的以下信息：
- "检测到多个视频流(X个)，调整缓冲区大小"
- "设置线程为低优先级，当前线程数: X"
- "检测到多个活跃视频流(X个)，延迟Xms后连接"
- "FFmpeg资源清理完成，剩余活跃线程: X"

## 故障排除

### 如果第三个视频流仍然卡顿
1. 检查网络带宽是否足够
2. 确认RTSP源的稳定性
3. 考虑进一步调整缓冲区参数
4. 检查系统硬件性能

### 如果出现连接失败
1. 检查RTSP URL的正确性
2. 确认网络连接稳定
3. 检查防火墙设置
4. 验证RTSP服务器的并发连接限制

## 性能调优建议

### 如果系统性能较低
- 减少MAX_FRAME_CACHE值
- 增加连接延迟时间
- 降低视频分辨率

### 如果系统性能较高
- 可以适当增加缓冲区大小
- 减少连接延迟时间
- 支持更多并发视频流

## 测试报告模板
```
测试时间：
系统配置：
测试的RTSP源数量：

测试结果：
1. 第一个视频流启动时间：___秒，播放质量：___
2. 第二个视频流启动时间：___秒，播放质量：___
3. 第三个视频流启动时间：___秒，播放质量：___

资源使用：
- 峰值CPU使用率：___%
- 峰值内存使用：___MB
- 网络带宽使用：___Mbps

稳定性：
- 连续播放时间：___分钟
- 是否出现断连：是/否
- 是否出现花屏：是/否

总体评价：
改善程度：显著/一般/无改善
```
