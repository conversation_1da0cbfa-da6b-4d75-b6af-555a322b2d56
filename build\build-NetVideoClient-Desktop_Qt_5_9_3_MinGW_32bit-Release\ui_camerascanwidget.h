/********************************************************************************
** Form generated from reading UI file 'camerascanwidget.ui'
**
** Created by: Qt User Interface Compiler version 5.9.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CAMERASCANWIDGET_H
#define UI_CAMERASCANWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_CameraScanWidget
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *titleLabel;
    QHBoxLayout *progressLayout;
    QLabel *statusLabel;
    QPushButton *rescanButton;
    QWidget *tableContainer;
    QHBoxLayout *tableLayout;
    QTableWidget *cameraTableWidget;

    void setupUi(QWidget *CameraScanWidget)
    {
        if (CameraScanWidget->objectName().isEmpty())
            CameraScanWidget->setObjectName(QStringLiteral("CameraScanWidget"));
        CameraScanWidget->resize(350, 600);
        CameraScanWidget->setStyleSheet(QStringLiteral("CameraScanWidget { background-image: url(:/images/list_bg.png); background-position: center; background-repeat: repeat; background-color: #001F3F; }"));
        verticalLayout = new QVBoxLayout(CameraScanWidget);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        titleLabel = new QLabel(CameraScanWidget);
        titleLabel->setObjectName(QStringLiteral("titleLabel"));
        titleLabel->setStyleSheet(QStringLiteral("QLabel { color: #FFFFFF; background-color: #003366; padding: 5px; font-weight: bold; font-size: 14px; }"));
        titleLabel->setAlignment(Qt::AlignCenter);
        titleLabel->setMinimumSize(QSize(0, 30));
        titleLabel->setMaximumSize(QSize(16777215, 30));

        verticalLayout->addWidget(titleLabel);

        progressLayout = new QHBoxLayout();
        progressLayout->setSpacing(10);
        progressLayout->setObjectName(QStringLiteral("progressLayout"));
        progressLayout->setContentsMargins(9, 5, 9, 5);
        statusLabel = new QLabel(CameraScanWidget);
        statusLabel->setObjectName(QStringLiteral("statusLabel"));
        statusLabel->setStyleSheet(QStringLiteral("QLabel { color: #00FFFF; font-weight: bold; padding: 3px; font-size: 12px; }"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
        sizePolicy.setHorizontalStretch(1);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(statusLabel->sizePolicy().hasHeightForWidth());
        statusLabel->setSizePolicy(sizePolicy);

        progressLayout->addWidget(statusLabel);

        rescanButton = new QPushButton(CameraScanWidget);
        rescanButton->setObjectName(QStringLiteral("rescanButton"));
        rescanButton->setMinimumSize(QSize(85, 0));
        rescanButton->setMaximumSize(QSize(85, 16777215));
        rescanButton->setStyleSheet(QLatin1String("QPushButton { background-color: #003366; color: #FFFFFF; border: 1px solid #00FFFF; border-radius: 3px; padding: 4px; font-size: 12px; font-weight: bold; } \n"
"QPushButton:hover { background-color: #004080; } \n"
"QPushButton:pressed { background-color: #002040; }"));

        progressLayout->addWidget(rescanButton);


        verticalLayout->addLayout(progressLayout);

        tableContainer = new QWidget(CameraScanWidget);
        tableContainer->setObjectName(QStringLiteral("tableContainer"));
        tableContainer->setStyleSheet(QStringLiteral("QWidget { background-color: transparent; }"));
        tableLayout = new QHBoxLayout(tableContainer);
        tableLayout->setSpacing(0);
        tableLayout->setObjectName(QStringLiteral("tableLayout"));
        tableLayout->setContentsMargins(9, 5, 9, 9);
        cameraTableWidget = new QTableWidget(tableContainer);
        cameraTableWidget->setObjectName(QStringLiteral("cameraTableWidget"));
        cameraTableWidget->setStyleSheet(QLatin1String("QTableWidget { border: none; background-color: rgba(0, 30, 60, 150); } \n"
"QHeaderView::section { background-color: #003366; color: #00FFFF; border: none; padding: 4px; font-weight: bold; font-size: 13px; } \n"
"QTableWidget::item { border-bottom: 1px solid rgba(0, 255, 255, 40); padding: 2px; color: white; font-size: 12px; background-color: transparent; }\n"
"QTableWidget::item:selected { background-color: rgba(0, 128, 255, 120); color: white; }\n"
"QTableWidget::item:focus { outline: none; }\n"
"QLineEdit { background-color: #001F3F; color: white; selection-background-color: #0078D7; selection-color: white; }"));
        cameraTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
        cameraTableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
        cameraTableWidget->setEditTriggers(QAbstractItemView::DoubleClicked|QAbstractItemView::EditKeyPressed|QAbstractItemView::SelectedClicked);
        cameraTableWidget->setContextMenuPolicy(Qt::CustomContextMenu);
        cameraTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
        cameraTableWidget->setAlternatingRowColors(false);
        cameraTableWidget->setShowGrid(false);
        cameraTableWidget->horizontalHeader()->setStretchLastSection(false);

        tableLayout->addWidget(cameraTableWidget);


        verticalLayout->addWidget(tableContainer);


        retranslateUi(CameraScanWidget);

        QMetaObject::connectSlotsByName(CameraScanWidget);
    } // setupUi

    void retranslateUi(QWidget *CameraScanWidget)
    {
        titleLabel->setText(QApplication::translate("CameraScanWidget", "\346\221\204\345\203\217\345\244\264\351\205\215\347\275\256", Q_NULLPTR));
        statusLabel->setText(QApplication::translate("CameraScanWidget", "\345\207\206\345\244\207\346\211\253\346\217\217...", Q_NULLPTR));
        rescanButton->setText(QApplication::translate("CameraScanWidget", "\351\207\215\346\226\260\346\211\253\346\217\217", Q_NULLPTR));
#ifndef QT_NO_TOOLTIP
        rescanButton->setToolTip(QApplication::translate("CameraScanWidget", "\351\207\215\346\226\260\346\211\253\346\217\217\347\275\221\347\273\234\344\270\255\347\232\204\346\221\204\345\203\217\345\244\264", Q_NULLPTR));
#endif // QT_NO_TOOLTIP
        Q_UNUSED(CameraScanWidget);
    } // retranslateUi

};

namespace Ui {
    class CameraScanWidget: public Ui_CameraScanWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CAMERASCANWIDGET_H
