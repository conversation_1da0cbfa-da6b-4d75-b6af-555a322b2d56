﻿#pragma once


#ifdef DLL_API_EXPORTS
#define DLL_API __declspec(dllexport)
#else
#define DLL_API __declspec(dllimport)
#endif


#define RET_PassValidation				 1		//	验证通过
#define RET_FailedValidation			-1		//	Failed validation	--- 验证不通过
#define RET_Failed_NotCompareProd		-2	    // The current authorized key is inconsistent with the corresponding product  --- 当前授权 KEY 与对应的产品不一致
#define RET_DateExpire                  -3
#define RET_Failed_ThreadCreate         -4      //线程创建失败--硬件key
#define RET_ThreadIsRunning             -5		//线程正在运行--硬件key
#define RET_DBOper_Failed               -6      //数据库操作失败
typedef void(*ResultCALLBACK)(int, void*);
typedef struct stParam
{
	ResultCALLBACK pCallBack ;   //回调函数
	void          *param ;       //透传参数
}ParamCustom;

/******************************************************
* 硬件key验证
* 参数1 ：sProderialNum Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 返回值 ：整型 操作码
	      成功：RET_PassValidation
		  其他为失败

******************************************************/

extern "C" DLL_API  int UdiskVerification(const char *strProderialNum);
/******************************************************
* 检查成功后才启动定时检查
* 参数1 ：sProderialNum Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：nSec 扫描间隔时间 单位秒
* 参数3 ：startScan 是否启动扫描线程
* 参数4 ：customParam 指针，
* 返回值 ：整型 操作码
		成功:RET_PassValidation
		其他为失败
*******************************************************/
extern "C" DLL_API  int UdiskVerificationAndScan(const char *sProderialNum, int nSec = 60, bool startScan = false, ParamCustom* customParam = NULL);
//
/******************************************************
* 关闭定时检查线程
* 参数：无
* 返回值 ：无
******************************************************/
extern "C" DLL_API  void CloseScan();

/******************************************************
* 软件key验证
* 参数1 ：strSoftID Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：strUtf8RegisterCode Utf8字符串 待验证的授权码
* 返回值 ：整型 操作码
成功：RET_PassValidation
其他为失败
******************************************************/
extern "C"  int DLL_API CheckSoftKey(const char* strSoftID, const char* strUtf8RegisterCode);

/******************************************************
* 软件key验证
* 参数1 ：strSoftID Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：strDBPath Utf8字符串  sqliteDB数据库
* 返回值 ：整型 操作码
成功：RET_PassValidation
其他为失败
******************************************************/
extern "C"  int DLL_API CheckSoftKeyFromDB(const char* strSoftID, const char* strDBPath);

/******************************************************
* 获取当前电脑机器码

******************************************************/
extern "C" int  DLL_API  GetSotfMachineCode(char* buf, int bufLen);

/******************************************************
* 软件key验证 成功后将授权码写入到指定的db文件中
* 参数1 ：strSoftID Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：strRegisterCode Utf8字符串 待验证的授权码
* 参数3 ：strDBPath  Utf8字符串 sqlite数据库路径
* 返回值 ：整型 操作码
		成功:RET_PassValidation
		其他为失败
*******************************************************/
extern "C"  int  DLL_API CheckSoftKeyToDB(const char* strSoftID, const char* strRegisterCode, const char* strDBPath);

/******************************************************
* 清除授权码，从指定的db文件中清除授权码
* 参数1 ：strSoftID Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：strDBPath Utf8字符串 sqlite数据库路径
* 返回值 ：整型 操作码
成功:RET_PassValidation
其他为失败
*******************************************************/
extern "C"  int  DLL_API UnSoftKey(const char* strSoftID, const char* strDBPath);
/******************************************************
* 验证软件授权码，成功则返回，不成功弹出软件授权码输入框，再次验证，成功后将授权码写入指定的db文件中
* 参数1 ：strSoftID Utf8字符串，软件标识编号 ZF-Sxxxxx;
* 参数2 ：strDBPath Utf8字符串 sqlite数据库路径
* 返回值 ：整型 操作码
成功:RET_PassValidation
其他为失败
*******************************************************/
extern "C"  DLL_API int CheckSoftKeyWithDlg(const char*strSoftID, const char* sDBPath);
//extern "C" DLL_API  void CheckSoftKey(const char*sProderialNum,const char* sDBPath);
