#define __DBL_MIN_EXP__ (-1021)
#define __cpp_attributes 200809
#define __pentiumpro__ 1
#define __UINT_LEAST16_MAX__ 0xffff
#define __ATOMIC_ACQUIRE 2
#define __FLT_MIN__ 1.17549435082228750797e-38F
#define __GCC_IEC_559_COMPLEX 2
#define __UINT_LEAST8_TYPE__ unsigned char
#define __SIZEOF_FLOAT80__ 12
#define _WIN32 1
#define __INTMAX_C(c) c ## LL
#define __CHAR_BIT__ 8
#define __UINT8_MAX__ 0xff
#define __WINT_MAX__ 0xffff
#define __cpp_static_assert 200410
#define __ORDER_LITTLE_ENDIAN__ 1234
#define __SIZE_MAX__ 0xffffffffU
#define __WCHAR_MAX__ 0xffff
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4 1
#define __DBL_DENORM_MIN__ double(4.94065645841246544177e-324L)
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8 1
#define __GCC_ATOMIC_CHAR_LOCK_FREE 2
#define __GCC_IEC_559 2
#define __FLT_EVAL_METHOD__ 2
#define __cpp_binary_literals 201304
#define __GCC_ATOMIC_CHAR32_T_LOCK_FREE 2
#define __cpp_variadic_templates 200704
#define __UINT_FAST64_MAX__ 0xffffffffffffffffULL
#define __SIG_ATOMIC_TYPE__ int
#define __DBL_MIN_10_EXP__ (-307)
#define __FINITE_MATH_ONLY__ 0
#define __GNUC_PATCHLEVEL__ 0
#define __UINT_FAST8_MAX__ 0xff
#define __has_include(STR) __has_include__(STR)
#define _stdcall __attribute__((__stdcall__))
#define __DEC64_MAX_EXP__ 385
#define __INT8_C(c) c
#define __UINT_LEAST64_MAX__ 0xffffffffffffffffULL
#define __SHRT_MAX__ 0x7fff
#define __LDBL_MAX__ 1.18973149535723176502e+4932L
#define __UINT_LEAST8_MAX__ 0xff
#define __GCC_ATOMIC_BOOL_LOCK_FREE 2
#define __UINTMAX_TYPE__ long long unsigned int
#define __DEC32_EPSILON__ 1E-6DF
#define __UINT32_MAX__ 0xffffffffU
#define __GXX_EXPERIMENTAL_CXX0X__ 1
#define __LDBL_MAX_EXP__ 16384
#define __WINT_MIN__ 0
#define __SCHAR_MAX__ 0x7f
#define __WCHAR_MIN__ 0
#define __INT64_C(c) c ## LL
#define __DBL_DIG__ 15
#define __GCC_ATOMIC_POINTER_LOCK_FREE 2
#define __SIZEOF_INT__ 4
#define __SIZEOF_POINTER__ 4
#define __GCC_ATOMIC_CHAR16_T_LOCK_FREE 2
#define __USER_LABEL_PREFIX__ _
#define __STDC_HOSTED__ 1
#define __WIN32 1
#define __LDBL_HAS_INFINITY__ 1
#define __FLT_EPSILON__ 1.19209289550781250000e-7F
#define __GXX_WEAK__ 1
#define __LDBL_MIN__ 3.36210314311209350626e-4932L
#define __DEC32_MAX__ 9.999999E96DF
#define __MINGW32__ 1
#define __INT32_MAX__ 0x7fffffff
#define __SIZEOF_LONG__ 4
#define __UINT16_C(c) c
#define __DECIMAL_DIG__ 21
#define __has_include_next(STR) __has_include_next__(STR)
#define __LDBL_HAS_QUIET_NAN__ 1
#define _REENTRANT 1
#define __GNUC__ 5
#define _cdecl __attribute__((__cdecl__))
#define __GXX_RTTI 1
#define __cpp_delegating_constructors 200604
#define __FLT_HAS_DENORM__ 1
#define __SIZEOF_LONG_DOUBLE__ 12
#define __BIGGEST_ALIGNMENT__ 16
#define __STDC_UTF_16__ 1
#define __i686 1
#define __DBL_MAX__ double(1.79769313486231570815e+308L)
#define _thiscall __attribute__((__thiscall__))
#define __cpp_raw_strings 200710
#define __INT_FAST32_MAX__ 0x7fffffff
#define __WINNT 1
#define __DBL_HAS_INFINITY__ 1
#define __INT64_MAX__ 0x7fffffffffffffffLL
#define __WINNT__ 1
#define __DEC32_MIN_EXP__ (-94)
#define __INT_FAST16_TYPE__ short int
#define _fastcall __attribute__((__fastcall__))
#define __LDBL_HAS_DENORM__ 1
#define __cplusplus 201103L
#define __cpp_ref_qualifiers 200710
#define __DEC128_MAX__ 9.999999999999999999999999999999999E6144DL
#define __INT_LEAST32_MAX__ 0x7fffffff
#define __DEC32_MIN__ 1E-95DF
#define __DEPRECATED 1
#define __DBL_MAX_EXP__ 1024
#define __DEC128_EPSILON__ 1E-33DL
#define __ATOMIC_HLE_RELEASE 131072
#define __WIN32__ 1
#define __PTRDIFF_MAX__ 0x7fffffff
#define __ATOMIC_HLE_ACQUIRE 65536
#define __GNUG__ 5
#define __LONG_LONG_MAX__ 0x7fffffffffffffffLL
#define __SIZEOF_SIZE_T__ 4
#define __cpp_rvalue_reference 200610
#define __cpp_nsdmi 200809
#define __SIZEOF_WINT_T__ 2
#define __cpp_initializer_lists 200806
#define __GCC_HAVE_DWARF2_CFI_ASM 1
#define __GXX_ABI_VERSION 1009
#define __FLT_MIN_EXP__ (-125)
#define __i686__ 1
#define __cpp_lambdas 200907
#define __INT_FAST64_TYPE__ long long int
#define __DBL_MIN__ double(2.22507385850720138309e-308L)
#define __FLT_MIN_10_EXP__ (-37)
#define __DECIMAL_BID_FORMAT__ 1
#define __GXX_TYPEINFO_EQUALITY_INLINE 0
#define __DEC128_MIN__ 1E-6143DL
#define __REGISTER_PREFIX__ 
#define __UINT16_MAX__ 0xffff
#define __DBL_HAS_DENORM__ 1
#define __cdecl __attribute__((__cdecl__))
#define __UINT8_TYPE__ unsigned char
#define __NO_INLINE__ 1
#define __i386 1
#define __FLT_MANT_DIG__ 24
#define __VERSION__ "5.3.0"
#define __UINT64_C(c) c ## ULL
#define __cpp_unicode_characters 200704
#define __GCC_ATOMIC_INT_LOCK_FREE 2
#define _X86_ 1
#define __FLOAT_WORD_ORDER__ __ORDER_LITTLE_ENDIAN__
#define __INT32_C(c) c
#define __DEC64_EPSILON__ 1E-15DD
#define __ORDER_PDP_ENDIAN__ 3412
#define __DEC128_MIN_EXP__ (-6142)
#define __code_model_32__ 1
#define __INT_FAST32_TYPE__ int
#define __UINT_LEAST16_TYPE__ short unsigned int
#define __INT16_MAX__ 0x7fff
#define __i386__ 1
#define __cpp_rtti 199711
#define __SIZE_TYPE__ unsigned int
#define __UINT64_MAX__ 0xffffffffffffffffULL
#define __INT8_TYPE__ signed char
#define __FLT_RADIX__ 2
#define __INT_LEAST16_TYPE__ short int
#define __LDBL_EPSILON__ 1.08420217248550443401e-19L
#define __UINTMAX_C(c) c ## ULL
#define __SIG_ATOMIC_MAX__ 0x7fffffff
#define __GCC_ATOMIC_WCHAR_T_LOCK_FREE 2
#define __SIZEOF_PTRDIFF_T__ 4
#define __DEC32_SUBNORMAL_MIN__ 0.000001E-95DF
#define __pentiumpro 1
#define __MSVCRT__ 1
#define __INT_FAST16_MAX__ 0x7fff
#define __UINT_FAST32_MAX__ 0xffffffffU
#define __UINT_LEAST64_TYPE__ long long unsigned int
#define __FLT_HAS_QUIET_NAN__ 1
#define __FLT_MAX_10_EXP__ 38
#define __LONG_MAX__ 0x7fffffffL
#define __DEC128_SUBNORMAL_MIN__ 0.000000000000000000000000000000001E-6143DL
#define __FLT_HAS_INFINITY__ 1
#define __cpp_unicode_literals 200710
#define __UINT_FAST16_TYPE__ short unsigned int
#define __DEC64_MAX__ 9.999999999999999E384DD
#define __CHAR16_TYPE__ short unsigned int
#define __PRAGMA_REDEFINE_EXTNAME 1
#define __INT_LEAST16_MAX__ 0x7fff
#define __DEC64_MANT_DIG__ 16
#define __UINT_LEAST32_MAX__ 0xffffffffU
#define __GCC_ATOMIC_LONG_LOCK_FREE 2
#define __INT_LEAST64_TYPE__ long long int
#define __INT16_TYPE__ short int
#define __INT_LEAST8_TYPE__ signed char
#define __DEC32_MAX_EXP__ 97
#define __INT_FAST8_MAX__ 0x7f
#define __INTPTR_MAX__ 0x7fffffff
#define __GXX_MERGED_TYPEINFO_NAMES 0
#define __cpp_range_based_for 200907
#define __stdcall __attribute__((__stdcall__))
#define __EXCEPTIONS 1
#define __LDBL_MANT_DIG__ 64
#define __DBL_HAS_QUIET_NAN__ 1
#define __SIG_ATOMIC_MIN__ (-__SIG_ATOMIC_MAX__ - 1)
#define __INTPTR_TYPE__ int
#define __UINT16_TYPE__ short unsigned int
#define __WCHAR_TYPE__ short unsigned int
#define __SIZEOF_FLOAT__ 4
#define __UINTPTR_MAX__ 0xffffffffU
#define __DEC64_MIN_EXP__ (-382)
#define __cpp_decltype 200707
#define __INT_FAST64_MAX__ 0x7fffffffffffffffLL
#define __GCC_ATOMIC_TEST_AND_SET_TRUEVAL 1
#define __FLT_DIG__ 6
#define __UINT_FAST64_TYPE__ long long unsigned int
#define __INT_MAX__ 0x7fffffff
#define WIN32 1
#define __INT64_TYPE__ long long int
#define __FLT_MAX_EXP__ 128
#define __DBL_MANT_DIG__ 53
#define __cpp_inheriting_constructors 200802
#define __SIZEOF_FLOAT128__ 16
#define __INT_LEAST64_MAX__ 0x7fffffffffffffffLL
#define __DEC64_MIN__ 1E-383DD
#define __WINT_TYPE__ short unsigned int
#define __UINT_LEAST32_TYPE__ unsigned int
#define __SIZEOF_SHORT__ 2
#define __LDBL_MIN_EXP__ (-16381)
#define __INT_LEAST8_MAX__ 0x7f
#define __WCHAR_UNSIGNED__ 1
#define __LDBL_MAX_10_EXP__ 4932
#define __ATOMIC_RELAXED 0
#define __DBL_EPSILON__ double(2.22044604925031308085e-16L)
#define __thiscall __attribute__((__thiscall__))
#define __UINT8_C(c) c
#define __INT_LEAST32_TYPE__ int
#define __SIZEOF_WCHAR_T__ 2
#define __UINT64_TYPE__ long long unsigned int
#define __INT_FAST8_TYPE__ signed char
#define __fastcall __attribute__((__fastcall__))
#define __GNUC_STDC_INLINE__ 1
#define __DBL_DECIMAL_DIG__ 17
#define __STDC_UTF_32__ 1
#define __DEC_EVAL_METHOD__ 2
#define __ORDER_BIG_ENDIAN__ 4321
#define __cpp_runtime_arrays 198712
#define __UINT32_C(c) c ## U
#define __INTMAX_MAX__ 0x7fffffffffffffffLL
#define __cpp_alias_templates 200704
#define __BYTE_ORDER__ __ORDER_LITTLE_ENDIAN__
#define WINNT 1
#define __FLT_DENORM_MIN__ 1.40129846432481707092e-45F
#define __INT8_MAX__ 0x7f
#define __UINT_FAST32_TYPE__ unsigned int
#define __CHAR32_TYPE__ unsigned int
#define __FLT_MAX__ 3.40282346638528859812e+38F
#define __cpp_constexpr 200704
#define __INT32_TYPE__ int
#define __SIZEOF_DOUBLE__ 8
#define __cpp_exceptions 199711
#define __INTMAX_TYPE__ long long int
#define i386 1
#define _INTEGRAL_MAX_BITS 64
#define __DEC128_MAX_EXP__ 6145
#define __ATOMIC_CONSUME 1
#define __GNUC_MINOR__ 3
#define __UINTMAX_MAX__ 0xffffffffffffffffULL
#define __DEC32_MANT_DIG__ 7
#define __DBL_MAX_10_EXP__ 308
#define __LDBL_DENORM_MIN__ 3.64519953188247460253e-4951L
#define __INT16_C(c) c
#define __STDC__ 1
#define __PTRDIFF_TYPE__ int
#define __ATOMIC_SEQ_CST 5
#define __UINT32_TYPE__ unsigned int
#define __UINTPTR_TYPE__ unsigned int
#define __DEC64_SUBNORMAL_MIN__ 0.000000000000001E-383DD
#define __DEC128_MANT_DIG__ 34
#define __LDBL_MIN_10_EXP__ (-4931)
#define __SIZEOF_LONG_LONG__ 8
#define __cpp_user_defined_literals 200809
#define __GCC_ATOMIC_LLONG_LOCK_FREE 2
#define __LDBL_DIG__ 18
#define __FLT_DECIMAL_DIG__ 9
#define __UINT_FAST16_MAX__ 0xffff
#define __GCC_ATOMIC_SHORT_LOCK_FREE 2
#define __UINT_FAST8_TYPE__ unsigned char
#define __ATOMIC_ACQ_REL 4
#define __ATOMIC_RELEASE 3
#define __declspec(x) __attribute__((x))
