@echo off
echo 正在测试编译...

REM 检查是否存在qmake
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到qmake，请确保Qt环境已正确配置
    pause
    exit /b 1
)

REM 创建临时构建目录
if not exist "temp_build" mkdir temp_build
cd temp_build

REM 运行qmake
echo 运行qmake...
qmake ..\NetVideoClient.pro
if %errorlevel% neq 0 (
    echo 错误: qmake失败
    cd ..
    pause
    exit /b 1
)

REM 尝试编译
echo 开始编译...
make
if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
) else (
    echo 编译成功！
)

cd ..
echo 测试完成
pause
