<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>0</number>
    </property>
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QWidget" name="bannerWidget" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>40</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>40</height>
       </size>
      </property>
      <layout class="QHBoxLayout" name="bannerLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="titleLabel">
         <property name="text">
          <string>摄像头监控系统</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QSplitter" name="mainSplitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <widget class="QWidget" name="leftWidget" native="true">
       <property name="minimumSize">
        <size>
         <width>350</width>
         <height>0</height>
        </size>
       </property>
      </widget>
      <widget class="QWidget" name="centerWidget" native="true">
       <layout class="QVBoxLayout" name="centerLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="monitorTitleLabel">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="text">
           <string>监控画面</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="displayModeBar" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>50</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="modeLayout">
           <property name="spacing">
            <number>5</number>
           </property>
           <property name="leftMargin">
            <number>5</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="mainContent" native="true">
          <property name="styleSheet">
           <string notr="true">background: transparent;</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="rightWidget" native="true">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>16777215</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="rightLayout">
        <property name="spacing">
         <number>15</number>
        </property>
        <property name="leftMargin">
         <number>15</number>
        </property>
        <property name="topMargin">
         <number>30</number>
        </property>
        <property name="rightMargin">
         <number>15</number>
        </property>
        <property name="bottomMargin">
         <number>30</number>
        </property>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="eavesdropButton">
          <property name="minimumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="toolTip">
           <string>窃视功能</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton { 
    background-color: #003366; 
    color: white; 
    border: 2px solid #00FFFF; 
    border-radius: 10px; 
    font-weight: bold; 
    font-size: 16px; 
} 
QPushButton:hover { 
    background-color: #004080; 
    border-color: #00FFFF; 
} 
QPushButton:pressed { 
    background-color: #002040; 
}</string>
          </property>
          <property name="text">
           <string>窃视</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="listenButton">
          <property name="minimumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="toolTip">
           <string>窃听功能</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton { 
    background-color: #003366; 
    color: white; 
    border: 2px solid #00FFFF; 
    border-radius: 10px; 
    font-weight: bold; 
    font-size: 16px; 
} 
QPushButton:hover { 
    background-color: #004080; 
    border-color: #00FFFF; 
} 
QPushButton:pressed { 
    background-color: #002040; 
}</string>
          </property>
          <property name="text">
           <string>窃听</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="screenshotButton">
          <property name="minimumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="toolTip">
           <string>截屏功能</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton { 
    background-color: #003366; 
    color: white; 
    border: 2px solid #00FFFF; 
    border-radius: 10px; 
    font-weight: bold; 
    font-size: 16px; 
} 
QPushButton:hover { 
    background-color: #004080; 
    border-color: #00FFFF; 
} 
QPushButton:pressed { 
    background-color: #002040; 
}</string>
          </property>
          <property name="text">
           <string>截屏</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="recordButton">
          <property name="minimumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>170</width>
            <height>80</height>
           </size>
          </property>
          <property name="toolTip">
           <string>录像功能</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton { 
    background-color: #003366; 
    color: white; 
    border: 2px solid #00FFFF; 
    border-radius: 10px; 
    font-weight: bold; 
    font-size: 16px; 
} 
QPushButton:hover { 
    background-color: #004080; 
    border-color: #00FFFF; 
} 
QPushButton:pressed { 
    background-color: #002040; 
}</string>
          </property>
          <property name="text">
           <string>录像</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
