# 基于QT的网络视频监控系统

## 系统优化记录

### 2023年优化：摄像头响应速度优化

为了解决勾选摄像头后需要等待较长时间才能显示画面的问题，我们进行了以下优化：

1. 减少了CameraScanWidget中`onOperationButtonClicked`方法的延迟处理时间：
   - 将处理延迟从500毫秒减少到100毫秒

2. 减少了OneVideo中`setRtspUrl`方法的连接延迟时间：
   - 将连接延迟从2000毫秒减少到500毫秒

3. 减少了MyThread中RTSP连接的超时时间：
   - `handleRtspWithMediaPlayer`方法中的超时从10秒减少到5秒
   - `handleRtspConnection`方法中的连接超时从5秒减少到3秒
   - 读取和写入超时从3秒减少到2秒

4. 优化了配置管理器中的默认超时设置：
   - RTSP超时从1000毫秒减少到800毫秒
   - HTTP超时从1000毫秒减少到800毫秒

这些优化使摄像头画面显示的响应时间大幅缩短，提升了用户体验。

## 使用说明

1. 左侧面板显示可用的摄像头列表
2. 勾选摄像头复选框可显示对应摄像头画面
3. 右侧上方可切换显示模式（单画面、四画面、九画面）
4. 摄像头画面上有工具栏可进行截图、录制等操作

## 系统要求

- Windows 7/10/11
- Qt 5.9.3或更高版本
- 网络摄像头（支持RTSP协议）