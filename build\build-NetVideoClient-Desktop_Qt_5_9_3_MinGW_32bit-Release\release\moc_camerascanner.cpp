/****************************************************************************
** Meta object code from reading C++ file 'camerascanner.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../camerascanner.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'camerascanner.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CameraScanner_t {
    QByteArrayData data[9];
    char stringdata0[86];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CameraScanner_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CameraScanner_t qt_meta_stringdata_CameraScanner = {
    {
QT_MOC_LITERAL(0, 0, 13), // "CameraScanner"
QT_MOC_LITERAL(1, 14, 12), // "scanProgress"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 7), // "current"
QT_MOC_LITERAL(4, 36, 5), // "total"
QT_MOC_LITERAL(5, 42, 11), // "cameraFound"
QT_MOC_LITERAL(6, 54, 10), // "CameraInfo"
QT_MOC_LITERAL(7, 65, 6), // "camera"
QT_MOC_LITERAL(8, 72, 13) // "scanCompleted"

    },
    "CameraScanner\0scanProgress\0\0current\0"
    "total\0cameraFound\0CameraInfo\0camera\0"
    "scanCompleted"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CameraScanner[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       3,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   29,    2, 0x06 /* Public */,
       5,    1,   34,    2, 0x06 /* Public */,
       8,    0,   37,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::Int,    3,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void,

       0        // eod
};

void CameraScanner::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        CameraScanner *_t = static_cast<CameraScanner *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->scanProgress((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 1: _t->cameraFound((*reinterpret_cast< const CameraInfo(*)>(_a[1]))); break;
        case 2: _t->scanCompleted(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            typedef void (CameraScanner::*_t)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanner::scanProgress)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (CameraScanner::*_t)(const CameraInfo & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanner::cameraFound)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (CameraScanner::*_t)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CameraScanner::scanCompleted)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject CameraScanner::staticMetaObject = {
    { &QThread::staticMetaObject, qt_meta_stringdata_CameraScanner.data,
      qt_meta_data_CameraScanner,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *CameraScanner::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CameraScanner::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CameraScanner.stringdata0))
        return static_cast<void*>(this);
    return QThread::qt_metacast(_clname);
}

int CameraScanner::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QThread::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 3)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 3;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 3)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 3;
    }
    return _id;
}

// SIGNAL 0
void CameraScanner::scanProgress(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CameraScanner::cameraFound(const CameraInfo & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CameraScanner::scanCompleted()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
