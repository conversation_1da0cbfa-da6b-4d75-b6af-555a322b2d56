#ifndef CAMERASCANNER_H
#define CAMERASCANNER_H

#include <QObject>
#include <QList>
#include <QString>
#include <QThread>
#include <QMutex>

// 摄像头信息结构
struct CameraInfo
{
    QString rtspUrl;
    QString ipAddress;
    bool isValid;
    bool hasHttpImage; // 是否有HTTP图像
    QString name;      // 摄像头名称
    bool isChecked;    // 摄像头勾选状态

    CameraInfo(const QString &url = "", const QString &ip = "", bool valid = false, bool httpImage = false)
        : rtspUrl(url), ipAddress(ip), isValid(valid), hasHttpImage(httpImage), name(""), isChecked(false) {}
};

// 摄像头扫描线程类
class CameraScanner : public QThread
{
    Q_OBJECT
public:
    explicit CameraScanner(QObject *parent = nullptr);
    ~CameraScanner();

    // 开始扫描，使用配置文件中的设置
    void startScan();

    // 停止扫描
    void stopScan();

    // 获取扫描结果
    QList<CameraInfo> getResults() const;

    // 检查是否由用户手动停止
    bool isUserStopped() const { return m_isUserStopped; }

signals:
    // 扫描进度更新
    void scanProgress(int current, int total);

    // 发现有效摄像头
    void cameraFound(const CameraInfo &camera);

    // 扫描完成
    void scanCompleted();

protected:
    void run() override;

private:
    bool m_isRunning;
    bool m_isUserStopped; // 是否由用户手动停止
    mutable QMutex m_mutex;
    QList<CameraInfo> m_results;
};

#endif // CAMERASCANNER_H